import { ApiUnitSchema, ApiUnit } from '../../src/types/schemas/unit';
import mockLocationsResponse from '../mockLocationsResponse.json';
import Adapter from '../../src/adapters/Adapter';
import { UnitAdapter } from '../../src/adapters/Units/UnitAdapter';

describe('UnitAdapter should properlly adapt API response to FE', () => {
  let apiUnit: ApiUnit;

  beforeAll(() => {
    apiUnit = mockLocationsResponse.Locations[0].Units[0];
  });

  test('Adapt API response to FE type', () => {
    const parsedUnit = ApiUnitSchema.parse(apiUnit);
    const adaptedUnit = Adapter
      .from(parsedUnit)
      .to((item) => new UnitAdapter(item).adapt());
    expect(adaptedUnit).not.toBeNull;
  });
});
