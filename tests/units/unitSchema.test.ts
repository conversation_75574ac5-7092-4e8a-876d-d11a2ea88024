import { ApiUnitSchema, ApiUnit } from '../../src/types/schemas/unit';
import mockLocationsResponse from '../mockLocationsResponse.json';

describe('ApiUnitSchema should parse correctly', () => {
  let apiUnit: ApiUnit;

  beforeAll(() => {
    apiUnit = mockLocationsResponse.Locations[0].Units[0];
  });

  test('Parse unit from API response', () => {
    const parsedUnit = ApiUnitSchema.parse(apiUnit);
    expect(parsedUnit).not.toBeNull;
  });
});
