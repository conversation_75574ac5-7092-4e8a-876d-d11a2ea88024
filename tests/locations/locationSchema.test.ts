import { ApiLocationSchema, ApiLocation } from '../../src/types/schemas/location';
import mockLocationsResponse from '../mockLocationsResponse.json';

describe('ApiUnitSchema should parse correctly', () => {
  let apiLocation: ApiLocation;

  beforeAll(() => {
    apiLocation = mockLocationsResponse.Locations[0];
  });

  test('Parse unit from API response', () => {
    const parsed = ApiLocationSchema.parse(apiLocation);
    expect(parsed).not.toBeNull;
  });
});
