import { ApiLocationSchema, ApiLocation } from '../../src/types/schemas/location';
import mockLocationsResponse from '../mockLocationsResponse.json';
import Adapter from '../../src/adapters/Adapter';
import { LocationAdapter } from '../../src/adapters/Locations/LocationAdapter';

describe('UnitAdapter should properlly adapt API response to FE', () => {
  let apiUnit: ApiLocation;

  beforeAll(() => {
    apiUnit = mockLocationsResponse.Locations[0];
  });

  test('Adapt API response to FE type', () => {
    const parsed = ApiLocationSchema.parse(apiUnit);
    const adapted = Adapter
      .from(parsed)
      .to((item) => new LocationAdapter(item).adapt());
    console.log(adapted);
    expect(adapted).not.toBeNull;
  });
});
