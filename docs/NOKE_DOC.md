

Noke API Gateway

Last Updated: 9/26/2024

The API Gateway allows 3rd party servers to hit authorized endpoints within the associated company. This is used when creating a custom 3rd-party app with separate authentication and connecting to Noke Smart Entry devices to unlock and view activity.

⸻

How it Works
	1.	A tenant logs into the 3rd-party app.
	2.	The app authenticates with the 3rd-party server using separate authentication.
	3.	The 3rd-party server asks the SmartEntry API Gateway for locks and commands the client has access to. The API Gateway passes these to the app.
	4.	The 3rd-party app uses the SmartEntry Mobile Library to send commands to the lock.
	5.	The lock responds to the command and sends activity logs to SmartEntry through wireless mesh for processing.

⸻

Required Fields for Validation

Key	Value
Authorization	ApiKey COMPANY_API_GATEWAY_KEY
companyId	COMPANY_ID_FROM_NOKE
siteId	3RD_PARTY_SITE_IDENTIFIER
phone	TENANT_PHONE

API URL: https://api.smartentry.noke.com/ENDPOINT/

Note: The ApiKey and companyId are provided by Noke during integration. The siteId is either the Noke Site Identifier or provided by the 3rd party.

⸻

Server-to-Server Authentication (cURL Example)

curl --location --globoff 'https://api.smartentry.noke.com/{{ENDPOINT}}/' \
--header 'Content-Type: application/json' \
--header 'Authorization: ApiKey {{COMPANY_API_GATEWAY_KEY}}' \
--header 'companyId: {{COMPANY_ID_FROM_NOKE}}' \
--header 'email: {{ApiGatewayUserEmail}}' \
--header 'siteId: {{ApiGatewaySiteId}}' \
--header 'phone: {{ApiGatewayUserPhone}}' \
--data '{}'


⸻

Commonly Used Endpoints

user/units/

Returns a list of the units and locks a user has access to.
	•	Tenant: Shows rented/assigned units and corresponding entries in the same zone.
	•	Employee: Returns all units/locks at the site (but only has commands for unoccupied units).

Offline commands expire at midnight.

lock/unlock/

Allows online unlock by sending a session and MAC address to the server to generate an encrypted one-time unlock command.

curl --location --globoff 'https://api.smartentry.noke.com/lock/unlock/' \
--header 'Content-Type: application/json' \
--header 'Authorization: ApiKey {{COMPANY_API_GATEWAY_KEY}}' \
--header 'companyId: {{COMPANY_ID_FROM_NOKE}}' \
--header 'email: {{ApiGatewayUserEmail}}' \
--header 'siteId: {{ApiGatewaySiteId}}' \
--header 'phone: {{ApiGatewayUserPhone}}' \
--data '{
  "session": "{{40CHAR_STRING_FROM_LOCK}}",
  "mac": "{{XX:XX:XX:XX:XX:XX}}"
}'


⸻

Tenant-Only Endpoint: user/onsite/

Used when tenants enter or exit the facility, triggered by geofencing.

curl --location --globoff 'https://api.smartentry.noke.com/user/onsite/' \
--header 'Content-Type: application/json' \
--header 'Authorization: ApiKey {{COMPANY_API_GATEWAY_KEY}}' \
--header 'companyId: {{COMPANY_ID_FROM_NOKE}}' \
--header 'email: {{ApiGatewayUserEmail}}' \
--header 'siteId: {{ApiGatewaySiteId}}' \
--header 'phone: {{ApiGatewayUserPhone}}' \
--data '{
  "onSite": true
}'


⸻

Employee-Only Endpoint: lock/unlock/mesh/

Allows remote unlocking of entries or unoccupied units.

curl --location --globoff 'https://api.smartentry.noke.com/lock/unlock/mesh/' \
--header 'Content-Type: application/json' \
--header 'Authorization: ApiKey {{COMPANY_API_GATEWAY_KEY}}' \
--header 'companyId: {{COMPANY_ID_FROM_NOKE}}' \
--header 'email: {{ApiGatewayUserEmail}}' \
--header 'siteId: {{ApiGatewaySiteId}}' \
--header 'phone: {{ApiGatewayUserPhone}}' \
--data '{
  "mac": "{{XX:XX:XX:XX:XX:XX}}"
}'


⸻

Lock Locate Endpoints

Used to beep and flash a lock for easy identification.

self/lock/locate/ – Tenant Only

Available for locks a tenant is renting/assigned.

lock/locate/ – Employee Only

Can be used on any lock at the facility.

curl --location --globoff 'https://api.smartentry.noke.com/self/lock/locate/' \
--header 'Content-Type: application/json' \
--header 'Authorization: ApiKey {{COMPANY_API_GATEWAY_KEY}}' \
--header 'companyId: {{COMPANY_ID_FROM_NOKE}}' \
--header 'email: {{ApiGatewayUserEmail}}' \
--header 'siteId: {{ApiGatewaySiteId}}' \
--header 'phone: {{ApiGatewayUserPhone}}' \
--data '{
  "id": 123
}'
