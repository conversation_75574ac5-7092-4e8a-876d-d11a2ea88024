name: Build and Distribute Staging App via Firebase

on:
  push:
    branches:
      - develop
      - release/*
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - '**'
  workflow_dispatch:
    inputs:
      git-ref:
        description: Git Ref (Optional)
        required: false
jobs:
  build-android:
    runs-on: ubuntu-latest
    environment: stage
    steps:
      - name: Checkout the Repo (Latest)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref == ''

      - name: Checkout the Repo (Custom Ref)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref != ''
        with:
          ref: ${{ github.event.inputs.git-ref }}

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17
          cache: gradle

      - name: Install dependencies
        uses: ./.github/actions/install-dependencies

      - name: Set Environment
        run: |
          echo API_URL=${{ vars.SMARTSTOP_API_URL }} >> ${{ github.workspace }}/.env.staging;
          echo NOKE_MODE=${{ vars.SMARTSTOP_NOKE_MODE }} >> ${{ github.workspace }}/.env.staging;
          echo ENVIRONMENT=staging >> ${{ github.workspace }}/.env.staging;
          echo "" >> ${{ github.workspace }}/android/sentry.properties
          echo auth.token=${{ secrets.SENTRY_TOKEN }} >> ${{ github.workspace }}/android/sentry.properties

      - name: Build Release APK
        env:
          KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
        run: |
          cd android
          echo "$KEYSTORE_BASE64" | base64 --decode > app/SmartStop.keystore
          ./gradlew assembleStagingRelease -PversionCode=${{ github.run_number }}
      
      - name: Upload APK as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: android-apk
          path: ${{ github.workspace }}/android/app/build/outputs/apk/staging/release/app-staging-release.apk

  build-ios:
    runs-on: macos-latest
    environment: stage
    steps:
      - name: Checkout the Repo (Latest)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref == ''

      - name: Checkout the Repo (Custom Ref)
        uses: actions/checkout@v4
        if: github.event.inputs.git-ref != ''
        with:
          ref: ${{ github.event.inputs.git-ref }}

      - name: Set Xcode
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: '16.1'
      
      - name: Install certificates and profiles
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.BUILD_CERTIFICATE_BASE64 }}
          P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.BUILD_PROVISION_PROFILE_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: ./.github/scripts/install_certs_and_profiles.sh

      - name: Install dependencies
        uses: ./.github/actions/install-dependencies

      - name: Set Environment
        run: |
          echo API_URL=${{ vars.SMARTSTOP_API_URL }} >> ${{ github.workspace }}/.env.staging;
          echo NOKE_MODE=${{ vars.SMARTSTOP_NOKE_MODE }} >> ${{ github.workspace }}/.env.staging;
          echo ENVIRONMENT=staging >> ${{ github.workspace }}/.env.staging;
          echo "" >> ${{ github.workspace }}/ios/sentry.properties
          echo auth.token=${{ secrets.SENTRY_TOKEN }} >> ${{ github.workspace }}/ios/sentry.properties

      - name: Set Build Number
        run: cd ios && bundle exec fastlane run increment_build_number build_number:${{ github.run_number }} && cd ../

      - name: Generate iOS Archive
        run: bundle exec fastlane ios build_stage
      
      - name: Upload IPA as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: ios-build
          path: ${{ github.workspace }}/ios/build/stage/SmartStop-stg.ipa

      - name: Upload dSYM as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: ios-symbols
          path: ${{ github.workspace }}/ios/build/stage/SmartStop-stg.app.dSYM.zip

  distribute-android:
    needs: build-android
    runs-on: ubuntu-latest
    environment: stage
    steps:
      - name: Download APK artifact
        uses: actions/download-artifact@v4
        with:
          name: android-apk
          path: build

      - name: Distribute to Firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ vars.FIREBASE_ANDROID_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_CRED_FILE_CONTENT }}
          groups: MEDL # use a comma-delimited list if we want to distribute to multiple groups
          file: build/app-staging-release.apk

  distribute-ios:
    needs: build-ios
    runs-on: ubuntu-latest
    environment: stage
    steps:
      - name: Download IPA artifact
        uses: actions/download-artifact@v4
        with:
          name: ios-build
          path: build

      - name: Distribute to Firebase
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ vars.FIREBASE_IOS_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_CRED_FILE_CONTENT }}
          groups: MEDL # use a comma-delimited list if we want to distribute to multiple groups
          file: build/SmartStop-stg.ipa