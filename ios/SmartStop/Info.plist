<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(BUNDLE_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.medlent.smartstop</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>smartstop</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>We use bluetooth to help you easily access your gate and unit</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>We use bluetooth to help you easily access your gate and unit</string>
	<key>NSCameraUsageDescription</key>
	<string>We access your device camera to update your profile photo</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Use FaceID to protect access to your account on your device.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We use your location to help direct you to the best Smart Stop facility for you</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to help direct you to the best Smart Stop facility for you</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We access your device photo library to update your profile photo</string>
	<key>UIAppFonts</key>
	<array>
		<string>FilsonPro-Black.ttf</string>
		<string>FilsonPro-Bold.ttf</string>
		<string>FilsonPro-Heavy.ttf</string>
		<string>FilsonPro-Light.ttf</string>
		<string>FilsonPro-Medium.ttf</string>
		<string>FilsonPro-Regular.ttf</string>
		<string>FilsonPro-Thin.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>fetch</string>
		<string>location</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_key</key>
	<dict>
		<key>live</key>
		<string>key_live_hAmSzn69BgF3UbGJpID0UkklsqgwFpkQ</string>
		<key>test</key>
		<string>key_test_iynQCa59vhA8JcIIeTz3RkjeruixFpgv</string>
	</dict>
	<key>branch_universal_link_domains</key>
	<array>
		<string>373sp.app.link</string>
		<string>373sp-alternate.test-app.link</string>
		<string>373sp.test-app.link</string>
		<string>373sp-alternate.test-app.link</string>
	</array>
</dict>
</plist>
