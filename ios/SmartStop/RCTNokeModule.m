//
//  RCTNokeModule.m
//  SmartStop
//
//  Created by <PERSON> on 9/12/24.
//

// RCTCalendarModule.m
#import "RCTNokeModule.h"
#import "SmartStop-Swift.h"

@implementation RCTNokeModule
{
  bool hasListeners;
}

// To export a module named RCTCalendarModule
RCT_EXPORT_MODULE();

#pragma mark - Constants

NSString *NOKE_DISCOVERED_KEY = @"NOKE_DISCOVERED";
NSString *NOKE_CONNECTING_KEY = @"NOKE_CONNECTING";
NSString *NOKE_CONNECTED_KEY = @"NOKE_CONNECTED";
NSString *NOKE_SYNCING_KEY = @"NOKE_SYNCING";
NSString *NOKE_UNLOCKED_KEY = @"NOKE_UNLOCKED";
NSString *NOKE_SHUTDOWN_KEY = @"NOKE_SHUTDOWN";
NSString *NOKE_DISCONNECTED_KEY = @"NOKE_DISCONNECTED";
NSString *DATA_UPLOADED_KEY = @"DATA_UPLOADED";
NSString *BLUETOOTH_STATUS_CHANGED_KEY = @"BLUETOOTH_STATUS_CHANGED";
NSString *ERROR_KEY = @"ERROR";
NSString *NOKE_ERROR_KEY = @"NOKE_ERROR";
NSString *NOKE_FIRMWARE_UPDATE_KEY = @"NOKE_FIRMWARE_UPDATE";

- (NSDictionary *)constantsToExport
{
 return @{
   NOKE_DISCOVERED_KEY: @"noke_discovered",
   NOKE_CONNECTING_KEY: @"noke_connecting",
   NOKE_CONNECTED_KEY: @"noke_connected",
   NOKE_SYNCING_KEY: @"noke_syncing",
   NOKE_UNLOCKED_KEY: @"noke_unlocked",
   NOKE_SHUTDOWN_KEY: @"noke_shutdown",
   NOKE_DISCONNECTED_KEY: @"noke_disconnected",
   DATA_UPLOADED_KEY: @"data_uploaded",
   BLUETOOTH_STATUS_CHANGED_KEY: @"bluetooth_status_changed",
   ERROR_KEY: @"error",
   NOKE_ERROR_KEY: @"noke_error",
   NOKE_FIRMWARE_UPDATE_KEY: @"noke_firmware_update",
 };
}

// Will be called when this module's first listener is added.
-(void)startObserving {
    hasListeners = YES;
    // Set up any upstream listeners or background tasks as necessary
}

// Will be called when this module's last listener is removed, or on dealloc.
-(void)stopObserving {
    hasListeners = NO;
    // Remove upstream listeners, stop unnecessary background tasks
}

- (NSArray<NSString *> *)supportedEvents {
  return @[
    [[self constantsToExport] valueForKey:NOKE_DISCOVERED_KEY],
    [[self constantsToExport] valueForKey:NOKE_CONNECTING_KEY],
    [[self constantsToExport] valueForKey:NOKE_CONNECTED_KEY],
    [[self constantsToExport] valueForKey:NOKE_SYNCING_KEY],
    [[self constantsToExport] valueForKey:NOKE_UNLOCKED_KEY],
    [[self constantsToExport] valueForKey:NOKE_SHUTDOWN_KEY],
    [[self constantsToExport] valueForKey:NOKE_DISCONNECTED_KEY],
    [[self constantsToExport] valueForKey:DATA_UPLOADED_KEY],
    [[self constantsToExport] valueForKey:BLUETOOTH_STATUS_CHANGED_KEY],
    [[self constantsToExport] valueForKey:ERROR_KEY],
    [[self constantsToExport] valueForKey:NOKE_ERROR_KEY],
    [[self constantsToExport] valueForKey:NOKE_FIRMWARE_UPDATE_KEY],
  ];
}

#pragma mark - Exported Methods

RCT_EXPORT_METHOD(initNoke:(NSInteger)mode)
{
  [[NokeBridge shared] initNokeWithMode:mode];
  [NokeBridge shared].delegate = self;
}

RCT_EXPORT_METHOD(searchForLock:(NSString *) name:(NSString *)mac)
{
  [[NokeBridge shared] searchForLockWithName:name mac:mac];
}

RCT_EXPORT_METHOD(stopSearchingForLock:(NSString *)mac)
{
  [[NokeBridge shared] stopSearchingForLockWithMac:mac];
}

RCT_EXPORT_METHOD(stopSearchingForAllLocks)
{
  [[NokeBridge shared] stopSearchingForAllLocks];
}

RCT_EXPORT_METHOD(connectToLock:(NSString *)mac)
{
  [[NokeBridge shared] connectToLockWithMac:mac];
}

RCT_EXPORT_METHOD(disconnectFromLock)
{
  [[NokeBridge shared] disconnectFromLock];
}

RCT_EXPORT_METHOD(offlineUnlock:(NSString *)offlineKey: (NSString *)unlockCommand)
{
  [[NokeBridge shared] offlineUnlockWithOfflineKey:offlineKey unlockCommand:unlockCommand];
}

RCT_EXPORT_METHOD(scheduledOfflineUnlock:(NSString *)offlineKey: (NSString *)unlockCommand)
{
  [[NokeBridge shared] offlineUnlockWithOfflineKey:offlineKey unlockCommand:unlockCommand];
}

RCT_EXPORT_METHOD(sendCommand:(NSString *)commandString)
{
  [[NokeBridge shared] sendCommandWithCommandString:commandString];
}

#pragma mark - NokeBridgeDelegate

- (void)nokeDeviceDiscovered:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_DISCOVERED_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceConnecting:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_CONNECTING_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceConnected:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_CONNECTED_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceSyncing:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_SYNCING_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceUnlocked:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_UNLOCKED_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceDidShutdown:(NokeDeviceBridge * _Nonnull)noke isLocked:(BOOL)isLocked didTimeout:(BOOL)didTimeout {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_SHUTDOWN_KEY] body:@{
      @"noke": [noke toDictionary],
      @"isLocked": @(isLocked),
      @"didTimeout": @(didTimeout)
    }];
  }
}

- (void)nokeDeviceDisconnected:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_DISCONNECTED_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)nokeDeviceError:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_ERROR_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

- (void)bluetoothManagerDidUpdateState:(NSInteger)state {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:BLUETOOTH_STATUS_CHANGED_KEY] body:@{ @"bluetoothStatus": @(state) }];
  }
}

- (void)didUploadData:(NSInteger)result message:(NSString * _Nonnull)message {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:DATA_UPLOADED_KEY] body:@{ @"result": @(result), @"message": message }];
  }
}

- (void)nokeErrorDidOccur:(NSInteger)error message:(NSString * _Nonnull)message noke:(NokeDeviceBridge * _Nullable)noke {
  if (hasListeners) {
    NSMutableDictionary *event = [[NSMutableDictionary alloc] initWithDictionary:@{
      @"error": @(error),
      @"message": message,
    }];
    if (noke != nil) {
      [event setObject:[noke toDictionary] forKey:@"noke"];
    }
    [self sendEventWithName:[[self constantsToExport] valueForKey:ERROR_KEY] body:[event copy]];
  }
}

- (void)nokeReadyForFirmwareUpdate:(NokeDeviceBridge * _Nonnull)noke {
  if (hasListeners) {
    [self sendEventWithName:[[self constantsToExport] valueForKey:NOKE_FIRMWARE_UPDATE_KEY] body:@{ @"noke": [noke toDictionary] }];
  }
}

@end
