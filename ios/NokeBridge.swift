//
//  NokeBridge.swift
//  SmartStop
//
//  Created by <PERSON> on 9/12/24.
//

import Foundation
import NokeMobileLibrary

@objcMembers public class NokeDeviceBridge: NSObject {
  let name: String
  let mac: String
  var session: String?
  
  init(name: String, mac: String) {
    self.name = name
    self.mac = mac
  }
  
  init(noke: NokeDevice) {
    self.name = noke.name
    self.mac = noke.mac
    self.session = noke.session
  }
  
  func toDictionary() -> NSDictionary {
    var session = ""
    if (self.session != nil) {
      session = self.session!
    }
    return [
      "mac": self.mac,
      "name": self.name,
      "session": session,
    ]
  }
}

@objc public protocol NokeBridgeDelegate {
  @objc func nokeDeviceDiscovered(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceConnecting(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceConnected(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceSyncing(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceUnlocked(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceDisconnected(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceError(_ noke: NokeDeviceBridge)
  @objc func nokeDeviceDidShutdown(_ noke: NokeDeviceBridge, isLocked: Bool, didTimeout: Bool)
  @objc func nokeErrorDidOccur(_ error: Int, message: String, noke: NokeDeviceBridge?)
  @objc func didUploadData(_ result: Int, message: String)
  @objc func bluetoothManagerDidUpdateState(_ state: Int)
  @objc func nokeReadyForFirmwareUpdate(_ noke: NokeDeviceBridge)
}

@objc class NokeBridge: NSObject, NokeDeviceManagerDelegate {
  static var sharedNokeBridge: NokeBridge?
  
  var connectedNoke: NokeDevice?
  @objc var delegate: NokeBridgeDelegate?
  
  @objc public static func shared()-> NokeBridge {
    if (sharedNokeBridge == nil) {
      sharedNokeBridge = NokeBridge.init()
    }
    return sharedNokeBridge!
  }
  
  @objc func initNoke(mode: Int) {
    NokeDeviceManager.shared().setLibraryMode(NokeLibraryMode(rawValue: mode) ?? .SANDBOX)
    NokeDeviceManager.shared().delegate = self
  }
  
  @objc func searchForLock(name: String, mac: String) {
    if (NokeDeviceManager.shared().nokeDevices.contains(where: { (key: String, value: NokeDevice) in
      return key == mac
    })) {
      return // already searching for device
    }
    let noke = NokeDevice.init(name: name, mac: mac)
    NokeDeviceManager.shared().addNoke(noke!)
  }
  
  @objc func stopSearchingForLock(mac: String) {
    if (NokeDeviceManager.shared().nokeDevices.contains(where: { (key: String, value: NokeDevice) in
      return key == mac
    })) {
      NokeDeviceManager.shared().removeNoke(mac: mac)
    }
  }
  
  @objc func stopSearchingForAllLocks() {
    NokeDeviceManager.shared().removeAllNoke()
  }
  
  @objc func connectToLock(mac: String) {
    if (NokeDeviceManager.shared().nokeDevices.contains(where: { (key: String, value: NokeDevice) in
      return key == mac
    })) {
      NokeDeviceManager.shared().connectToNokeDevice(NokeDeviceManager.shared().nokeDevices[mac]!)
    }
  }
  
  @objc func disconnectFromLock() {
    if (connectedNoke != nil) {
      NokeDeviceManager.shared().disconnectNokeDevice(connectedNoke!)
    }
  }
  
  @objc func offlineUnlock(offlineKey: String, unlockCommand: String) {
    if (connectedNoke != nil) {
      connectedNoke?.offlineUnlock(key: offlineKey, command: unlockCommand)
    }
  }
  
  @objc func sendCommand(commandString: String) {
    if (connectedNoke != nil) {
      connectedNoke?.sendCommands(commandString)
    }
  }

  func nokeDeviceDidUpdateState(to state: NokeMobileLibrary.NokeDeviceConnectionState, noke: NokeMobileLibrary.NokeDevice) {
    switch state {
    case .Discovered:
      delegate?.nokeDeviceDiscovered(NokeDeviceBridge.init(noke: noke))
      break
    case .Connecting:
      delegate?.nokeDeviceConnecting(NokeDeviceBridge.init(noke: noke))
      break
    case .Connected:
      connectedNoke = noke
      delegate?.nokeDeviceConnected(NokeDeviceBridge.init(noke: noke))
      break
    case .Syncing:
      delegate?.nokeDeviceSyncing(NokeDeviceBridge.init(noke: noke))
      break
    case .Unlocked:
      delegate?.nokeDeviceUnlocked(NokeDeviceBridge.init(noke: noke))
      break
    case .Disconnected:
      connectedNoke = nil
      delegate?.nokeDeviceDisconnected(NokeDeviceBridge.init(noke: noke))
      break
    case .Error:
      delegate?.nokeDeviceError(NokeDeviceBridge.init(noke: noke))
      break
    }
  }
  
  func nokeDeviceDidShutdown(noke: NokeMobileLibrary.NokeDevice, isLocked: Bool, didTimeout: Bool) {
    delegate?.nokeDeviceDidShutdown(NokeDeviceBridge.init(noke: noke), isLocked: isLocked, didTimeout: didTimeout)
  }
  
  func nokeErrorDidOccur(error: NokeMobileLibrary.NokeDeviceManagerError, message: String, noke: NokeMobileLibrary.NokeDevice?) {
    var nokeDevice: NokeDeviceBridge?
    if (noke != nil) {
      nokeDevice = NokeDeviceBridge.init(noke: noke!)
    }
    delegate?.nokeErrorDidOccur(error.rawValue, message: message, noke: nokeDevice)
  }
  
  func didUploadData(result: Int, message: String) {
    delegate?.didUploadData(result, message: message)
  }
  
  func bluetoothManagerDidUpdateState(state: NokeMobileLibrary.NokeManagerBluetoothState) {
    delegate?.bluetoothManagerDidUpdateState(state.rawValue)
    switch (state) {
      case NokeManagerBluetoothState.poweredOn:
          debugPrint("NOKE MANAGER ON")
          NokeDeviceManager.shared().startScanForNokeDevices()
          break
      case NokeManagerBluetoothState.poweredOff:
          debugPrint("NOKE MANAGER OFF")
          break
      default:
          debugPrint("NOKE MANAGER UNSUPPORTED")
          break
    }
  }
  
  func nokeReadyForFirmwareUpdate(noke: NokeMobileLibrary.NokeDevice) {
    delegate?.nokeReadyForFirmwareUpdate(NokeDeviceBridge.init(noke: noke))
  }}
