# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

# IMPORTANT: This maps xcode cofigurations to let CocoaPods
# know what configurations are debug and which are release, regardless of name
# https://stackoverflow.com/a/30102977
configurations = {
  'Debug' => :debug,
  'Debug-Stage' => :debug,
  'Release' => :release,
  'Release-Stage' => :release,
}

# IMPORTANT: Defines custom xcode configurations for CocoaPods. Without this and
# the above, React Native won't know where to look for the bundle, among many
# other things.
project './SmartStop.xcodeproj', configurations

platform :ios, min_ios_version_supported
prepare_react_native_project!

# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

target 'SmartStop' do
  config = use_native_modules!
  use_frameworks! :linkage => :static
  use_react_native!(
    :path => config[:reactNativePath],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    # :flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
  
  # uncomment the permissions you need
  setup_permissions([
    # 'AppTrackingTransparency',
     'Bluetooth',
    # 'Calendars',
    # 'CalendarsWriteOnly',
    # 'Camera',
    # 'Contacts',
    # 'FaceID',
     'LocationAccuracy',
     'LocationAlways',
     'LocationWhenInUse',
    # 'MediaLibrary',
    # 'Microphone',
    # 'Motion',
     'Notifications',
    # 'PhotoLibrary',
    # 'PhotoLibraryAddOnly',
    # 'Reminders',
    # 'Siri',
    # 'SpeechRecognition',
    # 'StoreKit',
  ])

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'NokeMobileLibrary'

  target 'SmartStopTests' do
    inherit! :complete
    # Pods for testing
  end

  ENVFILES = {
    'Debug-Stage' => '$(PODS_ROOT)/../../.env.staging',
    'Release-Stage' => '$(PODS_ROOT)/../../.env.staging',
    'Debug' => '$(PODS_ROOT)/../../.env.production',
    'Release' => '$(PODS_ROOT)/../../.env.production',
    'Debug-Store' => '$(PODS_ROOT)/../../.env.production',
    'Release-Store' => '$(PODS_ROOT)/../../.env.production',
  }
  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    # Set build settings with env file based on target configuration
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        if target.name == 'react-native-config'
          config.build_settings['ENVFILE'] = ENVFILES[config.name]
        end
      end
    end
  end
end
