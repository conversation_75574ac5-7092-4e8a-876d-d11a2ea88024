# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Build an archive for Production release"
  lane :build_prod do
    setup_ci if ENV['CI']
    build_app(
      scheme: "SmartStop",
      configuration: "Release",
      workspace: "ios/SmartStop.xcworkspace",
      output_directory: "ios/build/prod",
      output_name: "SmartStop.ipa",
      export_method: "ad-hoc", # will need to update to app-store
      export_options: {
        # Update with additional bundle ids when we setup the real store account
        provisioningProfiles: {
          "com.smartstop.selfstorage" => "SmartStop Self Storage Ad Hoc"
        }
      }
    )
  end

  lane :build_stage do
    setup_ci if ENV['CI']
    build_app(
      scheme: "SmartStopStaging",
      configuration: "Release-Stage",
      workspace: "ios/SmartStop.xcworkspace",
      output_directory: "ios/build/stage",
      output_name: "SmartStop-stg.ipa",
      export_method: "ad-hoc",
      export_options: {
        # Update with additional bundle ids when we setup the real store account
        provisioningProfiles: {
          "com.smartstop.selfstorage.staging" => "SmartStop Self Storage Staging Ad Hoc"
        }
      }
    )
  end

  lane :build_store do
    setup_ci if ENV['CI']
    build_app(
      scheme: "SmartStopStore",
      configuration: "Release-Store",
      workspace: "ios/SmartStop.xcworkspace",
      output_directory: "ios/build/store",
      output_name: "SmartStop.ipa",
      export_method: "app-store",
      export_options: {
        # Update with additional bundle ids when we setup the real store account
        provisioningProfiles: {
          "com.smartstop.selfstorage" => "SmartStop Self Storage Appstore"
        }
      }
    )
  end
end
