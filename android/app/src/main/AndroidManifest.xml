<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.BLUETOOTH" />
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
  <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
      android:usesPermissionFlags="neverForLocation" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
  <uses-feature android:name="android.hardware.camera" android:required="false" />
  <uses-feature android:name="android.hardware.camera.front" android:required="false" />
  <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
  >
    <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:theme="@style/SplashTheme"
        android:screenOrientation="portrait"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="smartstop" />
      </intent-filter>

      <!-- Branch App Links - Live App -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <!-- REPLACE `android:host` with your `app.link` domain -->
        <data android:scheme="https" android:host="373sp.app.link" />
        <data android:scheme="https" android:host="373sp-alternate.app.link" />
      </intent-filter>

      <!-- Branch App Links - Test App -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="373sp.test-app.link" />
        <data android:scheme="https" android:host="373sp-alternate.test-app.link" />
      </intent-filter>
    </activity>

    <service android:name="com.noke.nokemobilelibrary.NokeDeviceManagerService" android:enabled="true"/>

    <!-- Branch init -->
    <!-- REPLACE `BranchKey` with the value from your Branch Dashboard -->
    <meta-data android:name="io.branch.sdk.BranchKey" android:value="key_live_hAmSzn69BgF3UbGJpID0UkklsqgwFpkQ" />
    <!-- REPLACE `BranchKey.test` with the value from your Branch Dashboard -->
    <meta-data android:name="io.branch.sdk.BranchKey.test" android:value="key_test_iynQCa59vhA8JcIIeTz3RkjeruixFpgv" />
    <!-- Set to `true` to use `BranchKey.test` -->
    <meta-data android:name="io.branch.sdk.TestMode" android:value="true" />
  </application>
</manifest>