package com.smartstop
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.noke.nokemobilelibrary.NokeDefines
import com.noke.nokemobilelibrary.NokeDevice
import com.noke.nokemobilelibrary.NokeDeviceManagerService
import com.noke.nokemobilelibrary.NokeDeviceManagerService.LocalBinder
import com.noke.nokemobilelibrary.NokeServiceListener


class NokeModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val NOKE_DISCOVERED_KEY = "NOKE_DISCOVERED"
    private val NOKE_CONNECTING_KEY = "NOKE_CONNECTING"
    private val NOKE_CONNECTED_KEY = "NOKE_CONNECTED"
    private val NOKE_SYNCING_KEY = "NOKE_SYNCING"
    private val NOKE_UNLOCKED_KEY = "NOKE_UNLOCKED"
    private val NOKE_SHUTDOWN_KEY = "NOKE_SHUTDOWN"
    private val NOKE_DISCONNECTED_KEY = "NOKE_DISCONNECTED"
    private val DATA_UPLOADED_KEY = "DATA_UPLOADED"
    private val BLUETOOTH_STATUS_CHANGED_KEY = "BLUETOOTH_STATUS_CHANGED"
    private val ERROR_KEY = "ERROR"

    private val events: MutableMap<String, Any> = hashMapOf(
        NOKE_DISCOVERED_KEY to "noke_discovered",
        NOKE_CONNECTING_KEY to "noke_connecting",
        NOKE_CONNECTED_KEY to "noke_connected",
        NOKE_SYNCING_KEY to "noke_syncing",
        NOKE_UNLOCKED_KEY to "noke_unlocked",
        NOKE_SHUTDOWN_KEY to "noke_shutdown",
        NOKE_DISCONNECTED_KEY to "noke_disconnected",
        DATA_UPLOADED_KEY to "data_uploaded",
        BLUETOOTH_STATUS_CHANGED_KEY to "bluetooth_status_changed",
        ERROR_KEY to "error",
    )

    private var mNokeService: NokeDeviceManagerService? = null
    private var discoveredDevices: MutableMap<String, NokeDevice> = mutableMapOf()
    private var connectedNoke: NokeDevice? = null
    private var serviceMode: Int = 0

    override fun getName() = "NokeModule"

    override fun getConstants(): MutableMap<String, Any> = events

    @ReactMethod fun initNoke(mode: Int) {
        serviceMode = mode
        val nokeServiceIntent = Intent(this.reactApplicationContext, NokeDeviceManagerService::class.java)
        reactApplicationContext.bindService(nokeServiceIntent, mServiceConnection, Context.BIND_AUTO_CREATE)
    }

    @ReactMethod fun searchForLock(name: String, mac: String) {
        if (mNokeService?.nokeDevices?.containsKey(mac) == true) {
            return; // already searching for device
        }
        mNokeService?.addNokeDevice(NokeDevice(name, mac))
    }

    @ReactMethod fun stopSearchingForLock(mac: String) {
        if (mNokeService?.nokeDevices?.containsKey(mac) == true) {
            mNokeService?.removeNokeDevice(mac)
        }
    }

    @ReactMethod fun stopSearchingForAllLocks() {
        mNokeService?.removeAllNoke()
    }

    @ReactMethod fun connectToLock(mac: String) {
        if (connectedNoke != null) {
            Log.println(Log.DEBUG, "NOKE", "device already conected");
        }
        if (discoveredDevices.containsKey(mac)) {
            val device = discoveredDevices[mac]
            mNokeService?.connectToNoke(device)
        }
    }

    @ReactMethod fun disconnectFromLock() {
        if (connectedNoke != null) {
            mNokeService?.disconnectNoke(connectedNoke)
        }
    }

    @ReactMethod fun offlineUnlock(offlineKey: String, unlockCommand: String) {
        if (connectedNoke != null) {
            connectedNoke?.offlineKey = offlineKey
            connectedNoke?.offlineUnlockCmd = unlockCommand
            connectedNoke?.offlineUnlock()
        }
    }

    @ReactMethod fun scheduledOfflineUnlock(offlineKey: String, unlockCommand: String) {
        if (connectedNoke != null) {
            connectedNoke?.offlineKey = offlineKey
            connectedNoke?.offlineUnlockCmd = unlockCommand
            connectedNoke?.scheduledOfflineUnlock()
        }
    }

    @ReactMethod fun sendCommand(commandString: String) {
        if (connectedNoke != null) {
            connectedNoke?.sendCommands(commandString)
        }
    }

    private var listenerCount = 0

    @ReactMethod fun addListener(eventName: String) {
        listenerCount += 1
    }

    @ReactMethod fun removeListeners(count: Int) {
        if (listenerCount > 0) {
            listenerCount -= count
        }
    }

    private fun sendEvent(reactContext: ReactContext, eventName: String, params: WritableMap?) {
        if (listenerCount <= 0) {
            return // avoid sending events when no listeners are registered
        }
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    private val mServiceConnection = object: ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {

            //Store reference to service
            mNokeService = (service as LocalBinder).getService(serviceMode)


            //Register callback listener
            mNokeService!!.registerNokeListener(mNokeServiceListener)


            //Start bluetooth scanning
            mNokeService!!.startScanningForNokeDevices()

            if (!mNokeService!!.initialize()) {
                Log.e("NOKE", "Unable to initialize Bluetooth")
            }
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            mNokeService = null
        }
    }

    private val mNokeServiceListener = object: NokeServiceListener {
        override fun onNokeDiscovered(noke: NokeDevice?) {
            if (noke != null) {
                discoveredDevices[noke.mac] = noke
                val params = Arguments.createMap().apply {
                    putMap("noke", createNokeDeviceMap(noke))
                }
                sendEvent(reactContext, events[NOKE_DISCOVERED_KEY] as String, params)
            }
        }

        override fun onNokeConnecting(noke: NokeDevice?) {
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            sendEvent(reactContext, events[NOKE_CONNECTING_KEY] as String, params)
        }

        override fun onNokeConnected(noke: NokeDevice?) {
            connectedNoke = noke
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            sendEvent(reactContext, events[NOKE_CONNECTED_KEY] as String, params)
        }

        override fun onNokeSyncing(noke: NokeDevice?) {
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            sendEvent(reactContext, events[NOKE_SYNCING_KEY] as String, params)
        }

        override fun onNokeUnlocked(noke: NokeDevice?) {
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            sendEvent(reactContext, events[NOKE_UNLOCKED_KEY] as String, params)
        }

        override fun onNokeShutdown(noke: NokeDevice?, isLocked: Boolean?, didTimeout: Boolean?) {
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            if (isLocked != null) {
                params.putBoolean("isLocked", isLocked)
            }
            if (didTimeout != null) {
                params.putBoolean("didTimeout", didTimeout)
            }
            sendEvent(reactContext, events[NOKE_SHUTDOWN_KEY] as String, params)
        }

        override fun onNokeDisconnected(noke: NokeDevice?) {
            connectedNoke = null
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
            }
            sendEvent(reactContext, events[NOKE_DISCONNECTED_KEY] as String, params)
        }

        override fun onDataUploaded(result: Int, message: String?) {
            val params = Arguments.createMap().apply {
                putInt("result", result)
                putString("message", message)
            }
            sendEvent(reactContext, events[DATA_UPLOADED_KEY] as String, params)
        }

        override fun onBluetoothStatusChanged(bluetoothStatus: Int) {
            val params = Arguments.createMap().apply {
                putInt("bluetoothStatus", bluetoothStatus)
            }
            sendEvent(reactContext, events[BLUETOOTH_STATUS_CHANGED_KEY] as String, params)
        }

        override fun onError(noke: NokeDevice?, error: Int, message: String) {
            Log.e("NOKE", "NOKE SERVICE ERROR $error: $message")
            val params = Arguments.createMap().apply {
                putMap("noke", createNokeDeviceMap(noke))
                putInt("error", error)
                putString("message", message)
            }
            sendEvent(reactContext, events[ERROR_KEY] as String, params)
        }
    }

    private fun createNokeDeviceMap(noke: NokeDevice?): WritableMap {
        val map = Arguments.createMap()
        if (noke != null) {
            map.apply {
                putString("mac", noke.mac)
                putString("name", noke.name)
                putString("session", noke.session)
            }
        }

        return map;
    }
}