{"name": "smartstop", "version": "1.0.6", "private": true, "scripts": {"android": "react-native run-android --mode=stagingDebug", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint . --fix", "start": "react-native start", "test": "jest", "type-check": "tsc", "test:report": "jest --collectCoverage --coverageDirectory=\"./coverage\" --ci --reporters=default --reporters=jest-junit --coverage", "pod-install": "bundle exec pod install --project-directory=ios", "refresh-install": "npx rimraf node_modules ios/Pods && npm i && npm run pod-install"}, "dependencies": {"@react-native-community/netinfo": "~11.4.0", "@react-native-firebase/analytics": "~21.6.1", "@react-native-firebase/app": "~21.6.1", "@react-native-masked-view/masked-view": "~0.3.1", "@react-navigation/bottom-tabs": "~6.5.20", "@react-navigation/drawer": "~6.6.15", "@react-navigation/elements": "~1.3.31", "@react-navigation/native": "~6.1.17", "@react-navigation/native-stack": "~6.9.26", "@react-navigation/stack": "~6.3.29", "@sentry/react-native": "~6.4.0", "@tanstack/react-query": "~5.29.0", "@typescript-eslint/eslint-plugin": "~7.13.1", "axios": "~1.6.8", "eslint-config-prettier": "~9.1.0", "i18next": "~23.11.1", "lottie-react-native": "~6.7.2", "luxon": "~3.4.4", "react": "18.2.0", "react-i18next": "~14.1.0", "react-native": "0.73.6", "react-native-android-location-enabler": "~2.0.1", "react-native-android-open-settings": "~1.3.0", "react-native-biometrics": "~3.0.1", "react-native-ble-manager": "~11.5.5", "react-native-branch": "~6.4.0", "react-native-config": "~1.5.1", "react-native-element-dropdown": "~2.12.0", "react-native-gesture-handler": "~2.16.2", "react-native-get-location": "~5.0.0", "react-native-image-crop-picker": "~0.41.2", "react-native-indicators": "~0.17.0", "react-native-keyboard-aware-scroll-view": "~0.9.5", "react-native-mmkv": "~2.12.2", "react-native-page-indicator": "~2.3.0", "react-native-pager-view": "~6.3.3", "react-native-permissions": "~4.1.5", "react-native-reanimated": "~3.8.1", "react-native-safe-area-context": "~4.9.0", "react-native-screens": "~3.31.1", "react-native-snap-carousel": "~1.3.1", "react-native-svg": "~15.3.0", "react-native-swiper": "~1.6.0", "react-native-walkthrough-tooltip": "~1.6.0", "react-native-webview": "~13.10.4", "rn-gesture-swipeable-flatlist": "~2.3.1", "zod": "~3.22.4"}, "devDependencies": {"@babel/core": "~7.24.4", "@babel/preset-env": "~7.24.4", "@babel/runtime": "~7.24.4", "@cspell/eslint-plugin": "~8.16.0", "@eslint/js": "~9.5.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@testing-library/jest-native": "~5.4.2", "@testing-library/react-native": "~12.4.5", "@types/jest": "~29.5.12", "@types/luxon": "~3.4.2", "@types/node": "~18.14.1", "@types/react": "~18.2.75", "@types/react-native-indicators": "~0.16.6", "@types/react-test-renderer": "~18.0.7", "@typescript-eslint/parser": "~7.15.0", "babel-jest": "~29.7.0", "babel-plugin-module-resolver": "~5.0.0", "babel-plugin-root-import": "~6.6.0", "eslint": "~8.57.0", "eslint-config-airbnb": "~19.0.4", "eslint-config-airbnb-typescript": "~18.0.0", "eslint-import-resolver-typescript": "~3.6.1", "eslint-plugin-import": "~2.29.1", "eslint-plugin-jest": "~28.2.0", "eslint-plugin-jsx-a11y": "~6.8.0", "eslint-plugin-react": "~7.34.3", "globals": "~15.6.0", "jest": "~29.7.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.3.3", "typescript-eslint": "~7.13.1"}, "engines": {"node": ">=18"}}