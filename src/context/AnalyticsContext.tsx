import React, { createContext, useContext, useState, useMemo } from 'react';
import { userStorage } from '@/utils/storage';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';

interface AnalyticsContextType {
  facilityId?: string;
  tenantId?: string;
  currentScreen?: string;
  setFacilityId: (id: string | undefined) => void;
  setTenantId: (id: string | undefined) => void;
  setCurrentScreen: (screenName: string, params?: object) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType>({
  setFacilityId: () => {},
  setTenantId: () => {},
  setCurrentScreen: () => {},
});

export const ANALYTICS_STORAGE_KEYS = {
  FACILITY_ID: 'current_facility_id',
  TENANT_ID: 'current_tenant_id',
};

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const [facilityId, setFacilityId] = useState<string | undefined>(
    userStorage.getString(ANALYTICS_STORAGE_KEYS.FACILITY_ID) || undefined,
  );
  const [tenantId, setTenantId] = useState<string | undefined>(
    userStorage.getString(ANALYTICS_STORAGE_KEYS.TENANT_ID) || undefined,
  );
  const [currentScreen, setCurrentScreen] = useState<string>();

  const updateFacilityId = (id: string | undefined) => {
    console.log('[Analytics] Updating facilityId:', { old: facilityId, new: id });
    if (id) {
      userStorage.set(ANALYTICS_STORAGE_KEYS.FACILITY_ID, id);
    } else {
      userStorage.delete(ANALYTICS_STORAGE_KEYS.FACILITY_ID);
    }
    setFacilityId(id);
  };

  const updateTenantId = (id: string | undefined) => {
    console.log('[Analytics] Updating tenantId:', { old: tenantId, new: id });
    if (id) {
      userStorage.set(ANALYTICS_STORAGE_KEYS.TENANT_ID, id);
    } else {
      userStorage.delete(ANALYTICS_STORAGE_KEYS.TENANT_ID);
    }
    setTenantId(id);
  };

  const updateCurrentScreen = async (screenName: string, params?: object) => {
    console.log('[Analytics] Screen view:', { screen: screenName, params });
    setCurrentScreen(screenName);
    await logEventWithStatus(Events.screenView, EventStatus.initiated, {
      screen_name: screenName,
      screen_params: params ? JSON.stringify(params) : undefined,
      facilityId,
      tenantId,
    });
  };

  const value = useMemo(
    () => ({
      facilityId,
      tenantId,
      currentScreen,
      setFacilityId: updateFacilityId,
      setTenantId: updateTenantId,
      setCurrentScreen: updateCurrentScreen,
    }),
    [facilityId, tenantId, currentScreen],
  );

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
}

export const useAnalytics = () => useContext(AnalyticsContext);
