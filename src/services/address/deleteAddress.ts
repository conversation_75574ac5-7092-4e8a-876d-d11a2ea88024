import { DeleteBillingAddressRequest } from '@/types/schemas/request';
import {
  BaseApiResponse,
  BaseApiResponseSchema,
} from '@/types/schemas/apiResponse';
import { deleteBillingAddress } from '../api';

export default async (
  data: DeleteBillingAddressRequest,
): Promise<BaseApiResponse> => {
  const response = await deleteBillingAddress(data);
  return BaseApiResponseSchema.parse(response);
};
