import { SetPrimaryAddressRequest } from '@/types/schemas/request';
import {
  BaseApiResponse,
  BaseApiResponseSchema,
} from '@/types/schemas/apiResponse';
import { setPrimaryBillingAddress } from '../api';

export default async (
  data: SetPrimaryAddressRequest,
): Promise<BaseApiResponse> => {
  const response = await setPrimaryBillingAddress(data);
  return BaseApiResponseSchema.parse(response);
};
