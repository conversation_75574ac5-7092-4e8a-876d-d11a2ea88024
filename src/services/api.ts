import * as request from '@/services/instance';
import { RefreshTokenGrantRequest } from '@/types/auth';
import {
  ApiAccessCodeResponse,
  ApiAccessCodeResponseSchema,
} from '@/types/schemas/access';
import {
  ApiAccountResponse,
  ApiAccountResponseSchema,
} from '@/types/schemas/account';
import {
  BaseApiResponse,
  BaseApiResponseSchema,
} from '@/types/schemas/apiResponse';
import {
  ApiGateOptionsResponse,
  ApiGateOptionsResponseSchema,
} from '@/types/schemas/gateOptions';
import {
  ApiLocationResponse,
  ApiLocationResponseSchema,
  ApiLocationsInStateResponse,
  ApiLocationsInStateResponseSchema,
} from '@/types/schemas/location';
import {
  ApiNokeGenericResponse,
  ApiNokeGenericResponseSchema,
  ApiNokeLockResponse,
  ApiNokeLocksResponseSchema,
  ApiNokeUnlockCommandResponse,
  ApiNokeUnlockCommandResponseSchema,
} from '@/types/schemas/Noke';
import {
  ApiNotificationsResponse,
  ApiNotificationsResponseSchema,
} from '@/types/schemas/notification';
import {
  ApiPaymentHistoryResponse,
  ApiPaymentHistoryResponseSchema,
} from '@/types/schemas/paymentHistory';
import {
  AddBillingAddressRequest,
  AddStorageAccountRequest,
  ChangePasswordRequest,
  DeleteBillingAddressRequest,
  DisableAutoPayRequest,
  FindAccountRequest,
  GetGateOptionsRequest,
  GetNokeLocksRequest,
  GetNokeUnlockCommandRequest,
  GetPaymentHistoryRequest,
  NokeUnlockOverNetworkRequest,
  NokeUpdateOnsiteStatusRequest,
  OpenGateRequest,
  PasswordResetRequest,
  PaymentHistoryRequest,
  RegisterAccountRequest,
  SendPaymentReceiptRequest,
  SetPrimaryAddressRequest,
  SubmitSupportFormRequest,
  UpdateCustomerContactRequest,
  WebViewUrlRequest,
} from '@/types/schemas/request';
import {
  ApiSecondaryContentResponse,
  ApiSecondaryContentResponseSchema,
} from '@/types/schemas/secondaryContent';
import {
  ApiStateResponse,
  ApiStateResponseSchema,
} from '@/types/schemas/state';
import {
  ApiTokenResponse,
  ApiTokenResponseSchema,
} from '@/types/schemas/tokenData';
import {
  ApiWebViewResponse,
  ApiWebViewResponseSchema,
} from '@/types/schemas/webview';
import { getTokenData } from '@/utils/auth';

const API_PREFIX = '/api/app/actions';
const AUTH_PREFIX = '/api/authorization/token';

const AUTHORIZATION_ENDPOINTS = {
  GET_TOKENS: `${AUTH_PREFIX}`,
  REFRESH_TOKENS: `${AUTH_PREFIX}`,
};

const ACCOUNT_ENDPOINTS = {
  GET_STORAGE_DATA: `${API_PREFIX}/GetStorageData`,
  GET_ACCOUNT_DATA: `${API_PREFIX}/GetAccountData`,
  ADD_STORAGE_ACCOUNT: `${API_PREFIX}/AddStorageAccount`,
  UPDATE_CONTACT_INFO: `${API_PREFIX}/UpdateCustomerContact`,
  CHANGE_PASSWORD: `${API_PREFIX}/ChangePassword`,
  SUBMIT_SUPPORT_FORM: `${API_PREFIX}/SubmitSupportForm`,
};

const BILLING_ENDPOINTS = {
  ADD_ADDRESS: `${API_PREFIX}/AddBillingAddress`,
  DELETE_ADDRESS: `${API_PREFIX}/DeleteBillingAddress`,
  SET_PRIMARY_ADDRESS: `${API_PREFIX}/SetPrimaryBillingAddress`,
  GET_PAYMENT_HISTORY: `${API_PREFIX}/GetPaymentHistory`,
  GET_PAYMENT_HISTORY_FOR_UNIT: `${API_PREFIX}/GetPaymentHistoryUnitList`,
  DISABLE_AUTO_PAY: `${API_PREFIX}/DisableAutopay`,
  SEND_PAYMENT_RECEIPT: `${API_PREFIX}/SendPaymentReceipt`,
};

const WEBVIEWS_ENDPOINTS = {
  AUTO_PAY_WEB_VEIW: `${API_PREFIX}/GetAutopayWebviewUrl`,
  PAYMENT_WEB_VEIW: `${API_PREFIX}/GetPaymentWebviewUrl`,
};

const CONTENT_ENDPOINTS = {
  GET_SECONDARY_CONTENT: `${API_PREFIX}/GetSecondaryContent`,
  GET_NOTIFICATIONS: `${API_PREFIX}/GetNotifications`,
};

const GATE_ACCESS_ENDPOINTS = {
  GET_ACCESS_CODE: `${API_PREFIX}/GetAccessCodes`,
  GET_GATE_OPTIONS: `${API_PREFIX}/GetGateOptions`,
  OPEN_GATE_PTI: `${API_PREFIX}/OpenGate`,
};

const NOKE_ENDPOINTS = {
  NOKE_GET_LOCKS: `${API_PREFIX}/Noke/user/locks`,
  NOKE_UPDATE_ONSITE_STATUS: `${API_PREFIX}/Noke/user/onsite`,
  NOKE_GET_UNLOCK_COMMAND: `${API_PREFIX}/Noke/lock/unlock`,
  NOKE_UNLOCK_OVER_NETWORK: `${API_PREFIX}/Noke/lock/unlock/mesh`,
};

const AUTH_ENDPOINTS = {
  ...ACCOUNT_ENDPOINTS,
  ...BILLING_ENDPOINTS,
  ...WEBVIEWS_ENDPOINTS,
  ...CONTENT_ENDPOINTS,
  ...GATE_ACCESS_ENDPOINTS,
  ...NOKE_ENDPOINTS,
};

const NON_AUTH_ENDPOINTS = {
  GET_STATES: `${API_PREFIX}/GetStates`,
  GET_LOCATIONS_IN_STATE: `${API_PREFIX}/GetFacilitiesInState`,
  PASSWORD_RESET: `${API_PREFIX}/RequestPasswordReset`,
  REGISTER_ACCOUNT: `${API_PREFIX}/RegisterAccount`,
  FIND_ACCOUNT: `${API_PREFIX}/FindAccount`,
};

const GenericError = new Error('Something went wrong. Please try again later');

/**
 * A method to reconcile a logic error from the API. Since the API returns a
 * status of 200 OK even in the event of an error, these errors are logic based
 * and we will want to evaluate the properties given to determine if we should
 * throw an error and what message should be logged.
 *
 * @param resp Api response describing if the request was successful and if not,
 * exposes a message property to let us know what went wrong
 * @param source The name of the function that the error was found in.
 */
const checkAndHandleError = (resp: BaseApiResponse, source?: string) => {
  const { success, message } = resp;
  if (!success) {
    // Insert some error logger here
    console.error(`Source: ${source}
      Error: ${message}`);
    throw new Error(message) ?? GenericError;
  }
};

const generateAuthHeaderConfig = () => {
  const tokenData = getTokenData();
  if (!tokenData) {
    return {};
  }
  const { accessToken } = tokenData;
  return {
    Authorization: `Bearer ${accessToken}`,
  };
};

// =============================================================================
// TOKEN REQUESTS
// =============================================================================

export const getTokens = async (username: string, password: string) => {
  const passwordGrantRequest = {
    username,
    password,
    grant_type: 'password',
  };
  const response = await request.post<ApiTokenResponse>(
    AUTHORIZATION_ENDPOINTS.GET_TOKENS,
    passwordGrantRequest,
  );
  return ApiTokenResponseSchema.parse(response);
};

export const refreshTokens = async (token: string) => {
  const refreshTokenRequest: RefreshTokenGrantRequest = {
    refresh_token: token,
    grant_type: 'refresh_token',
  };
  const response = await request.post<ApiTokenResponse>(
    AUTHORIZATION_ENDPOINTS.REFRESH_TOKENS,
    refreshTokenRequest,
  );
  return ApiTokenResponseSchema.parse(response);
};

// =============================================================================
// ACCOUNT REQUESTS
// =============================================================================

/**
 * Gets the list of rented storage units and associated store facility info.
 *
 * @returns Promise<ApiLocationResponse>
 */
export const getStorageData = async () => {
  const config = {
    params: {
      culture: 'en-us',
    },
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.get<ApiLocationResponse>(
    AUTH_ENDPOINTS.GET_STORAGE_DATA,
    config,
  );
  // checkAndHandleError(response, 'getStorageData');
  return ApiLocationResponseSchema.parse(response);
};

/**
 * Get stored profile info and billing addresses.
 *
 * @returns Promise<ApiAccountResponse>
 */
export const getAccountData = async () => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.get<ApiAccountResponse>(
    AUTH_ENDPOINTS.GET_ACCOUNT_DATA,
    config,
  );
  checkAndHandleError(response, 'getAccountData');
  return ApiAccountResponseSchema.parse(response);
};

/**
 * Links a storage account number to the user's account profile.
 *
 * @returns Promise<BaseApiResponse>
 */
export const addStorageAccount = async (data: AddStorageAccountRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.ADD_STORAGE_ACCOUNT,
    data,
    config,
  );
  checkAndHandleError(response, 'addStorageAccount');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Updates profile contact information.
 *
 * @returns Promise<BaseApiResponse>
 */
export const updateCustomerContact = async (
  data: UpdateCustomerContactRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.UPDATE_CONTACT_INFO,
    data,
    config,
  );
  checkAndHandleError(response, 'updateCustomerContact');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Changes the account login password.
 *
 * @returns Promise<BaseApiResponse>
 */
export const changePassword = async (data: ChangePasswordRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.CHANGE_PASSWORD,
    data,
    config,
  );
  checkAndHandleError(response, 'changePassword');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Sends an email from the support form.
 *
 * @returns Promise<BaseApiResponse>
 */
export const submitSupportForm = async (data: SubmitSupportFormRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.SUBMIT_SUPPORT_FORM,
    data,
    config,
  );
  checkAndHandleError(response, 'submitSupportForm');
  return BaseApiResponseSchema.parse(response);
};

// =============================================================================
// BILLING REQUESTS
// =============================================================================

/**
 * Adds a new billing address to the user's account
 *
 * @returns Promise<BaseApiResponse>
 */
export const addBillingAddress = async (data: AddBillingAddressRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.ADD_ADDRESS,
    data,
    config,
  );
  checkAndHandleError(response, 'addBillingAddress');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Deletes a billing address from the current account.
 *
 * @returns Promise<BaseApiResponse>
 */
export const deleteBillingAddress = async (
  data: DeleteBillingAddressRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.DELETE_ADDRESS,
    data,
    config,
  );
  checkAndHandleError(response, 'deleteBillingAddress');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Sets an existing billing address as the primary.
 *
 * @returns Promise<BaseApiResponse>
 */
export const setPrimaryBillingAddress = async (
  data: SetPrimaryAddressRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.SET_PRIMARY_ADDRESS,
    data,
    config,
  );
  checkAndHandleError(response, 'setPrimaryBillingAddress');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Fetches the payment history for one or more units.
 *
 * @returns Promise<ApiPaymentHistoryResponse>
 */
export const getPaymentHistory = async (reqData: PaymentHistoryRequest) => {
  const data: GetPaymentHistoryRequest = {
    culture: 'en-us',
    rentalAccounts: reqData,
  };
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiPaymentHistoryResponse>(
    AUTH_ENDPOINTS.GET_PAYMENT_HISTORY,
    data,
    config,
  );
  checkAndHandleError(response, 'getPaymentHistory');
  return ApiPaymentHistoryResponseSchema.parse(response);
};

// =============================================================================
// WEBVIEW REQUESTS
// =============================================================================

/**
 * Get "enable autopay" webview URL.
 *
 * @returns Promise<ApiWebViewResponse>
 */
export const getAutopayWebViewUrl = async (data: WebViewUrlRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiWebViewResponse>(
    AUTH_ENDPOINTS.AUTO_PAY_WEB_VEIW,
    data,
    config,
  );
  checkAndHandleError(response, 'getAutopayWebViewUrl');
  return ApiWebViewResponseSchema.parse(response);
};

/**
 * Get "make payment" webview URL.
 *
 * @returns Promise<ApiWebViewResponse>
 */
export const getPaymentWebViewUrl = async (data: WebViewUrlRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiWebViewResponse>(
    AUTH_ENDPOINTS.PAYMENT_WEB_VEIW,
    data,
    config,
  );
  checkAndHandleError(response, 'getPaymentWebViewUrl');
  return ApiWebViewResponseSchema.parse(response);
};

// =============================================================================
// CONTENT REQUESTS
// =============================================================================

/**
 * Gets the various secondary content for FAQs, termas and conditions, etc.
 *
 * @returns Promise<ApiSecondaryContentResponse>
 */
export const getSecondaryContent = async () => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.get<ApiSecondaryContentResponse>(
    AUTH_ENDPOINTS.GET_SECONDARY_CONTENT,
    config,
  );
  checkAndHandleError(response, 'getSecondaryContent');
  return ApiSecondaryContentResponseSchema.parse(response);
};

/**
 * Gets the list of all notifications to display for the logged-in user.
 *
 * @returns Promise<ApiNotificationsResponse>
 */
export const getNotifications = async () => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.get<ApiNotificationsResponse>(
    AUTH_ENDPOINTS.GET_NOTIFICATIONS,
    config,
  );
  checkAndHandleError(response, 'getNotifications');
  return ApiNotificationsResponseSchema.parse(response);
};

// =============================================================================
// GATE ACCESS
// =============================================================================

/**
 * Gets the list of access code to display for the logged-in user.
 *
 * @returns Promise<ApiAccessCodeResponse>
 */
export const getAccessCode = async () => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.get<ApiAccessCodeResponse>(
    AUTH_ENDPOINTS.GET_ACCESS_CODE,
    config,
  );
  checkAndHandleError(response, 'getAccessCode');
  return ApiAccessCodeResponseSchema.parse(response);
};

export const getGateOptions = async (data: GetGateOptionsRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiGateOptionsResponse>(
    AUTH_ENDPOINTS.GET_GATE_OPTIONS,
    data,
    config,
  );
  checkAndHandleError(response, 'getGateOptions');
  return ApiGateOptionsResponseSchema.parse(response);
};

export const openGatePTI = async (data: OpenGateRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.OPEN_GATE_PTI,
    data,
    config,
  );
  checkAndHandleError(response, 'openGatePTI');
  return BaseApiResponseSchema.parse(response);
};

export const getNokeLocks = async (data: GetNokeLocksRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiNokeLockResponse>(
    AUTH_ENDPOINTS.NOKE_GET_LOCKS,
    data,
    config,
  );
  return ApiNokeLocksResponseSchema.parse(response);
};

export const getNokeUnlockCommand = async (
  data: GetNokeUnlockCommandRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiNokeUnlockCommandResponse>(
    AUTH_ENDPOINTS.NOKE_GET_LOCKS,
    data,
    config,
  );
  return ApiNokeUnlockCommandResponseSchema.parse(response);
};

export const unlockNokeOverNetwork = async (
  data: NokeUnlockOverNetworkRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiNokeGenericResponse>(
    AUTH_ENDPOINTS.NOKE_UNLOCK_OVER_NETWORK,
    data,
    config,
  );
  return ApiNokeGenericResponseSchema.parse(response);
};

export const updateNokeOnsiteStatus = async (
  data: NokeUpdateOnsiteStatusRequest,
) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<ApiNokeGenericResponse>(
    AUTH_ENDPOINTS.NOKE_UNLOCK_OVER_NETWORK,
    data,
    config,
  );
  return ApiNokeGenericResponseSchema.parse(response);
};

// =============================================================================
// NON AUTH REQUESTS
// =============================================================================

/**
 * Retrieves the list of states/provinces that have facilities.
 *
 * @returns Promise<ApiStateResponse>
 */
export const getStates = async () => {
  const response = await request.get<ApiStateResponse>(
    NON_AUTH_ENDPOINTS.GET_STATES,
  );
  // checkAndHandleError(response, 'getStates');
  return ApiStateResponseSchema.parse(response);
};

/**
 * Retrieves the list of facilities in a given state/province.
 *
 * @returns Promise<ApiLocationsInStateResponse>
 */
export const getLocationsInState = async (stateCode: string) => {
  const config = {
    params: { stateCode },
  };
  const response = await request.get<ApiLocationsInStateResponse>(
    NON_AUTH_ENDPOINTS.GET_LOCATIONS_IN_STATE,
    config,
  );
  checkAndHandleError(response, 'getLocationsInState');
  return ApiLocationsInStateResponseSchema.parse(response);
};

/**
 * Request to initiate the password reset process.
 *
 * @returns Promise<BaseApiResponse>
 */
export const requestPasswordReset = async (data: PasswordResetRequest) => {
  const response = await request.post<BaseApiResponse>(
    NON_AUTH_ENDPOINTS.PASSWORD_RESET,
    data,
  );
  checkAndHandleError(response, 'requestPasswordReset');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Register new online account.
 *
 * @returns Promise<BaseApiResponse>
 */
export const registerAccount = async (data: RegisterAccountRequest) => {
  const response = await request.post<BaseApiResponse>(
    NON_AUTH_ENDPOINTS.REGISTER_ACCOUNT,
    data,
  );
  checkAndHandleError(response, 'registerAccount');
  return BaseApiResponseSchema.parse(response);
};

/**
 *  Look up storage account number
 *
 * @returns Promise<BaseApiResponse>
 */
export const findAccount = async (data: FindAccountRequest) => {
  const response = await request.post<BaseApiResponse>(
    NON_AUTH_ENDPOINTS.FIND_ACCOUNT,
    data,
  );
  checkAndHandleError(response, 'findAccount');
  return BaseApiResponseSchema.parse(response);
};

/**
 * For disable autopay.
 *
 * @returns Promise<BaseApiResponse>
 */
export const disableAutoPay = async (data: DisableAutoPayRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.DISABLE_AUTO_PAY,
    data,
    config,
  );
  checkAndHandleError(response, 'disableAutoPay');
  return BaseApiResponseSchema.parse(response);
};

/**
 * Request for send payment receipt.
 *
 * @returns Promise<BaseApiResponse>
 */
export const sendPaymentReceipt = async (data: SendPaymentReceiptRequest) => {
  const config = {
    headers: {
      ...generateAuthHeaderConfig(),
    },
  };
  const response = await request.post<BaseApiResponse>(
    AUTH_ENDPOINTS.SEND_PAYMENT_RECEIPT,
    data,
    config,
  );
  checkAndHandleError(response, 'sendPaymentReceipt');
  return BaseApiResponseSchema.parse(response);
};
