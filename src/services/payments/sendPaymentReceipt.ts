import {
  BaseApiResponse,
  BaseApiResponseSchema,
} from '@/types/schemas/apiResponse';
import { SendPaymentReceiptRequest } from '@/types/schemas/request';
import { sendPaymentReceipt } from '../api';

export default async (
  reqData: SendPaymentReceiptRequest,
): Promise<BaseApiResponse> => {
  const response = await sendPaymentReceipt(reqData);
  return BaseApiResponseSchema.parse(response);
};
