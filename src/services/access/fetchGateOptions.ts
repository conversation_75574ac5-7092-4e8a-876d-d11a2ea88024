import {
  ApiGateOptions,
  ApiGateOptionsResponseSchema,
} from '@/types/schemas/gateOptions';
import { GetGateOptionsRequest } from '@/types/schemas/request';
import { getGateOptions } from '../api';

export default async (
  getGateOptionsRequest: GetGateOptionsRequest,
): Promise<ApiGateOptions[]> => {
  const response = await getGateOptions(getGateOptionsRequest);
  const parsedResponse = ApiGateOptionsResponseSchema.parse(response);
  return parsedResponse.gateDeviceOptions;
};
