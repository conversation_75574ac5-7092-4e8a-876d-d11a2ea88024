{"Success": true, "AccountDisabled": false, "Locations": [{"Image": {"Url": "/media/ggmng5pk/smartstopselfstorage-lahabraca-580eastlambert-hero-002.jpg?width=450&height=312&format=jpg&quality=60&slimmage=true&rnd=133310550719670000", "AltText": "Location"}, "LocationId": 1, "OfficeHours": [{"Day": "Mon", "Hours": "9:30 AM - 6:00 PM"}, {"Day": "<PERSON><PERSON>", "Hours": "9:30 AM - 6:00 PM"}, {"Day": "Wed", "Hours": "9:30 AM - 6:00 PM"}, {"Day": "<PERSON>hu", "Hours": "9:30 AM - 6:00 PM"}, {"Day": "<PERSON><PERSON>", "Hours": "9:30 AM - 6:00 PM"}, {"Day": "Sat", "Hours": "9:00 AM - 4:00 PM"}, {"Day": "Sun", "Hours": "10:00 AM - 2:00 PM"}], "GateHours": [{"Day": "Mon", "Hours": "6:00AM - 10:00PM"}, {"Day": "<PERSON><PERSON>", "Hours": "6:00AM - 10:00PM"}, {"Day": "Wed", "Hours": "6:00AM - 10:00PM"}, {"Day": "<PERSON>hu", "Hours": "6:00AM - 10:00PM"}, {"Day": "<PERSON><PERSON>", "Hours": "6:00AM - 10:00PM"}, {"Day": "Sat", "Hours": "6:00AM - 10:00PM"}, {"Day": "Sun", "Hours": "6:00AM - 10:00PM"}], "Address": "580 E Lambert Rd", "City": "La Habra", "State": "CA", "Zip": "90631", "Phone": "(*************", "Rating": 4.8, "NewReviewUrl": "https://search.google.com/local/writereview?placeid=ChIJLT64tqQq3YARbPAtSmFVY5k", "Units": [{"FacilityId": 1, "AccountId": ***************, "RentalId": ***************, "UnitNumber": "1080", "UnitSize": "4' x 10'", "NextPaymentDue": "06/27/2024", "Autopay": false, "NoPaymentReason": "", "NextPayment": "104.0000", "UnitTotal": 104, "Vacated": false, "CanMakePayment": true, "IsLate": false, "NextPaymentDueValue": "2024-06-27T00:00:00Z", "PaidThruDate": "2024-06-27T00:00:00Z", "LocationPhoneText": "(*************", "LocationPhoneDigits": "**********"}, {"FacilityId": 1, "AccountId": ***************, "RentalId": ***************, "UnitNumber": "2148", "UnitSize": "10' x 10'", "NextPaymentDue": "NA", "Autopay": false, "NoPaymentReason": "(Unoccupied)", "NextPayment": "56.54", "UnitTotal": 56.54, "Vacated": true, "CanMakePayment": false, "IsLate": false, "NextPaymentDueValue": "2024-04-24T00:00:00Z", "PaidThruDate": "2024-04-24T00:00:00Z", "LocationPhoneText": "(*************", "LocationPhoneDigits": "**********"}]}]}