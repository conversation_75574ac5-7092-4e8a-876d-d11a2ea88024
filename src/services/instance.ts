import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import Config from 'react-native-config';

const prefixUrl = `${
  Config.API_URL ? Config.API_URL : 'https://staging.smartstopselfstorage.com'
}`;

const instance = axios.create({
  baseURL: prefixUrl,
});

const run = async (promise: Promise<AxiosResponse>) => {
  try {
    const result = await promise;
    return result;
  } catch (error) {
    const err = error as AxiosError;
    console.error(`Request Action: ${err.config?.url}
      Error Message: ${err.message}
      Error Data: ${JSON.stringify(err.response?.data)}`);
    if (err.status === 401) {
      // TODO: possibly make a requst here to attempt a refresh token request
    }
    throw error;
  }
};

export const get = async <T>(url: string, config?: AxiosRequestConfig) => {
  const promise = instance.get(url, config);
  const result = await run(promise);
  return result.data as T;
};

export const put = async <T>(
  url: string,
  data?: unknown,
  config?: AxiosRequestConfig,
) => {
  const promise = instance.put(url, data, config);
  const result = await run(promise);
  return result.data as T;
};

export const post = async <T>(
  url: string,
  data?: unknown,
  config?: AxiosRequestConfig,
) => {
  const promise = instance.post(url, data, config);
  const result = await run(promise);
  return result.data as T;
};

export const del = async <T>(url: string, config?: AxiosRequestConfig) => {
  const promise = instance.delete(url, config);
  const result = await run(promise);
  return result.data as T;
};
