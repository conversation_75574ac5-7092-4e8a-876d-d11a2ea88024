import { TextStyle } from 'react-native';
import type { FontColors, FontSizes } from '@/types/theme/fonts';
import type { UnionConfiguration } from '@/types/theme/config';
import { config } from '@/theme/_config';

export const generateFontColors = (configuration: UnionConfiguration) => {
  return Object.entries(configuration.fonts.colors ?? {}).reduce(
    (acc, [key, value]) => {
      return Object.assign(acc, {
        [`${key}`]: {
          color: value,
        },
      });
    },
    {} as FontColors,
  );
};

export const generateFontSizes = () => {
  return config.fonts.sizes.reduce((acc, size) => {
    return Object.assign(acc, {
      [`size_${size}`]: {
        fontSize: size,
      },
    });
  }, {} as FontSizes);
};

/**
 * 100 – Thin.
200 – Extra Light (Ultra Light)
300 – Light.
400 – Normal.
500 – Medium.
600 – Semi Bold (Demi Bold)
700 – Bold.
800 – Extra Bold (Ultra Bold)
 */

export const staticFontStyles = {
  thin: {
    fontWeight: '100',
  },
  extraLight: {
    fontWeight: '200',
  },
  light: {
    fontWeight: '300',
  },
  medium: {
    fontWeight: '500',
  },
  semiBold: {
    fontWeight: '600',
  },
  extraBold: {
    fontWeight: '800',
  },
  bold: {
    fontWeight: 'bold',
  },
  normal: {
    fontWeight: 'normal',
  },
  uppercase: {
    textTransform: 'uppercase',
  },
  capitalize: {
    textTransform: 'capitalize',
  },
  underline: {
    textDecorationLine: 'underline',
  },
  alignCenter: {
    textAlign: 'center',
  },
  appFontRegular: {
    fontFamily: 'FilsonPro-Regular',
  },
  appFontBold: {
    fontFamily: 'FilsonPro-Bold',
  },
  appFontMedium: {
    fontFamily: 'FilsonPro-Medium',
  },
} as const satisfies Record<string, TextStyle>;
