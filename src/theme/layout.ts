import { ViewStyle } from 'react-native';

import { config } from '@/theme/_config';
import type { LayoutPercents, LayoutSizes } from '@/types/theme/layout';

export const generateLayoutSizes = () => {
  return config.layout.sizes.reduce((acc, size) => {
    return Object.assign(acc, {
      [`w_${size}`]: {
        width: size,
      },
      [`h_${size}`]: {
        height: size,
      },
    });
  }, {} as LayoutSizes);
};

export const generateLayoutPercents = () => {
  return config.layout.percents.reduce((acc, percent) => {
    return Object.assign(acc, {
      [`percentW_${percent}`]: {
        width: `${percent}%`,
      },
      [`percentH_${percent}`]: {
        height: `${percent}%`,
      },
    });
  }, {} as LayoutPercents);
};

export const staticLayoutStyles = {
  col: {
    flexDirection: 'column',
  },
  colReverse: {
    flexDirection: 'column-reverse',
  },
  wrap: {
    flexWrap: 'wrap',
  },
  row: {
    flexDirection: 'row',
  },
  rowReverse: {
    flexDirection: 'row-reverse',
  },
  itemsCenter: {
    alignItems: 'center',
  },
  itemsStart: {
    alignItems: 'flex-start',
  },
  itemsStretch: {
    alignItems: 'stretch',
  },
  itemsEnd: {
    alignItems: 'flex-end',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  justifyAround: {
    justifyContent: 'space-around',
  },
  justifyBetween: {
    justifyContent: 'space-between',
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  justifyStart: {
    justifyContent: 'flex-start',
  },
  selfCenter: {
    alignSelf: 'center',
  },
  justifySpaceEvenly: {
    justifyContent: 'space-evenly',
  },
  alignContentCenter: {
    alignContent: 'center',
  },
  /* Sizes Layouts */
  flex_1: {
    flex: 1,
  },
  flexGrow_1: {
    flexGrow: 1,
  },
  fullWidth: {
    width: '100%',
  },
  fullHeight: {
    height: '100%',
  },
  /* Positions */
  relative: {
    position: 'relative',
  },
  absolute: {
    position: 'absolute',
  },
  top0: {
    top: 0,
  },
  bottom0: {
    bottom: 0,
  },
  left0: {
    left: 0,
  },
  right0: {
    right: 0,
  },
  z1: {
    zIndex: 1,
  },
  z10: {
    zIndex: 10,
  },
  z999: {
    zIndex: 999,
  },
  opacity_0: {
    opacity: 0,
  },
} as const satisfies Record<string, ViewStyle>;

export default staticLayoutStyles;
