import { DarkTheme, DefaultTheme } from '@react-navigation/native';

import type { ThemeConfiguration } from '@/types/theme/config';

const colorsLight = {
  red500: '#C13333',
  gray10: '#F8F9F9',
  gray800: '#303030',
  gray400: '#4D4D4D',
  gray200: '#A1A1A1',
  gray100: '#DFDFDF',
  gray50: '#EFEFEF',
  purple500: '#44427D',
  purple100: '#E1E1EF',
  purple50: '#1B1A23',
  white: '#FFFFFF',
  transparent: 'transparent',
  lightGreen: '#EDF4EF',
  olive: '#497C5C',
  lightGray: '#D5DDE2',
  lightCharcoal: '#687176',
  midCharcoal: '#51585C',
  bluePrimary: '#006CAF',
  alertOrange: '#FAAE4C',
  cherry: '#9C0A0A',
  primary: '#004977',
  lightCherry: '#FAE8E8',
  charcoal: '#161718',
  blueLight: '#CCE5F3',
  blue: '#0077B8',
  blue10: '#E6F2F9',
  blueLogo: '#0089CE',
  blue40: '#99CBE7',
  springGrass20: '#DBEAE0',
  springGrass40: '#B7D5C2',
  springGrass60: '#92BFA3',
  argentGray: '#C0C0C0',
  dimGray: '#6F6F74',
  borderGray: '#909093',
  deepBlue: '#357BF6',
  darkRed: '#EB4D3D',
  limeGreen: '#A4D300',
} as const;

const colorsDark = {
  red500: '#C13333',
  gray10: '#F8F9F9',
  gray800: '#E0E0E0',
  gray400: '#969696',
  gray200: '#BABABA',
  gray100: '#000000',
  gray50: '#EFEFEF',
  purple500: '#A6A4F0',
  purple100: '#252732',
  purple50: '#1B1A23',
  white: '#FFFFFF',
  transparent: 'transparent',
  lightGreen: '#EDF4EF',
  olive: '#497C5C',
  lightGray: '#D5DDE2',
  lightCharcoal: '#687176',
  midCharcoal: '#51585C',
  bluePrimary: '#006CAF',
  alertOrange: '#FAAE4C',
  cherry: '#9C0A0A',
  primary: '#004977',
  lightCherry: '#FAE8E8',
  charcoal: '#161718',
  primary_80: '#3398CF',
  blueLight: '#CCE5F3',
  blue: '#0077B8',
  blue10: '#E6F2F9',
  blueLogo: '#0089CE',
  blue40: '#99CBE7',
  springGrass20: '#DBEAE0',
  springGrass40: '#B7D5C2',
  springGrass60: '#92BFA3',
  argentGray: '#C0C0C0',
  dimGray: '#6F6F74',
  borderGray: '#909093',
  deepBlue: '#357BF6',
  darkRed: '#EB4D3D',
  limeGreen: '#A4D300',
} as const;

const sizes = [
  0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 14, 15, 16, 17, 18, 20, 26, 24, 32, 35, 40,
  50, 56, 60, 64, 65, 72, 76, 80, 90, 100, 120, 137, 140, 160, 200, 208, 230,
  250, 252, 280,
] as const;

export const config = {
  layout: {
    sizes,
    percents: [
      0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 48, 50, 55, 60, 65, 70, 75, 80, 85,
      90, 95, 252, 280,
    ],
  },
  colors: colorsLight,
  fonts: {
    sizes,
    colors: colorsLight,
  },
  gutters: sizes,
  backgrounds: colorsLight,
  borders: {
    widths: [0, 1, 2],
    radius: [4, 8, 9, 10, 12, 13, 15, 16, 20, 24, 25, 28, 32, 50, 126, 140],
    colors: colorsLight,
  },
  navigationColors: {
    ...DefaultTheme.colors,
    background: colorsLight.blue10,
    card: colorsLight.white,
    notification: colorsDark.alertOrange,
  },
  variants: {
    dark: {
      colors: colorsDark,
      fonts: {
        colors: colorsDark,
      },
      backgrounds: colorsDark,
      navigationColors: {
        ...DarkTheme.colors,
        background: colorsDark.blue10,
        card: colorsDark.white,
        notification: colorsDark.alertOrange,
      },
    },
  },
} as const satisfies ThemeConfiguration;
