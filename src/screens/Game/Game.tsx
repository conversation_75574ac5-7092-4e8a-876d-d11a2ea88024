import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';

const windowHeight = Dimensions.get('window').height;
const boxHeight = 50; // Height of each box

interface Box {
  id: number;
  position: number;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  box: {
    width: 50,
    height: boxHeight,
    backgroundColor: 'blue',
    position: 'absolute',
  },
  gameOverText: {
    fontSize: 24,
    color: 'red',
    position: 'absolute',
    top: 20,
  },
});

function Game() {
  const [stack, setStack] = useState<Box[]>([]); // Positions of stacked boxes
  const [currentBox, setCurrentBox] = useState<Box | null>(null); // Current falling box
  const [gameOver, setGameOver] = useState<boolean>(false);

  const startBox = (): void => {
    // Reset if game over
    if (gameOver) {
      setStack([]);
      setGameOver(false);
    }
    // Initial position for the new falling box
    const newBox: Box = { id: Math.random(), position: 0 };
    setCurrentBox(newBox);

    // Game loop for the current box
    const interval = setInterval(() => {
      setCurrentBox(current => {
        if (current) {
          const newPosition = current.position + 10; // Move box down by 10 units
          if (newPosition > windowHeight - boxHeight) {
            clearInterval(interval); // Stop the box
            setStack(prevStack => [...prevStack, current]); // Add box to the stack
            startBox(); // Start a new box
          }
          return { ...current, position: newPosition };
        }
        return current;
      });
    }, 100); // Update position every 100 ms
  };

  useEffect(() => {
    startBox();
  }, []);

  const handleTap = (): void => {
    // Stop the current box and add it to the stack
    if (currentBox) {
      setStack(prevStack => [...prevStack, currentBox]);
      startBox();
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handleTap}
      activeOpacity={1}
    >
      {stack.map(box => (
        <View key={box.id} style={[styles.box, { bottom: box.position }]} />
      ))}
      {currentBox && (
        <View style={[styles.box, { bottom: currentBox.position }]} />
      )}
      {gameOver && <Text style={styles.gameOverText}>Game Over</Text>}
    </TouchableOpacity>
  );
}

export default Game;
