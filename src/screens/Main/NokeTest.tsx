import { GateSvg } from '@/assets/svg';
import SSDoorButton, { SSDore } from '@/components/atoms/SSDoorButton';
import SSAlertDialog from '@/components/molecules/SSAlertDialog';
import NokeModule from '@/noke/NokeModule';
import { getNokeLocks } from '@/services/api';
import fetchLocations from '@/services/locations/fetchLocations';
import { NokeLock } from '@/types/schemas/Noke';
import {
  GetNokeLocksRequest,

} from '@/types/schemas/request';
import { DoorState, GateSystemType } from '@/utils/commonFunctions';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { FlatList, Text, View } from 'react-native';

function NokeTest() {
  const [nokeLocks, setNokeLocks] = useState<NokeLock[]>([]);


  const { data } = useQuery({
    queryKey: ['home_locations'],
    queryFn: () => {
      return fetchLocations();
    },
    refetchOnWindowFocus: false,
  });

  const GetNokeLocksList = useMutation({
    mutationFn: (lockRequest: GetNokeLocksRequest) => {
      return getNokeLocks(lockRequest);
    },
    onSuccess: nokeLocksData => {
      const nokeLockArr: NokeLock[] = [];
      nokeLocksData.nokeResponse?.data?.units?.forEach(unit => {
        unit.locks?.forEach(lock => nokeLockArr.push(lock));
      });
      setNokeLocks(nokeLockArr);
    },
    onError: e => {
      console.error(e);
    },
  });

  const callGetNokeLocksList = async (lockRequest: GetNokeLocksRequest) => {
    GetNokeLocksList.reset();
    await GetNokeLocksList.mutateAsync(lockRequest);
  };



  useEffect(() => {
    setNokeLocks([]);
    if (!data) return;

    NokeModule.initNoke();
    const lockRequest: GetNokeLocksRequest = {
      facilityId: data[0].units[0].facilityId,
      tenantId: data[0].units[0].accountId,
    };
    void callGetNokeLocksList(lockRequest);
  }, [data]);

  if (!data || !nokeLocks) {
    return (
      <View style={{ padding: 40, gap: 20 }}>
        <Text>Loading</Text>
      </View>
    );
  }

  const renderGate = ({ item }: { item: SSDore }) => {
    if (data) {
      return (
        <SSDoorButton
          item={item}
          icon={GateSvg}
          facilityId={data[0].units[0].facilityId}
          tenantId={data[0].units[0].accountId}
          accessCode=""
          defultDoorState={DoorState.OPEN}
          progress={0}
          buttonOnPress={() => {}}
          isGateButtonOpening={() => {}}
          onError={undefined}
        />
      );
    }
    return null;
  };

  const gates: SSDore[] = nokeLocks.map(lock => ({
    name: lock.name,
    id: lock.mac,
    type: GateSystemType.NOKE,
    nokeOfflineKey: lock.offlineKey,
    nokeOfflineKeyExpiration: lock.offlineKeyObj.offlineExpiration,
    nokeOfflineUnlockCommand: lock.unlockCmd,
    nokeScheduledOfflineUnlockCommand: lock.scheduledUnlockCmd,
  }));

  return (
    <View style={{ padding: 40, gap: 20 }}>
      <FlatList
        data={gates}
        renderItem={renderGate}
        keyExtractor={item => item.id.toString()}
        scrollEnabled={false}
      />

    </View>
  );
}

export default NokeTest;
