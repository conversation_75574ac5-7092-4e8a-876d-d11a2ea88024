import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import SSButton from '@/components/atoms/SSButton/SSButton';
import SSErrorView from '@/components/atoms/SSErrorView';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { submitSupportForm } from '@/services/api';
import useTheme from '@/theme/hooks/useTheme';
import { AccountStackScreenProps } from '@/types/navigation';
import { SubmitSupportFormRequest } from '@/types/schemas/request';
import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import { formatPhoneNumber } from '@/utils/commonFunctions';
import { isValidEmail } from '@/utils/validate';
import { useMutation } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Linking,
  Modal,
  StatusBar,
  Text,
  View,
} from 'react-native';

function Support({ navigation }: AccountStackScreenProps) {
  const { fonts, layout, gutters, borders, backgrounds } = useTheme();
  const [name, setName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorView, setErrorView] = useState<boolean>(false);

  const callSupportRequestApi = useMutation({
    mutationFn: (data: SubmitSupportFormRequest) => {
      return submitSupportForm(data);
    },
    onSuccess: () => {
      setIsLoading(false);
      navigation.goBack();
    },
    onError: async () => {
      await logEventWithStatus(Events.supportForm, EventStatus.failed);
      setErrorView(true);
    },
    onSettled: async () => {
      await logEventWithStatus(Events.supportForm, EventStatus.completed);
      setIsLoading(false);
    },
  });

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  const onSubmitClick = async () => {
    await logEventWithStatus(Events.supportForm, EventStatus.initiated);
    setIsLoading(true);
    setErrorView(false);
    const submitSupportFormRequest: SubmitSupportFormRequest = {
      name,
      email,
      phone,
      message,
    };
    callSupportRequestApi.reset();
    await callSupportRequestApi.mutateAsync(submitSupportFormRequest);
  };

  const validateField = () => {
    if (!name) {
      return false;
    }

    if (!email || !isValidEmail(email)) {
      return false;
    }

    if (!phone || phone.length < 12) {
      return false;
    }

    if (!message) {
      return false;
    }

    return true;
  };

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={[layout.flexGrow_1]}
      bounces={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={[layout.col, layout.flex_1]}>
        <View
          style={[
            { height: 316 },
            backgrounds.bluePrimary,
            borders.bottomLeftRounded_16,
            borders.bottomRightRounded_16,
          ]}
        />
        <View
          style={[
            layout.flex_1,
            layout.fullWidth,
            { marginTop: -316 },
            gutters.padding_16,
          ]}
        >
          <View
            style={[layout.flex_1, gutters.gap_16, gutters.marginBottom_10]}
          >
            <Text style={[fonts.appFontBold, fonts.white, fonts.size_32]}>
              Support
            </Text>
            <Text style={[fonts.appFontRegular, fonts.white, fonts.size_14]}>
              {
                'No need to stress! We\'re here to make your self-storage experience easy and hassle-free!\n\n'
              }
              <Text style={[fonts.appFontRegular]}>{'Customer Service\n'}</Text>
              <Text
                style={[fonts.appFontBold, fonts.white, fonts.size_14]}
                onPress={() => Linking.openURL('tel:8889778672')}
              >
                {'(*************\n\n'}
              </Text>
              <Text style={[fonts.appFontRegular]}>{'Email\n'}</Text>
              <Text
                style={[fonts.appFontBold, fonts.white, fonts.size_14]}
                onPress={() => Linking.openURL('mailto:<EMAIL>')}
              >
                <EMAIL>
              </Text>
            </Text>
            <View
              style={[
                layout.col,
                gutters.gap_16,
                gutters.paddingTop_24,
                gutters.paddingBottom_24,
                gutters.paddingLeft_16,
                gutters.paddingRight_16,
                backgrounds.white,
                borders.rounded_16,
              ]}
            >
              {errorView && (
                <SSErrorView
                  title="validation errors"
                  description="All fileds are required"
                />
              )}

              <SSTextInput
                label="Name"
                value={name}
                isError={errorView}
                onChangeText={(value: string) => {
                  setName(value);
                }}
              />
              <SSTextInput
                label="Email"
                value={email}
                isError={errorView}
                keyboardType="email-address"
                onChangeText={(value: string) => {
                  setEmail(value);
                }}
              />
              <SSTextInput
                label="Phone Number*"
                maxLength={20}
                isError={errorView}
                keyboardType="numeric"
                value={phone}
                onChangeText={(value: string) => {
                  setPhone(formatPhoneNumber(value));
                }}
              />

              <SSTextInput
                label="Write note here..."
                value={message}
                isError={errorView}
                multiline
                numberOfLines={5}
                onChangeText={(value: string) => {
                  setMessage(value);
                }}
              />

              <SSButton
                onPress={() => {
                  void onSubmitClick();
                }}
                title="Submit"
                accessibilityLabel="Submit"
                variant={validateField() ? 'dark' : 'light'}
                disabled={!validateField()}
              />
            </View>
          </View>
        </View>
        {isLoading && (
          <Modal
            transparent
            animationType="none"
            visible={isLoading}
            onRequestClose={() => null}
          >
            <View
              style={[
                layout.flex_1,
                layout.itemsCenter,
                layout.justifyCenter,
                { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
              ]}
            >
              <View
                style={[
                  backgrounds.white,
                  borders.rounded_12,
                  { height: 100, width: 100 },
                  layout.itemsCenter,
                  layout.justifyCenter,
                ]}
              >
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            </View>
          </Modal>
        )}
      </View>
    </KeyboardAwareScrollView>
  );
}

export default Support;