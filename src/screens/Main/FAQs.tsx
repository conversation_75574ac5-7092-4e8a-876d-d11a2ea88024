import React, { useEffect, useState } from 'react';
import {
  FlatList,
  View,
  Text,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useTheme } from '@/theme';
import { useMutation } from '@tanstack/react-query';
import { ActivityLoader } from '@/components/atoms';
import Adapter from '@/adapters/Adapter';
import getSecondaryContent from '@/services/SecondaryContent/getSecondaryContent';
import {
  ApiSecondaryContentResponse,
  FAQItem,
  SecondaryContent,
} from '@/types/schemas/secondaryContent';
import { SecondaryContentAdapter } from '@/adapters/SecondaryContent/SecondaryContentAdapter';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { MinusIconSVG, PlusIconSVG } from '@/assets/svg';
import { HomeStackScreenProps } from '@/types/navigation';

type ListItem = {
  item: FAQItem;
  index: number;
};

function FAQs({ navigation }: HomeStackScreenProps) {
  const { layout, gutters, fonts, backgrounds, borders } = useTheme();
  const [faqData, setFaqData] = useState([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  const getSecondaryContentUrlMutation = useMutation({
    mutationFn: () => {
      return getSecondaryContent();
    },
    onSuccess: data => {
      const obj: SecondaryContent = Adapter.from(data).to(
        (item: ApiSecondaryContentResponse) =>
          new SecondaryContentAdapter(item).adapt(),
      );
      setIsLoading(false);
      setFaqData(obj.faqItems);
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.secondaryContent, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      setIsLoading(false);
      if (data) {
        await logEventWithStatus(
          Events.secondaryContent,
          EventStatus.completed,
        );
      }
    },
  });

  const callApiGetSecondaryContent = async () => {
    setIsLoading(true);
    getSecondaryContentUrlMutation.reset();
    await getSecondaryContentUrlMutation.mutateAsync();
  };

  useEffect(() => {
    void callApiGetSecondaryContent();
  }, []);

  const handleExpendCollepseAction = (index: number) => {
    if (index === selectedIndex) {
      setSelectedIndex(-1);
    } else {
      setSelectedIndex(index);
    }
  };

  const renderListItem = ({ item, index }: ListItem) => {
    const isExpended = index === selectedIndex;
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={[
          gutters.marginHorizontal_16,
          gutters.marginTop_8,
          gutters.marginBottom_8,
          backgrounds.white,
          borders.rounded_12,
          layout.row,
        ]}
        onPress={() => handleExpendCollepseAction(index)}
      >
        <View
          style={[
            gutters.marginHorizontal_16,
            gutters.marginVertical_16,
            backgrounds.white,
            borders.rounded_12,
            layout.flex_1,
          ]}
        >
          <Text style={[fonts.appFontMedium, fonts.size_16, fonts.charcoal]}>
            {item?.header}
          </Text>
          {isExpended ? (
            <Text
              style={[
                gutters.marginTop_10,
                fonts.appFontMedium,
                fonts.size_14,
                fonts.lightCharcoal,
              ]}
            >
              {item?.answer}
            </Text>
          ) : null}
        </View>
        <TouchableOpacity
          style={[gutters.marginTop_16, gutters.marginRight_16]}
          accessibilityLabel={item?.header}
          onPress={() => handleExpendCollepseAction(index)}
        >
          {isExpended ? <MinusIconSVG /> : <PlusIconSVG />}
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View
      style={[
        layout.h_100,
        backgrounds.bluePrimary,
        layout.justifyCenter,
        borders.bottomLeftRounded_16,
        borders.bottomRightRounded_16,
        gutters.paddingLeft_16,
      ]}
    >
      <Text style={[fonts.appFontBold, fonts.size_32, fonts.white]}>FAQs</Text>
    </View>
  );

  return (
    <View style={[layout.flex_1, layout.col, backgrounds.blue10]}>
      <FlatList
        bounces={false}
        data={faqData}
        renderItem={renderListItem}
        style={[layout.flex_1, gutters.paddingBottom_40]}
        ListHeaderComponent={renderHeader}
      />
      <ActivityLoader isLoading={isLoading} />
    </View>
  );
}

export default FAQs;