import React, { useState } from 'react';
import {
  FlatList,
  View,
  RefreshControl,
  Text,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '@/theme';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ActivityLoader, PaymentHistoryCard } from '@/components/atoms';
import Adapter from '@/adapters/Adapter';
import {
  PaymentHistoryItem,
  PaymentsHistory,
} from '@/types/schemas/paymentHistory';
import { PaymentsAdapter } from '@/adapters/Payments/PaymentsAdapter';
import { fetchPaymentHistory, sendPaymentReceipt } from '@/services/payments';
import { Unit } from '@/types/schemas/unit';
import {
  PaymentHistoryRequest,
  SendPaymentReceiptRequest,
} from '@/types/schemas/request';
import fetchLocations from '@/services/locations/fetchLocations';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { DateTime } from 'luxon';

type ListItem = {
  item: PaymentHistoryItem;
};

function PaymentHistory() {
  const { layout, gutters, fonts, backgrounds } = useTheme();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const paymentUnitInfoQuery = useQuery({
    queryKey: ['fetch_units_payment'],
    queryFn: () => {
      return fetchLocations();
    },
    select: data => {
      const allUnits: [Unit] = [];
      data.forEach(loc => {
        allUnits.push(...[...loc.units]);
      });
      return allUnits;
    },
    refetchOnWindowFocus: true,
  });

  const getRequestedArr: PaymentHistoryRequest =
    paymentUnitInfoQuery?.data?.filter(unit => {
      return { accountId: unit.accountId, rentalId: unit.rentalId };
    });

  const paymentHistoryQuery = useQuery({
    queryKey: ['paymentHistory'],
    queryFn: () => {
      return fetchPaymentHistory(getRequestedArr);
    },
    select: data => {
      return Adapter.from(data).to((item: PaymentsHistory) => {
        return new PaymentsAdapter(item).adapt();
      });
    },
  });

  const sendPaymentReceiptMutation = useMutation({
    mutationFn: (item: PaymentHistoryItem) => {
      const reqData: SendPaymentReceiptRequest = {
        culture: 'en-us',
        accountId: item.accountId,
        rentalId: item.rentalId,
        paymentReceiptNumber: item.paymentReceiptNumber,
      };

      return sendPaymentReceipt(reqData);
    },
    onSuccess: data => {
      setIsLoading(false);
      if (data.success) {
        Alert.alert(
          'SmartStop',
          'Payment receipt was successfully sent to registered email.',
        );
      }
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.requestReceipt, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        setIsLoading(false);
        await logEventWithStatus(Events.requestReceipt, EventStatus.completed);
      }
    },
  });

  const callSendPaymentReceiptApi = async (item: PaymentHistoryItem) => {
    setIsLoading(true);
    sendPaymentReceiptMutation.reset();
    await logEventWithStatus(Events.requestReceipt, EventStatus.initiated);
    await sendPaymentReceiptMutation.mutateAsync(item);
  };

  const renderListItem = ({ item }: ListItem) => {
    return (
      <PaymentHistoryCard
        onPress={() => {}}
        item={item}
        onEmailReceiptPress={() => void callSendPaymentReceiptApi(item)}
      />
    );
  };

  const onRefresh = () => {
    void paymentHistoryQuery.refetch();
  };

  const renderHeader = () => (
    <View style={[layout.h_80, layout.fullWidth, layout.justifyCenter]}>
      <Text style={[fonts.appFontBold, fonts.size_32, fonts.white]}>
        Previous Payments
      </Text>
    </View>
  );

  const renderFooter = () =>
    paymentHistoryQuery.data?.items?.length ? (
      <View
        style={[
          layout.h_40,
          layout.fullWidth,
          layout.justifyCenter,
          layout.itemsCenter,
        ]}
      >
        <Text style={[fonts.appFontBold, fonts.size_14, fonts.lightCharcoal]}>
          End of Payment History
        </Text>
      </View>
    ) : null;

  return (
    <View style={[layout.flex_1, layout.col, backgrounds.bluePrimary]}>
      <ScrollView
        contentContainerStyle={[layout.flexGrow_1]}
        scrollEnabled
        refreshControl={
          <RefreshControl
            tintColor={backgrounds.white.backgroundColor}
            refreshing={paymentHistoryQuery.isRefetching}
            onRefresh={onRefresh}
          />
        }
      >
        <View style={[backgrounds.blue10, layout.flex_1]}>
          <View
            style={[
              layout.h_160,
              backgrounds.bluePrimary,
              layout.justifyCenter,
              { borderBottomLeftRadius: 16, borderBottomRightRadius: 16 },
            ]}
          />
          <View
            style={[
              layout.fullWidth,
              layout.fullHeight,
              gutters.paddingHorizontal_16,
              { marginTop: -160 },
            ]}
          >
            <FlatList
              data={paymentHistoryQuery.data?.items.sort(
                (a: PaymentHistoryItem, b: PaymentHistoryItem) => {
                  const dateA = DateTime.fromFormat(a.date, 'MM/dd/yyyy');
                  const dateB = DateTime.fromFormat(b.date, 'MM/dd/yyyy');
                  return dateB - dateA; // Descending order (latest first)
                },
              )}
              renderItem={renderListItem}
              style={[layout.flex_1, gutters.paddingBottom_20]}
              contentContainerStyle={[gutters.gap_10]}
              ListHeaderComponent={renderHeader()}
              ListFooterComponent={renderFooter()}
              scrollEnabled={false}
            />
          </View>
        </View>
      </ScrollView>
      <ActivityLoader isLoading={paymentHistoryQuery.isLoading || isLoading} />
    </View>
  );
}

export default PaymentHistory;
