import React, { useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { useMutation } from '@tanstack/react-query';
import { changePassword } from '@/services/api';
import SSErrorView from '@/components/atoms/SSErrorView';
import { ChangePasswordRequest } from '@/types/schemas/request';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { ActivityLoader } from '@/components/atoms';
import { AccountStackScreenProps } from '@/types/navigation';
import SSSuccessDialog from '@/components/molecules/SSSuccessDialog';

function ChangePassword({ navigation }: AccountStackScreenProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorView, setErrorView] = useState<boolean>(false);
  const [showSuccessDialog, setshowSuccessDialog] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [currentpassword, setCurrentPassword] = useState<string>('');
  const [newpassword, setNewPassword] = useState<string>('');
  const [reEnterpassword, setReEnterPassword] = useState<string>('');

  const { layout, gutters, fonts, backgrounds, borders } = useTheme();

  const callChangePasswordApi = useMutation({
    mutationFn: (data: ChangePasswordRequest) => {
      return changePassword(data);
    },
    onSuccess: () => {
      setIsLoading(false);
      setshowSuccessDialog(true);
    },
    onError: error => {
      setErrorMessage(error.message);
      setErrorView(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const onSubmitClick = async () => {
    const changePasswordRequest: ChangePasswordRequest = {
      oldPassword: currentpassword,
      newPassword: newpassword,
    };
    setIsLoading(true);
    setErrorMessage('');
    setErrorView(false);
    callChangePasswordApi.reset();
    await callChangePasswordApi.mutateAsync(changePasswordRequest);
  };

  const validateField = () => {
    const passwordRegex = /^.{8,}$/;

    // Validate password
    if (!passwordRegex.test(currentpassword)) {
      return false;
    }

    if (!passwordRegex.test(newpassword)) {
      return false;
    }

    if (!passwordRegex.test(reEnterpassword)) {
      return false;
    }

    // Validate confirm password
    if (newpassword !== reEnterpassword) {
      return false;
    }

    return true;
  };

  return (
    <ScrollView contentContainerStyle={[layout.flexGrow_1]}>
      <View style={[layout.col, layout.flex_1]}>
        <View
          style={[
            { height: 208 },
            backgrounds.bluePrimary,
            borders.bottomLeftRounded_16,
            borders.bottomRightRounded_16,
          ]}
        />
        <View
          style={[
            layout.flex_1,
            layout.fullWidth,
            { marginTop: -208 },
            gutters.padding_16,
          ]}
        >
          <View
            style={[layout.flex_1, gutters.gap_16, gutters.marginBottom_10]}
          >
            <Text style={[fonts.appFontBold, fonts.white, fonts.size_32]}>
              Change Password
            </Text>
            <Text style={[fonts.appFontRegular, fonts.white, fonts.size_14]}>
              Please complete the fields below to update your password.
            </Text>
            <View
              style={[
                layout.col,
                gutters.gap_16,
                gutters.paddingTop_24,
                gutters.paddingBottom_24,
                gutters.paddingLeft_16,
                gutters.paddingRight_16,
                backgrounds.white,
                borders.rounded_16,
              ]}
            >
              {errorView && (
                <SSErrorView
                  title="Change Password error"
                  description={errorMessage}
                />
              )}

              <SSTextInput
                label="Current Password*"
                isPassword
                togglePassword
                value={currentpassword}
                isError={errorView}
                onChangeText={(value: string) =>
                  setCurrentPassword(value?.trim() ?? "")
                }
                accessibilityLabel="Current Password"
              />
              <SSTextInput
                label="New Password*"
                isPassword
                togglePassword
                value={newpassword}
                isError={errorView}
                onChangeText={(value: string) => setNewPassword(value?.trim() ?? '')}
                accessibilityLabel="New Password"
              />
              <SSTextInput
                label="Re-enter New Password*"
                isPassword
                togglePassword
                value={reEnterpassword}
                isError={errorView}
                onChangeText={(value: string) =>
                  setReEnterPassword(value?.trim() ?? '')
                }
                accessibilityLabel="Re-enter New Password"
              />

              <SSButton
                onPress={() => {
                  void onSubmitClick();
                }}
                title="Save"
                accessibilityLabel="Save"
                variant={validateField() ? 'dark' : 'light'}
                disabled={!validateField()}
              />
            </View>
          </View>
        </View>
        <ActivityLoader isLoading={isLoading} />
        {showSuccessDialog && (
          <SSSuccessDialog
            onClose={() => {
              // setshowSuccessDialog(false);
              navigation.goBack();
            }}
            buttonTitle="Return to Account"
            title="Success! Your Password Has Been Updated"
            description=""
          />
        )}
      </View>
    </ScrollView>
  );
}

export default ChangePassword;
