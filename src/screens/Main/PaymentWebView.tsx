import React, { useEffect } from 'react';
import { KeyboardAvoidingView } from 'react-native';
import { WebView } from 'react-native-webview';
import { useTheme } from '@/theme';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import { SS_LINKS } from '@/utils/linking';

// Constants
const defaultUri = SS_LINKS.HOME;

interface WebviewProps {
  navigation: NavigationProp<ParamListBase>;
  route: { params: { uri: string; title: string } };
}

function PaymentWebView({ navigation, route }: WebviewProps) {
  const { layout, backgrounds } = useTheme();
  const uri = route.params?.uri ?? defaultUri;
  useEffect(() => {
    navigation.setOptions({
      title: route.params?.title ?? 'WEB',
      headerStyle: {
        backgroundColor: backgrounds.charcoal.backgroundColor,
      },
    });
  }, []);

  return (
    <KeyboardAvoidingView style={layout.flex_1}>
      <WebView
        style={layout.flex_1}
        androidHardwareAccelerationDisabled
        source={{ uri }}
        cacheEnabled={false}
        startInLoadingState
        javaScriptEnabled
        onNavigationStateChange={event => {
          const { url } = event;
          if (url?.includes('/payments')) {
            navigation.goBack();
          }
        }}
      />
    </KeyboardAvoidingView>
  );
}

// Export
export default PaymentWebView;
