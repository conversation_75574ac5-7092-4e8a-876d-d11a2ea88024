import * as React from 'react';
import { Text, TextStyle, View } from 'react-native';
import { AlertSuccess } from '@/assets/svg';
import { SSButton } from '@/components/atoms';
import { useTheme } from '@/theme';
import { PaymentStackScreenProps } from '@/types/navigation';
import { useMutation } from '@tanstack/react-query';
import { fetchMakeAutopayUrl } from '@/services/payments';
import { Unit } from '@/types/schemas/unit';
import { ApiWebViewResponse, WebViewUrl } from '@/types/schemas/webview';
import Adapter from '@/adapters/Adapter';
import { GetAutopayPaymentAdapter } from '@/adapters/Payments/GetAutopayPaymentAdapter';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';

function AutoPayTurnOffSuccess({ navigation, route }: PaymentStackScreenProps) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const unitInfo = route?.params?.unitInfo as Unit;

  const transparentBackgroundStyle = {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  };
  const otherTextStyles: TextStyle = {
    textAlign: 'center',
  };

  const dismissAlert = () => {
    navigation.goBack();
  };

  const getAutoPayUrlMutation = useMutation({
    mutationFn: () => {
      return fetchMakeAutopayUrl({
        facilityId: unitInfo?.facilityId,
        accountId: unitInfo?.accountId,
        rentalId: unitInfo?.rentalId,
        deepLinkCallback: 'payments',
      });
    },
    onSuccess: data => {
      const autoPayUrl: WebViewUrl = Adapter.from(data).to(
        (item: ApiWebViewResponse) =>
          new GetAutopayPaymentAdapter(item).adapt(),
      );
      if (autoPayUrl?.webviewUrl) {
        dismissAlert();
        setTimeout(() => {
          navigation.navigate({
            name: 'PaymentWebView',
            params: { uri: autoPayUrl?.webviewUrl, title: 'Set Up Autopay' },
          });
        }, 500);
      }
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.autopayAdd, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        await logEventWithStatus(Events.autopayAdd, EventStatus.completed);
      }
    },
  });

  const callGetAutoPayUrlApi = async () => {
    await logEventWithStatus(Events.autopayAdd, EventStatus.initiated);
    getAutoPayUrlMutation.reset();
    await getAutoPayUrlMutation.mutateAsync();
  };

  const manageAutoPay = () => {
    void callGetAutoPayUrlApi();
  };

  const returnToPayments = () => {
    dismissAlert();
    setTimeout(() => {
      navigation.navigate('Payments');
    }, 500);
  };

  const renderSuccessIcon = () => (
    <View style={[gutters.marginTop_10]}>
      <AlertSuccess />
    </View>
  );

  const renderTitle = () => (
    <Text
      style={[
        fonts.size_24,
        fonts.appFontBold,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      Autopay has{'\n'}been turned off
    </Text>
  );

  const renderDescription = () => (
    <Text
      style={[
        fonts.size_14,
        fonts.appFontRegular,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      If this was a mistake, this helpful payment feature can be turned back on
      at anytime.
    </Text>
  );

  const renderReturnToPaymentsBtn = () => (
    <View style={[layout.fullWidth]}>
      <SSButton
        title="Return to Payments"
        onPress={returnToPayments}
        variant="dark"
      />
    </View>
  );

  const renderManageAutoPayBtn = () => (
    <View style={[layout.fullWidth]}>
      <SSButton
        title="Manage AutoPay"
        onPress={manageAutoPay}
        variant="transparent"
      />
    </View>
  );

  const renderButtons = () => (
    <View style={[layout.fullWidth, gutters.gap_10, gutters.marginVertical_20]}>
      {renderReturnToPaymentsBtn()}
      {renderManageAutoPayBtn()}
    </View>
  );

  const renderAlertContent = () => (
    <View
      style={[
        layout.fullWidth,
        layout.itemsCenter,
        borders.rounded_16,
        backgrounds.white,
        gutters.paddingHorizontal_20,
        gutters.paddingVertical_20,
      ]}
    >
      {renderSuccessIcon()}
      {renderTitle()}
      {renderDescription()}
      {renderButtons()}
    </View>
  );

  return (
    <View
      style={[
        layout.flex_1,
        layout.justifyCenter,
        layout.itemsCenter,
        gutters.paddingHorizontal_10,
        transparentBackgroundStyle,
      ]}
    >
      {renderAlertContent()}
    </View>
  );
}

export default AutoPayTurnOffSuccess;
