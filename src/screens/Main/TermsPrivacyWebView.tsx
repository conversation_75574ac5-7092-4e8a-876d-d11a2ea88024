import React, { useEffect, useState } from 'react';
import { KeyboardAvoidingView, StatusBar } from 'react-native';
import { WebView } from 'react-native-webview';
import { useTheme } from '@/theme';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import { useMutation } from '@tanstack/react-query';
import {
  ApiSecondaryContentResponse,
  SecondaryContent,
} from '@/types/schemas/secondaryContent';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { SecondaryContentAdapter } from '@/adapters/SecondaryContent/SecondaryContentAdapter';
import Adapter from '@/adapters/Adapter';
import getSecondaryContent from '@/services/SecondaryContent/getSecondaryContent';
import { SS_LINKS } from '@/utils/linking';

// Constants
const defaultUri = SS_LINKS.HOME;

interface WebviewProps {
  navigation: NavigationProp<ParamListBase>;
  route: { params: { isTermsConditions: boolean; title: string } };
}

function TermsPrivacyWebView({ navigation, route }: WebviewProps) {
  const { layout, backgrounds } = useTheme();
  const [webUri, setUri] = useState<string>(defaultUri);
  const isTermsConditions = route.params?.isTermsConditions ?? false;

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      title: route.params?.title ?? 'WEB',
      headerStyle: {
        backgroundColor: backgrounds.charcoal.backgroundColor,
      },
    });
  }, []);

  const getSecondaryContentUrlMutation = useMutation({
    mutationFn: () => {
      return getSecondaryContent();
    },
    onSuccess: data => {
      const obj: SecondaryContent = Adapter.from(data).to(
        (item: ApiSecondaryContentResponse) =>
          new SecondaryContentAdapter(item).adapt(),
      );
      if (isTermsConditions) {
        setUri(obj.termsPoliciesUrl);
      } else {
        setUri(obj.privacyPolicyUrl);
      }
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.secondaryContent, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        await logEventWithStatus(
          Events.secondaryContent,
          EventStatus.completed,
        );
      }
    },
  });

  const callApiGetSecondaryContent = async () => {
    getSecondaryContentUrlMutation.reset();
    await getSecondaryContentUrlMutation.mutateAsync();
  };

  useEffect(() => {
    void callApiGetSecondaryContent();
  }, []);

  return (
    <KeyboardAvoidingView style={layout.flex_1}>
      <WebView
        style={layout.flex_1}
        androidHardwareAccelerationDisabled
        source={{ uri: webUri }}
        cacheEnabled={false}
        startInLoadingState
        javaScriptEnabled
        bounces={false} // Disables bouncy effect
      />
    </KeyboardAvoidingView>
  );
}

// Export
export default TermsPrivacyWebView;
