import React, { useEffect, useState } from 'react';
import { Linking, ScrollView, View } from 'react-native';
import { useTheme } from '@/theme';
import signout from '@/services/accounts/signout';
import { AccountStackScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { LogoutSVG } from '@/assets/svg';
import { useMutation } from '@tanstack/react-query';
import { EventStatus, Events, logEventWithStatus } from '@/utils/analytics';
import { fetchAccountData } from '@/services/accounts';
import { useIsFocused } from '@react-navigation/native';
import {
  ActivityLoader,
  ListCardButton,
  ProfileCard,
} from '@/components/atoms';
import { AccountData, ApiAccountResponse } from '@/types/schemas/account';
import { AccountAdapter } from '@/adapters/Accounts/AccountAdapter';
import Adapter from '@/adapters/Adapter';
import { getProfileImageData, setUserInfoData } from '@/utils/storage';
import { SS_LINKS } from '@/utils/linking';
import { useAnalytics } from '@/context/AnalyticsContext';

const listArray = [
  {
    title: 'Payment History',
    navigateTo: 'PaymentHistory',
  },
  {
    title: 'Reset Password',
    navigateTo: 'ChangePassword',
  },
  {
    title: 'Settings',
    navigateTo: 'AccountSettings',
  },
  {
    title: 'Rent A Unit',
    navigateTo: '',
  },
  {
    title: 'Notifications',
    navigateTo: 'MainNotifications',
  },
  {
    title: 'Support',
    navigateTo: 'Support',
  },
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents
function Account({ navigation }: AccountStackScreenProps | any) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const { setCurrentScreen } = useAnalytics();
  const defaultData: AccountData = {
    contactInfo: {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
    },
    billingAddresses: [],
    personalizedPrefillUrl: '',
  };
  const [accountsData, setAccountsData] = useState(defaultData);
  const profileImageData: any = getProfileImageData();
  const isFocused = useIsFocused();

  const accountMutation = useMutation({
    mutationFn: () => {
      return fetchAccountData();
    },
    onSuccess: data => {
      setIsLoading(false);
      const accountData: AccountData = Adapter.from(data).to(
        (item: ApiAccountResponse) => new AccountAdapter(item).adapt(),
      );
      setAccountsData(accountData);
      setUserInfoData(accountData);
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.account, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        setIsLoading(false);
        await logEventWithStatus(Events.account, EventStatus.completed);
      }
    },
  });

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('Account', {
        hasProfileImage: Boolean(profileImageData?.path),
        hasContactInfo: Boolean(
          accountsData?.contactInfo?.firstName &&
          accountsData?.contactInfo?.lastName &&
          accountsData?.contactInfo?.phone &&
          accountsData?.contactInfo?.email
        ),
        hasBillingAddresses: accountsData?.billingAddresses?.length > 0,
        billingAddressCount: accountsData?.billingAddresses?.length || 0,
        isLoading,
        hasError: accountMutation.isError,
        hasPersonalizedPrefillUrl: Boolean(accountsData?.personalizedPrefillUrl)
      });
    }
  }, [
    isFocused,
    accountsData,
    profileImageData,
    isLoading,
    accountMutation.isError,
    setCurrentScreen,
  ]);

  const onLogoutPressed = () => {
    void signout();
  };

  const onDeleteAccountPressed = () => {
    void Linking.openURL(SS_LINKS.DELETE_ACCOUNT);
  };

  const callAccountDataApi = async () => {
    setIsLoading(true);
    accountMutation.reset();
    await logEventWithStatus(Events.account, EventStatus.initiated);
    await accountMutation.mutateAsync();
  };

  useEffect(() => {
    if (isFocused) {
      void callAccountDataApi();
    }
  }, [isFocused]);

  const navigateTo = (screenName: string) => {
    if (screenName) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
      navigation.navigate(screenName);
    }
  };

  const onEditProfilePressed = () => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    navigation.navigate('EditProfile');
  };

  const renderListItem = (item: { title: string; navigateTo: string }) => (
    <ListCardButton
      title={item.title}
      onPress={() => {
        if (item.title === 'Rent A Unit') {
          void Linking.openURL(
            accountsData?.personalizedPrefillUrl ?? SS_LINKS.HOME,
          );
        } else {
          navigateTo(item.navigateTo);
        }
      }}
    />
  );

  return (
    <ScrollView
      style={[
        layout.flex_1,
        layout.col,
        layout.fullWidth,
        layout.fullHeight,
        backgrounds.bluePrimary,
      ]}
    >
      <View style={[layout.flex_1, layout.col, backgrounds.blue10]}>
        <View
          style={[
            layout.h_200,
            backgrounds.bluePrimary,
            layout.justifyCenter,
            layout.fullWidth,
            { borderBottomLeftRadius: 16, borderBottomRightRadius: 16 },
          ]}
        />
        <View
          style={[
            layout.flex_1,
            layout.col,
            gutters.paddingHorizontal_16,
            { marginTop: -210 },
          ]}
        >
          <ProfileCard
            accountInfo={accountsData?.contactInfo}
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            profilePicture={profileImageData?.path || undefined}
            onEditProfilePress={onEditProfilePressed}
          />
          <View style={[gutters.paddingVertical_20, gutters.gap_8]}>
            {listArray.map(item => {
              return renderListItem(item);
            })}
          </View>
          <View
            style={[
              gutters.marginTop_6,
              gutters.marginBottom_20,
              gutters.gap_20,
            ]}
          >
            <SSButton
              title="LOGOUT"
              accessibilityLabel="Log out of SmartStop"
              onPress={onLogoutPressed}
              variant="transparent"
              fontColorStyle={fonts.blue}
              customBorderColorStyle={borders.blue}
              fontSizeStyle={fonts.size_17}
              customIcon={<LogoutSVG fill={backgrounds.blue.backgroundColor} />}
            />
            <SSButton
              title="DELETE ACCOUNT"
              accessibilityLabel="Delete your SmartStop account"
              onPress={onDeleteAccountPressed}
              variant="transparent"
              fontColorStyle={fonts.blue}
              customBorderColorStyle={borders.blue}
              fontSizeStyle={fonts.size_17}
            />
          </View>
        </View>
      </View>
      <ActivityLoader isLoading={isLoading} />
    </ScrollView>
  );
}

export default Account;
