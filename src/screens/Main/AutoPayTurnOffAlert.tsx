import React, { useState } from 'react';
import { Text, TextStyle, View, Alert } from 'react-native';
import { useMutation } from '@tanstack/react-query';
import { AlertWarning, CheckboxMarked } from '@/assets/svg';
import { SSButton, ActivityLoader } from '@/components/atoms';
import { useTheme } from '@/theme';
import { PaymentStackScreenProps } from '@/types/navigation';
import { disableAutoPay } from '@/services/payments';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { Unit } from '@/types/schemas/unit';

function AutoPayTurnOffAlert({ navigation, route }: PaymentStackScreenProps) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const transparentBackgroundStyle = {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  };
  const otherTextStyles: TextStyle = {
    textAlign: 'center',
  };
  const otherBenefitsTitleStyles: TextStyle = {
    textTransform: 'uppercase',
    alignSelf: 'flex-start',
    letterSpacing: 0.8,
  };
  const unitInfo = route?.params?.unitInfo as Unit;

  const autoPayBenefits = [
    'Never Miss a Payment',
    'Avoid Late Fees',
    'Save Time & Effort',
  ];

  const dismissAlert = () => {
    navigation.goBack();
  };

  const showSuccessAlert = () => {
    dismissAlert();
    setTimeout(() => {
      navigation.navigate({
        name: 'AutoPayTurnOffSuccess',
        params: { unitInfo },
      });
    }, 500);
  };

  const disableAutopayMutation = useMutation({
    mutationFn: () => {
      return disableAutoPay({
        siteId: unitInfo?.facilityId,
        accountId: unitInfo?.accountId,
        rentalId: unitInfo?.rentalId,
      });
    },
    onSuccess: data => {
      setIsLoading(false);
      if (data.success) {
        showSuccessAlert();
      } else {
        Alert.alert('Error', data.message ?? '');
      }
    },
    onError: async error => {
      setIsLoading(false);
      console.error(error);
      await logEventWithStatus(Events.autopayRemove, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      setIsLoading(false);
      if (data) {
        await logEventWithStatus(Events.autopayRemove, EventStatus.completed);
      }
    },
  });

  const callAutoPayDisableApi = async () => {
    setIsLoading(true);
    disableAutopayMutation.reset();
    await logEventWithStatus(Events.autopayRemove, EventStatus.initiated);
    await disableAutopayMutation.mutateAsync();
  };

  const renderAlertIcon = () => (
    <View style={[gutters.marginTop_10]}>
      <AlertWarning />
    </View>
  );

  const renderTitle = () => (
    <Text
      style={[
        fonts.size_24,
        fonts.appFontBold,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      Wait, before you {'\n'} turn off Autopay!
    </Text>
  );

  const renderDescription = () => (
    <Text
      style={[
        fonts.size_14,
        fonts.appFontRegular,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      Are you sure you want to disable Autopay? Keeping Autopay active ensures
      you never miss a payment and avoid late feeds. Plus, it&apos;s one less
      thing to worry about each month!
    </Text>
  );

  const renderSeparator = () => (
    <View
      style={[
        layout.fullWidth,
        layout.h_1,
        backgrounds.springGrass40,
        gutters.marginTop_20,
      ]}
    />
  );

  const renderBenefitsHeader = () => (
    <Text
      style={[
        fonts.size_14,
        fonts.appFontBold,
        fonts.lightCharcoal,
        gutters.marginVertical_20,
        otherBenefitsTitleStyles,
      ]}
    >
      Autopay Benefits
    </Text>
  );

  const renderBenefit = (item: string) => (
    <View
      style={[
        layout.row,
        layout.itemsCenter,
        layout.justifyStart,
        layout.fullWidth,
        gutters.gap_10,
        gutters.marginBottom_8,
      ]}
    >
      <CheckboxMarked />
      <Text style={[fonts.size_14, fonts.appFontRegular, fonts.charcoal]}>
        {item}
      </Text>
    </View>
  );

  const renderTurnOffBtn = () => (
    <View style={[layout.fullWidth]}>
      <SSButton
        title="Turn Off"
        onPress={() => callAutoPayDisableApi()}
        variant="dark"
      />
    </View>
  );

  const renderCancelBtn = () => (
    <View style={[layout.fullWidth]}>
      <SSButton title="Cancel" onPress={dismissAlert} variant="transparent" />
    </View>
  );

  const renderButtons = () => (
    <View style={[layout.fullWidth, gutters.gap_10, gutters.marginVertical_20]}>
      {renderTurnOffBtn()}
      {renderCancelBtn()}
    </View>
  );

  const renderAlertContent = () => (
    <View
      style={[
        layout.fullWidth,
        layout.itemsCenter,
        borders.rounded_16,
        backgrounds.white,
        gutters.paddingHorizontal_20,
        gutters.paddingVertical_20,
      ]}
    >
      {renderAlertIcon()}
      {renderTitle()}
      {renderDescription()}
      {renderSeparator()}
      {renderBenefitsHeader()}
      {autoPayBenefits.map(renderBenefit)}
      {renderButtons()}
    </View>
  );

  return (
    <View
      style={[
        layout.flex_1,
        layout.justifyCenter,
        layout.itemsCenter,
        gutters.paddingHorizontal_10,
        transparentBackgroundStyle,
      ]}
    >
      {renderAlertContent()}
      <ActivityLoader isLoading={isLoading} />
    </View>
  );
}

export default AutoPayTurnOffAlert;
