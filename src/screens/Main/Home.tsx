import React, { useContext, useEffect, useState } from 'react';
import {
  Text,
  ScrollView,
  View,
  FlatList,
  Dimensions,
  Image,
  Linking,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTheme } from '@/theme';
import { HomeStackScreenProps } from '@/types/navigation';
import fetchLocations from '@/services/locations/fetchLocations';
import { Location } from '@/types/schemas/location';
import SSLocationCard from '@/components/atoms/SSLocationCard';
import { Unit } from '@/types/schemas/unit';
import SSUnitInfo from '@/components/atoms/SSUnitInfo';
import { PageIndicator } from 'react-native-page-indicator';
import ListCardButton from '@/components/atoms/ListCardButton';
import AddStorageAccountDialog from '@/components/molecules/AddStorageAccountDialog';
import HoursDialog from '@/components/molecules/HoursDialog';
import { AccountData } from '@/types/schemas/account';
import { getUserInfoData, PERMISSIONS_DIALOG_SHOWN } from '@/utils/storage';
import { useIsFocused } from '@react-navigation/native';
import fetchNotifications from '@/services/notifications/fetchNotifications';
import Adapter from '@/adapters/Adapter';
import { NotificationsAdapter } from '@/adapters/Notifications/NotificationsAdapter';
import { ApiNotification } from '@/types/schemas/notification';
import { isNewNotification } from '@/utils/commonFunctions';
import { HomeLogoSVG } from '@/assets/svg';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ActivityLoader, HamburgerMenuButton } from '@/components/atoms';
import NotificationsMenuButton from '@/components/atoms/NotificationsMenuButton';
import { useDrawerStatus } from '@react-navigation/drawer';
import { PermissionsDialog } from '@/components/molecules';
import useStorage from '@/hooks/useStorage';
import fetchAccessCode from '@/services/access/fetchAccessCode';
import { AccessCode, ApiAccessCode } from '@/types/schemas/access';
import { AccessCodeAdapter } from '@/adapters/Access/AccessCodeAdapter';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { fetchMakePaymentUrl } from '@/services/payments';
import { ApiWebViewResponse, WebViewUrl } from '@/types/schemas/webview';
import { OneTimePaymentAdapter } from '@/adapters/Payments/OneTimePaymentAdapter';
import { SS_LINKS } from '@/utils/linking';
import { useAnalytics } from '@/context/AnalyticsContext';

const { width } = Dimensions.get('window');
export const NavigationContext = React.createContext({
  hasStorage: false,
  setHasStorageState: () => { },
});

function Home({ navigation, route }: HomeStackScreenProps) {
  const { setCurrentScreen } = useAnalytics();
  const storage = useStorage('global-storage');
  const permissionsDialogShown =
    storage.getBoolean(PERMISSIONS_DIALOG_SHOWN) ?? false;
  const { badge, setBadge } = route.params as {
    badge: boolean;
    setBadge: (badge: boolean) => void;
  };
  const [localBadge, setLocalBadge] = useState<boolean>(badge);
  const { layout, fonts, gutters, backgrounds, borders, colors } = useTheme();
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [accessCodes, setAccessCodes] = useState<AccessCode[]>([]);
  const [showAddUnitDialog, setShowAddUnitDialog] = useState<boolean>(false);
  const [isUnitAdded, setIsUnitAdded] = useState<boolean>(false);
  const [showHoursDialog, setShowHoursDialog] = useState<boolean>(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState<boolean>(
    !permissionsDialogShown,
  );

  const { setHasStorageState } = useContext(NavigationContext);
  const [accountData] = useState<AccountData | null>(getUserInfoData());
  const [userName, setUserName] = useState<string>('');
  const isFocused = useIsFocused();
  const insets = useSafeAreaInsets();
  const drawerStatus = useDrawerStatus();

  useEffect(() => {
    if (drawerStatus === 'closed' && isFocused) {
      StatusBar.setBarStyle('dark-content');
    }
  }, [drawerStatus, isFocused]);

  const GetStorageData = async () => {
    try {
      const response = await fetchLocations();
      setHasStorageState(false);
      return response;
    } catch (error) {
      setHasStorageState(true);
      throw error; // Handle error state
    }
  };

  const queryClient = useQueryClient();
  const { data, error, isLoading, refetch, isRefetching } = useQuery({
    queryKey: ['home_locations'],
    queryFn: () => {
      return GetStorageData();
    },
    refetchOnWindowFocus: false,
  });

  const getAccessCodeMutation = useMutation({
    mutationFn: () => {
      return fetchAccessCode();
    },
    onSuccess: data => {
      const accessCodesArr: AccessCode[] = Adapter.from(data).to(
        (item: ApiAccessCode[]) => {
          return item.map(accessCode => {
            return new AccessCodeAdapter(accessCode).adapt();
          });
        },
      );
      setAccessCodes(accessCodesArr);
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.gateAccessCode, EventStatus.failed, {
        error: JSON.stringify(error),
        facilityId: data?.[currentPage]?.units[0]?.facilityId?.toString(),
        tenantId: data?.[currentPage]?.units[0]?.accountId?.toString(),
      });
    },
    onSettled: async () => {
      if (data?.[currentPage]?.units[0]) {
        await logEventWithStatus(Events.gateAccessCode, EventStatus.completed, {
          facilityId: data[currentPage].units[0].facilityId.toString(),
          tenantId: data[currentPage].units[0].accountId.toString(),
        });
      }
    },
  });

  const getOneTimePaymentMutation = useMutation({
    mutationFn: (item: Unit) => {
      return fetchMakePaymentUrl({
        facilityId: item.facilityId,
        accountId: item.accountId,
        rentalId: item.rentalId,
        deepLinkCallback: 'home',
      });
    },
    onSuccess: data => {
      const oneTimePaymentUrl: WebViewUrl = Adapter.from(data).to(
        (item: ApiWebViewResponse) => new OneTimePaymentAdapter(item).adapt(),
      );
      if (oneTimePaymentUrl?.webviewUrl) {
        navigation.navigate({
          name: 'PaymentWebView',
          params: { uri: oneTimePaymentUrl?.webviewUrl, title: 'Payment' },
        });
      }
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.oneTimePayment, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        await logEventWithStatus(Events.oneTimePayment, EventStatus.completed);
      }
    },
  });

  const callGetAccessCodeApi = async () => {
    getAccessCodeMutation.reset();
    await logEventWithStatus(Events.gateAccessCode, EventStatus.initiated, {
      facilityId: data?.[currentPage]?.units[0]?.facilityId?.toString(),
      tenantId: data?.[currentPage]?.units[0]?.accountId?.toString(),
    });
    await getAccessCodeMutation.mutateAsync();
  };

  useEffect(() => {
    if (isFocused && data) {
      void callGetAccessCodeApi();
    }
  }, [isFocused, data]);

  const notifiactionsQuery = useQuery({
    queryKey: ['notifications'],
    queryFn: () => {
      return fetchNotifications();
    },
    select: notificationData => {
      return Adapter.from(notificationData).to((item: ApiNotification[]) => {
        return item.map(notification => {
          return new NotificationsAdapter(notification).adapt();
        });
      });
    },
  });

  const callGetOneTimePaymentApi = async (item: Unit) => {
    await logEventWithStatus(Events.oneTimePayment, EventStatus.initiated);
    getOneTimePaymentMutation.reset();
    await getOneTimePaymentMutation.mutateAsync(item);
  };


  const onRefresh = () => {
    void refetch();
  };


  useEffect(() => {
    if (isFocused) {
      if (notifiactionsQuery.data) {
        const isNew = isNewNotification(notifiactionsQuery.data);
        setBadge(isNew);
        setLocalBadge(isNew)
      }
    }
  }, [isFocused, notifiactionsQuery.data]);

  useEffect(() => {
    // Clear the cache and refetch data when the component mounts
    queryClient.removeQueries({ queryKey: ['home_locations'] });
    void refetch();
    if (accountData) {
      setUserName(
        ` ${accountData.contactInfo.firstName} ${accountData.contactInfo.lastName}`,
      );
    }
  }, [queryClient, refetch, accountData]);

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('Home', {
        hasStorage: data ? true : false,
        locationCount: data?.length || 0,
      });
    }
  }, [isFocused, data, setCurrentScreen]);

  if (error) {
    setHasStorageState(true);
  }

  if (data) {
    setHasStorageState(false);
  }

  const handleMakePaymentPressed = (unit: Unit) => {
    if (unit?.canMakePayment) {
      void callGetOneTimePaymentApi(unit);
    }
  };

  const handleOnUnitNotesPressed = (unit: Unit) => {
    if (data) {
      const allUnits = data.flatMap(location => location.units);
      navigation.navigate('MainLocationUnitNotes', {
        unit: allUnits,
        selectedUnit: unit,
      });
    }
  };

  const handleOnAccessPresssed = (item: Location) => {
    navigation.navigate('Access', {
      screen: 'AccessHome',
      params: { selectedLocationId: item.locationId },
    });
  };

  const openSupportPage = () => {
    navigation.navigate('Support');
  };

  const renderLocation = ({ item }: { item: Location }) => (
    <View
      accessibilityLabel="Location Information"
      style={[
        layout.flex_1,
        { width: width - 32 },
        gutters.marginTop_16,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <SSLocationCard
        location={item}
        onAccess={() => handleOnAccessPresssed(item)}
      />
    </View>
  );

  const renderEmptyView = () => (
    <View
      style={[
        layout.flex_1,
        { width: width - 32 },
        gutters.marginTop_16,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <View
        style={[
          layout.col,
          layout.flex_1,
          layout.justifyBetween,
          borders.gray100,
        ]}
      >
        <Image
          style={[
            layout.fullWidth,
            layout.h_160,
            { borderTopLeftRadius: 16, borderTopRightRadius: 16 },
          ]}
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          source={require('@/assets/NoLocationSmartStopSelfStorage.png')}
        />

        <View style={[gutters.padding_24]}>
          <Text style={[fonts.charcoal, fonts.appFontBold, fonts.size_20]}>
            No storage unit linked to this account.
          </Text>
          <Text style={[fonts.charcoal, fonts.appFontRegular, fonts.size_14]}>
            There are no storage units currently linked to your account. Please
            add a storage unit now to get started.
          </Text>
        </View>
      </View>
    </View>
  );

  const renderItem = (item: Unit, hasMultipleUnits: boolean) => (
    <SSUnitInfo
      unit={item}
      isToggleVisible={hasMultipleUnits}
      onPress={handleMakePaymentPressed}
      handleOnUnitNotesPressed={handleOnUnitNotesPressed}
    />
  );

  const onFindStorageAccountNumber = () => {
    setShowAddUnitDialog(false);
    setTimeout(
      () =>
        navigation.navigate('FindStorageNumber', {
          isFromHome: true,
          isFromPayment: false,
        }),
      100,
    );
  };

  const renderHeader = () => {
    return (
      <View>
        <View
          style={[
            backgrounds.white,
            layout.row,
            layout.itemsEnd,
            gutters.paddingHorizontal_16,
            gutters.gap_16,
            { height: insets.top + 50 },
          ]}
        >
          <View style={[gutters.paddingBottom_12]}>
            <HamburgerMenuButton navigation={navigation} color={colors.blue} />
          </View>
          <View style={[layout.flex_1, layout.itemsCenter, layout.fullHeight]}>
            <View style={[layout.fullHeight, layout.justifyEnd]}>
              <View style={[backgrounds.blueLogo, layout.flex_1]} />
              <HomeLogoSVG />
            </View>
          </View>
          <View style={[gutters.paddingBottom_12]}>
            <NotificationsMenuButton badge={localBadge} color={colors.blue} />
          </View>
        </View>
      </View>
    );
  };

  const onConfirmPermissionsDialog = () => {
    setShowPermissionsDialog(false);
    storage.set(PERMISSIONS_DIALOG_SHOWN, true);
  };

  const getAccessCode = (item: Location) => {
    const a = item.units.map(unit => {
      const accessCode = accessCodes.find(
        access => access.tenantId === unit.accountId,
      );
      return accessCode?.accessCode || 'Unavailable';
    });
    return a[0];
  };

  return (
    <View style={[layout.flex_1, backgrounds.white]}>
      {renderHeader()}
      <ScrollView
        contentContainerStyle={[
          layout.flexGrow_1,
          layout.col,
          backgrounds.blue10,
        ]}
        refreshControl={
          <RefreshControl
            tintColor={borders.blue.borderColor}
            refreshing={isRefetching}
            onRefresh={onRefresh}
          />
        }
      >
        <View
          style={[
            backgrounds.white,
            layout.absolute,
            layout.h_208,
            layout.fullWidth,
            borders.bottomLeftRounded_16,
            borders.bottomRightRounded_16,
          ]}
        />
        <View style={[gutters.padding_16]}>
          {!isLoading && (
            <View style={[gutters.paddingBottom_32]}>
              <Text
                style={[
                  fonts.charcoal,
                  fonts.size_24,
                  gutters.marginTop_16,
                  fonts.appFontRegular,
                ]}
              >
                Welcome,
                <Text style={[fonts.blue, fonts.size_24, fonts.appFontBold]}>
                  {userName}
                </Text>
              </Text>
              {/*  Account have location */}
              {data && (
                <>
                  <FlatList
                    data={data}
                    horizontal
                    pagingEnabled
                    showsHorizontalScrollIndicator={false}
                    renderItem={renderLocation}
                    keyExtractor={item => item.locationId.toString()}
                    onScroll={event => {
                      const page = Math.round(
                        event.nativeEvent.contentOffset.x / width,
                      );
                      setCurrentPage(page);
                    }}
                  />
                  {data?.length > 1 && (
                    <View style={[layout.selfCenter, gutters.margin_16]}>
                      <PageIndicator
                        color={fonts.primary.color}
                        activeColor={fonts.primary.color}
                        count={data?.length}
                        current={currentPage}
                      />
                    </View>
                  )}

                  <Text
                    style={[
                      fonts.size_24,
                      gutters.marginTop_16,
                      fonts.appFontBold,
                      fonts.charcoal,
                    ]}
                  >
                    Unit Information
                  </Text>
                  <FlatList
                    scrollEnabled={false}
                    data={data[currentPage].units}
                    renderItem={({ item }) => renderItem(item, data[currentPage]?.units?.length > 1)}
                    keyExtractor={item => item.unitNumber.toString()}
                  />
                </>
              )}
              {/* Empty view added */}
              {error && !data && renderEmptyView()}
              <View style={[layout.itemsCenter, gutters.marginTop_16]}>
                <Text
                  style={[fonts.appFontRegular, fonts.size_14, fonts.charcoal]}
                >
                  Don't see your unit?{' '}
                  <Text
                    style={[fonts.primary, fonts.appFontBold, fonts.underline]}
                    onPress={() => {
                      setShowAddUnitDialog(true);
                    }}
                  >
                    Link Your Storage Unit
                  </Text>
                </Text>
              </View>
              {data && (
                <>
                  <ListCardButton
                    title="Gate Access"
                    description={`Access code: ${getAccessCode(
                      data[currentPage],
                    )}`}
                    onPress={() => handleOnAccessPresssed(data[currentPage])}
                  />
                  <ListCardButton
                    title="Hours"
                    onPress={() => {
                      setShowHoursDialog(true);
                    }}
                  />
                </>
              )}
              <ListCardButton
                title="Rent A Unit"
                onPress={() => {
                  void Linking.openURL(
                    accountData?.personalizedPrefillUrl ?? SS_LINKS.HOME,
                  );
                }}
              />
              <ListCardButton
                title="Contact Us"
                onPress={() => {
                  openSupportPage();
                }}
              />
            </View>
          )}
        </View>
      </ScrollView>
      {showAddUnitDialog && (
        <AddStorageAccountDialog
          onClose={() => {
            setShowAddUnitDialog(false)
            if (isUnitAdded === true) {
              setIsUnitAdded(false)
              queryClient.removeQueries({ queryKey: ['home_locations'] });
              onRefresh();
            }
          }}
          onUnitAdded={(value: boolean) => setIsUnitAdded(value)}
          onNavigation={() => onFindStorageAccountNumber()}
        />
      )}
      {showHoursDialog && data && (
        <HoursDialog
          location={data[currentPage]}
          onClose={() => setShowHoursDialog(false)}
        />
      )}
      {showPermissionsDialog && (
        <PermissionsDialog onConfirm={onConfirmPermissionsDialog} />
      )}
      <ActivityLoader isLoading={isLoading} />
    </View>
  );
}

export default Home;
