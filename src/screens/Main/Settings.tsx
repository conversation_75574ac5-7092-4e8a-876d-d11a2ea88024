import React from 'react';
import { View } from 'react-native';
import { useTheme } from '@/theme';
import { PermissionToggles } from '@/components/atoms';

function Settings() {
  const { layout, gutters, backgrounds, borders } = useTheme();
  return (
    <View
      style={[
        layout.flex_1,
        layout.col,
        gutters.padding_16,
        backgrounds.blue10,
      ]}
    >
      <PermissionToggles
        containerStyle={[gutters.gap_10]}
        toggleContainerStyle={[
          backgrounds.white,
          gutters.paddingVertical_10,
          gutters.paddingHorizontal_16,
          borders.rounded_16,
        ]}
      />
    </View>
  );
}

export default Settings;
