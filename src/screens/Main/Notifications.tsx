import React, { useCallback, useEffect, useState } from 'react';
import Adapter from '@/adapters/Adapter';
import fetchNotifications from '@/services/notifications/fetchNotifications';
import { useMutation } from '@tanstack/react-query';
import { View, Text, Pressable, Alert, StatusBar } from 'react-native';
import { ApiNotification, Notification } from '@/types/schemas/notification';
import { NotificationsAdapter } from '@/adapters/Notifications/NotificationsAdapter';
import { RefreshControl } from 'react-native-gesture-handler';
import { DateTime } from 'luxon';

import { DeleteIconSVG, EmptyBoxSVG } from '@/assets/svg';
import SwipeableFlatList from 'rn-gesture-swipeable-flatlist/dist/SwipeableFlatList';
import useTheme from '@/theme/hooks/useTheme';
import { ActivityLoader } from '@/components/atoms';
import {
  getLastFetchedNotificationDate,
  getNotificationDeleteId,
  getUserInfoData,
  setLastFetchedNotificationDate,
  setNotificationDeleteId,
} from '@/utils/storage';
import { useIsFocused } from '@react-navigation/native';
import { shortenRelativeTime } from '@/utils/commonFunctions';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import { useAnalytics } from '@/context/AnalyticsContext';

function Notifications() {
  const [lastFetchDate] = useState<string>(
    getLastFetchedNotificationDate(getUserInfoData()?.contactInfo.email ?? "") ?? '',
  );
  const [deletedIds, setDeletedIds] = useState<string[]>(
    getNotificationDeleteId(getUserInfoData()?.contactInfo?.email ?? '') ?? [],
  );
  const isFocused = useIsFocused();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(true);
  const [notifiacationData, setNotificationData] = useState<Notification[]>([]);
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { setCurrentScreen } = useAnalytics();

  const notificationMutation = useMutation({
    mutationFn: () => {
      return fetchNotifications();
    },
    onSuccess: data => {
      setIsLoading(false);
      setIsRefreshing(false);
      setLastFetchedNotificationDate(DateTime.now().toString(), getUserInfoData()?.contactInfo?.email ?? '');
      const notificationArr = Adapter.from(data).to(
        (item: ApiNotification[]) => {
          return item.map(notification => {
            return new NotificationsAdapter(notification).adapt();
          });
        },
      );
      const updatedArr: Notification[] = notificationArr.filter(
        item => !deletedIds.includes(item.id),
      );
      setNotificationData(updatedArr);
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      setIsRefreshing(false);
      await logEventWithStatus(
        Events.getNotificationsList,
        EventStatus.failed,
        {
          error: JSON.stringify(error),
        },
      );
      Alert.alert('Error', error.message ?? '');
    },
    onSettled: async data => {
      if (data) {
        setIsLoading(false);
        setIsRefreshing(false);
        await logEventWithStatus(
          Events.getNotificationsList,
          EventStatus.completed,
        );
      }
    },
  });

  const callNotificationsApi = async () => {
    notificationMutation.reset();
    await logEventWithStatus(
      Events.getNotificationsList,
      EventStatus.initiated,
    );
    await notificationMutation.mutateAsync();
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    void callNotificationsApi();
  };

  useEffect(() => {
    if (isFocused) {
      setIsLoading(true);
      void callNotificationsApi();
      StatusBar.setBarStyle('light-content');
    }
  }, [isFocused]);

  useEffect(() => {
    if (deletedIds) {
      setNotificationDeleteId(deletedIds, getUserInfoData()?.contactInfo?.email ?? '');
      const updatedArr: Notification[] = notifiacationData.filter(
        item => !deletedIds.includes(item.id),
      );
      setNotificationData(updatedArr);
    }
  }, [deletedIds]);

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('Notifications', {
        totalNotifications: notifiacationData?.length ?? 0,
        deletedNotifications: deletedIds?.length ?? 0,
        hasNotifications: notifiacationData?.length > 0,
        isLoading,
        isRefreshing,
        lastFetchDate: lastFetchDate || undefined,
      });
    }
  }, [
    isFocused,
    notifiacationData?.length ?? 0,
    deletedIds?.length ?? 0,
    isLoading,
    isRefreshing,
    lastFetchDate,
    setCurrentScreen,
  ]);

  const removeNotification = (data: Notification) => {
    setDeletedIds(prevArray => {
      if (!prevArray.includes(data.id)) {
        return [...prevArray, data.id];
      }
      return prevArray;
    });
  };

  const renderNotification = (params: { item: Notification }) => {
    const { item } = params;
    const date = DateTime.fromISO(item.date);
    const lastFetchDateTime = DateTime.fromISO(lastFetchDate);
    const isDateAfter =
      date.isValid && lastFetchDateTime.isValid
        ? date > lastFetchDateTime
        : true;
    return (
      <Pressable
        style={[
          borders.rounded_16,
          layout.row,
          layout.justifyCenter,
          gutters.gap_5,
          gutters.padding_14,
          backgrounds.white,
        ]}
      >
        <View style={[layout.flex_1, layout.col, gutters.gap_5]}>
          <Text style={[fonts.appFontBold, fonts.charcoal, fonts.size_16]}>
            {item.title}
          </Text>
          <Text style={[fonts.appFontRegular, fonts.charcoal, fonts.size_12]}>
            {item.subtitle}
          </Text>
        </View>
        <View
          style={[
            layout.col,
            layout.itemsEnd,
            { flexDirection: 'column-reverse' },
            layout.justifyBetween,
            gutters.gap_5,
            gutters.marginVertical_4,
          ]}
        >
          <Text
            style={[fonts.appFontRegular, fonts.lightCharcoal, fonts.size_12]}
          >
            {shortenRelativeTime(date.toRelative())}
          </Text>
          {isDateAfter && (
            <View
              style={[
                layout.h_12,
                layout.w_12,
                borders.rounded_12,
                backgrounds.alertOrange,
              ]}
            />
          )}
        </View>
      </Pressable>
    );
  };

  const renderRightAction = useCallback(
    (data: Notification) => (
      <Pressable
        style={[
          gutters.marginLeft_8,
          borders.rounded_16,
          layout.col,
          layout.fullHeight,
          layout.justifyCenter,
          layout.itemsCenter,
          gutters.gap_5,
          gutters.paddingHorizontal_20,
          backgrounds.blue,
        ]}
        onPress={() => {
          removeNotification(data);
        }}
      >
        <DeleteIconSVG />
        <Text
          style={[
            fonts.appFontBold,
            fonts.white,
            fonts.size_12,
            fonts.uppercase,
          ]}
        >
          Delete
        </Text>
      </Pressable>
    ),
    [],
  );

  const renderEmptyState = () => {
    return (
      !isLoading && (
        <View
          style={[
            layout.flex_1,
            layout.justifyCenter,
            layout.itemsCenter,
            gutters.padding_16,
          ]}
        >
          <EmptyBoxSVG />
          <Text
            style={[
              fonts.size_32,
              fonts.appFontBold,
              fonts.primary,
              fonts.alignCenter,
            ]}
          >
            No Notifications!
          </Text>
          <Text
            style={[
              fonts.size_14,
              fonts.appFontRegular,
              fonts.charcoal,
              fonts.alignCenter,
            ]}
          >
            You have no new notifications at this time. Please check back for
            any updates or important messages.
          </Text>
        </View>
      )
    );
  };

  return (
    <View
      style={[
        layout.flex_1,
        gutters.paddingVertical_20,
        gutters.paddingHorizontal_12,
      ]}
    >
      {!isLoading && (
        <SwipeableFlatList
          data={notifiacationData}
          contentContainerStyle={[gutters.gap_10]}
          ListEmptyComponent={renderEmptyState}
          renderItem={renderNotification}
          renderRightActions={renderRightAction}
          enableOpenMultipleRows={false}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
          }
        />
      )}
      <ActivityLoader isLoading={isLoading} />
    </View>
  );
}

export default Notifications;
