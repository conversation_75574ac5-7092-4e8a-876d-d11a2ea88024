import React, { useState } from 'react';
import { ScrollView, View, Image, Alert } from 'react-native';
import { useTheme } from '@/theme';
import { AccountStackScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';

import profileUserIcon from '@/assets/profileUserIcon.png';
import ImagePicker, { Options } from 'react-native-image-crop-picker';
import {
  clearProfileImageData,
  getProfileImageData,
  getUserInfoData,
  setProfileImageData,
} from '@/utils/storage';
import { formatPhoneNumber } from '@/utils/commonFunctions';

type Error = {
  code: string;
  message: string;
};

// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents, @typescript-eslint/no-explicit-any
function EditProfile({ navigation }: AccountStackScreenProps | any) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const imageData: any = getProfileImageData();
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const [imageMeta, setImage] = useState(imageData);
  const [userData] = useState(getUserInfoData());

  const imagePickerOptions: Options = {
    mediaType: 'photo',
    width: 512,
    height: 512,
    compressImageMaxWidth: 512,
    compressImageMaxHeight: 512,
    cropping: true,
    compressImageQuality: 0.3, // values range from 0 to 1, 1 being highest quality.
  };

  const isValidImageSize = (image: object & { size: number }) => {
    const fileSizeBytes = image?.size; // bytes
    const fileSizeMB = fileSizeBytes / 1024 ** 2;
    if (fileSizeMB > 5) {
      return false;
    }
    return true;
  };

  const updateImage = (image: object & { size: number }) => {
    if (isValidImageSize(image)) {
      setImage(image);
      setProfileImageData(image);
      Alert.alert('Smart Stop', 'The image has been successfully uploaded');
    } else {
      Alert.alert('Failed to upload image', 'Image size should be maximum 5MB');
    }
  };

  const onPressCamera = () => {
    ImagePicker.openCamera(imagePickerOptions)
      .then(image => {
        updateImage(image);
      })
      .catch((error: Error) => {
        if (error.code === 'E_PICKER_CANCELLED') {
          return false;
        }
        return Alert.alert('Failed to Change Avatar', error.message);
      });
  };

  const onPressGallery = () => {
    ImagePicker.openPicker(imagePickerOptions)
      .then(image => {
        updateImage(image);
      })
      .catch((error: Error) => {
        if (error.code === 'E_PICKER_CANCELLED') {
          return false;
        }
        return Alert.alert('Failed to Change Avatar', error.message);
      });
  };

  const onRemovePhoto = () => {
    setImage(null);
    clearProfileImageData();
  };

  const onEditPhotoPressed = () => {
    const name = 'ActionSheet';
    const params = {
      title: 'Edit Profile Photo',
      actions: [
        {
          text: 'Photo Library',
          onPress: () => onPressGallery(),
        },
        {
          text: 'Take Photo',
          onPress: () => onPressCamera(),
        },
        {
          text: 'Remove Photo',
          onPress: () => onRemovePhoto(),
          textStyle: { color: fonts.darkRed.color },
        },
      ],
    };
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    navigation.navigate({ name, params });
  };

  const renderUserProfileIcon = () => (
    <Image
      accessibilityLabel='Profile picture'
      style={[
        layout.w_100,
        layout.h_100,
        borders.rounded_50,
        backgrounds.blue10,
        gutters.marginVertical_26,
      ]}
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
      source={imageMeta ? { uri: imageMeta?.path } : profileUserIcon}
    />
  );

  const renderEditPhotoButton = () => (
    <SSButton
      title="EDIT PHOTO"
      accessibilityLabel="Edit photo"
      onPress={onEditPhotoPressed}
      variant="transparent"
      fontColorStyle={fonts.blue}
      customBorderColorStyle={borders.blue}
      fontSizeStyle={fonts.size_12}
      borderRadiusStyle={borders.rounded_13}
      heightStyle={layout.h_26}
      widthStyle={layout.w_120}
    />
  );

  const renderCard = () => (
    <View style={[backgrounds.white, borders.rounded_16]}>
      <View style={[layout.itemsCenter]}>
        {renderUserProfileIcon()}
        {renderEditPhotoButton()}
      </View>

      <View
        style={[
          gutters.marginHorizontal_16,
          gutters.marginVertical_20,
          gutters.gap_12,
        ]}
      >
        <SSTextInput
          label="First Name"
          value={userData?.contactInfo.firstName}
          isEditable={false}
        />
        <SSTextInput
          label="Last Name"
          value={userData?.contactInfo.lastName}
          isEditable={false}
        />
        <SSTextInput
          label="Email"
          value={userData?.contactInfo.email}
          isEditable={false}
        />
        <SSTextInput
          label="Phone Number"
          value={formatPhoneNumber(userData?.contactInfo.phone)}
          isEditable={false}
        />
      </View>
    </View>
  );

  return (
    <ScrollView style={[layout.flex_1, layout.col, backgrounds.blue10]}>
      <View style={[layout.flex_1, layout.fullHeight, backgrounds.blue10]}>
        <View
          style={[
            layout.h_200,
            backgrounds.bluePrimary,
            layout.justifyCenter,
            { borderBottomLeftRadius: 16, borderBottomRightRadius: 16 },
          ]}
        />
        <View
          style={[
            layout.flex_1,
            layout.fullHeight,
            gutters.paddingHorizontal_16,
            { marginTop: -170 },
          ]}
        >
          {renderCard()}
        </View>
      </View>
    </ScrollView>
  );
}

export default EditProfile;
