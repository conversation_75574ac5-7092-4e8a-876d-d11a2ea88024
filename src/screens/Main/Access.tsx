/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  FlatList,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  NativeSyntheticEvent,
  LayoutChangeEvent,
  NativeScrollEvent,
  Platform,
  AppState,
  RefreshControl,
} from 'react-native';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTheme } from '@/theme';
import { AccessStackScreenProps } from '@/types/navigation';
import fetchLocations from '@/services/locations/fetchLocations';
import { Location } from '@/types/schemas/location';
import SSAccessLocationCard from '@/components/atoms/SSAccessLocationCard';
import { PageIndicator } from 'react-native-page-indicator';
import {
  AccessErrorView,
  ActivityLoader,
  SSDoorButton,
} from '@/components/atoms';
import { useIsFocused } from '@react-navigation/native';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import fetchAccessCode from '@/services/access/fetchAccessCode';
import Adapter from '@/adapters/Adapter';
import { AccessCode, ApiAccessCode } from '@/types/schemas/access';
import { AccessCodeAdapter } from '@/adapters/Access/AccessCodeAdapter';
import { GateOptionAdapter } from '@/adapters/Access/GateOptionAdapter';
import { GateSvg } from '@/assets/svg';
import {
  DoorState,
  GateSystemType,
  getDistanceFromLatLonInKm,
} from '@/utils/commonFunctions';
import {
  GetGateOptionsRequest,
  GetNokeLocksRequest,
  NokeUnlockOverNetworkRequest,
} from '@/types/schemas/request';
import { ApiGateOptions, GateOptions } from '@/types/schemas/gateOptions';
import fetchGateOptions from '@/services/access/fetchGateOptions';
import {
  check,
  checkMultiple,
  PERMISSIONS,
  PermissionStatus,
  RESULTS,
} from 'react-native-permissions';
import GetLocation, {
  GetCurrentPositionOptions,
} from 'react-native-get-location';
import BleManager, { BleState } from 'react-native-ble-manager';
import NetInfo from '@react-native-community/netinfo';
import {
  isLocationEnabled,
  promptForEnableLocationIfNeeded,
} from 'react-native-android-location-enabler';
import { getNokeLocks, unlockNokeOverNetwork } from '@/services/api';
import { ApiNokeLockResponse, NokeLock } from '@/types/schemas/Noke';
import NokeModule from '@/noke/NokeModule';
import { SSDore } from '@/components/atoms/SSDoorButton';
import SSAlertDialog from '@/components/molecules/SSAlertDialog';
import { ErrorType } from '@/components/atoms/AccessErrorView';
import { NokeLockAdapter } from '@/adapters/Access/NokeLockAdapter';
import * as Sentry from '@sentry/react-native';
import { getCachedNokeLocks, setCachedNokeLocks } from '@/utils/storage';
import Config from 'react-native-config';
import { useAnalytics } from '@/context/AnalyticsContext';

const allowOutOfRange = Config.ALLOW_OUT_OF_RANGE ?? false;

const { width } = Dimensions.get('window');
export const NavigationContext = React.createContext({
  hasStorage: false,
  setHasStorageState: () => { },
});

// Debug flag
const DEBUG = {
  LOCATIONS: false, // Enable location logging
  GATES: false, // Disable gate logging
  API_CALLS: false, // Enable API call logging
};

const debugLog = (type: keyof typeof DEBUG, ...args: unknown[]) => {
  if (DEBUG[type]) {
    console.log(`[DEBUG:${type}]`, ...args);
  }
};

const logLocationDetails = (locations: Location[]) => {
  if (!DEBUG.LOCATIONS) return;

  debugLog('LOCATIONS', '\n=== LOCATION DETAILS ===');

  locations.forEach((location, index) => {
    debugLog('LOCATIONS', `\nLocation ${index + 1}:`);
    debugLog('LOCATIONS', `  ID: ${location.locationId}`);
    debugLog('LOCATIONS', `  Address: ${location.address}`);
    debugLog('LOCATIONS', `  City: ${location.city}, ${location.state}`);
    debugLog('LOCATIONS', `  Gate System: ${location.gateSystemType}`);

    debugLog('LOCATIONS', '\n  Units:');
    location.units.forEach((unit, unitIndex) => {
      debugLog('LOCATIONS', `    Unit ${unitIndex + 1}:`);
      debugLog('LOCATIONS', `      Unit Number: ${unit.unitNumber}`);
      debugLog('LOCATIONS', `      Account ID: ${unit.accountId}`);
      debugLog('LOCATIONS', `      Facility ID: ${unit.facilityId}`);
    });
  });
};

const logGateOptions = (gates: GateOptions[]) => {
  if (!DEBUG.GATES) return;

  debugLog('GATES', '\n=== GATE OPTIONS ===');
  gates.forEach((gate, index) => {
    debugLog('GATES', `\nGate ${index + 1}:`);
    debugLog('GATES', `  Device ID: ${gate.deviceId}`);
    debugLog('GATES', `  Name: ${gate.friendlyName}`);
    debugLog('GATES', `  Facility ID: ${gate.facilityId}`);
    debugLog('GATES', `  Tenant ID: ${gate.tenantId}`);
  });
};

function Access({ navigation, route }: AccessStackScreenProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const [accessCodes, setAccessCodes] = useState<AccessCode[]>([]);
  const [gateOptions, setGateOptions] = useState<GateOptions[]>([]);
  const [nokeLocks, setNokeLocks] = useState<NokeLock[]>([]);
  const [isGateOpening, setIsGateOpening] = useState<boolean>(false);
  const [isFlatListVisible, setIsFlatListVisible] = useState(true);
  const [flatListLayout, setFlatListLayout] = useState<{
    y: number;
    height: number;
  } | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [bluetoothOn, setBluetoothOn] = useState(false);
  const [bluetoothStatus, setBluetoothStatus] = useState<PermissionStatus>(
    RESULTS.DENIED,
  );
  const [locationStatus, setLocationStatus] = useState<PermissionStatus>(
    RESULTS.DENIED,
  );
  const [latLong, setLatLong] = useState<{ lat: number; long: number }>();
  const [hasInternetAccess, setHasInternetAccess] = useState(false);
  const { setHasStorageState } = useContext(NavigationContext);
  const isFocus = useIsFocused();
  const flatListRef = useRef<FlatList>(null);
  const { facilityId, tenantId, setFacilityId, setTenantId, setCurrentScreen } = useAnalytics();

  const selectedLocationId = route.params?.selectedLocationId ?? 0;
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [fallbackLockData, setFallbackLockData] = useState<
    { mac: string; facilityId: number; tenantId: number } | undefined
  >();

  const GetStorageData = async () => {
    try {
      const response = await fetchLocations();
      logLocationDetails(response);
      setHasStorageState(false);
      return response;
    } catch (error) {
      setHasStorageState(true);
      throw error; // Handle error state
    }
  };

  const queryClient = useQueryClient();
  const { data, error, isLoading, refetch, isRefetching } = useQuery({
    queryKey: ['access_locations'],
    queryFn: () => {
      return GetStorageData();
    },
    refetchOnWindowFocus: false,
  });

  const getAccessCodeMutation = useMutation({
    mutationFn: () => {
      return fetchAccessCode();
    },
    onSuccess: data => {
      const accessCodesArr: AccessCode[] = Adapter.from(data).to(
        (item: ApiAccessCode[]) => {
          return item.map(accessCode => {
            return new AccessCodeAdapter(accessCode).adapt();
          });
        },
      );
      setAccessCodes(accessCodesArr);
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.gateAccessCode, EventStatus.failed, {
        error: JSON.stringify(error),
        facilityId,
        tenantId,
      });
    },
    onSettled: async () => {
      await logEventWithStatus(Events.gateAccessCode, EventStatus.completed, {
        facilityId,
        tenantId,
      });
    },
  });

  const getAccessCode = (item: Location) => {
    const a = item.units.map(unit => {
      const accessCode = accessCodes.find(
        access => access.tenantId === unit.accountId,
      );
      return accessCode?.accessCode || 'Unavailable';
    });
    return a[0];
  };

  const callGetAccessCodeApi = async () => {
    getAccessCodeMutation.reset();
    await logEventWithStatus(Events.gateAccessCode, EventStatus.initiated, {
      facilityId,
      tenantId,
    });
    await getAccessCodeMutation.mutateAsync();
  };

  const UnlockNokeOverNetwork = useMutation({
    mutationFn: (unlockRequest: NokeUnlockOverNetworkRequest) => {
      return unlockNokeOverNetwork(unlockRequest);
    },
    onSuccess: unlockData => {
      console.log(unlockData);
    },
    onError: e => {
      console.error(e);
    },
  });

  const callUnlockNokeOverNetwork = async (
    unlockRequest: NokeUnlockOverNetworkRequest,
  ) => {
    setFallbackLockData(undefined);
    UnlockNokeOverNetwork.reset();
    await UnlockNokeOverNetwork.mutateAsync(unlockRequest);
  };

  const getGatesForAccounts = async (
    facilityId: number,
    accountIds: number[],
  ) => {
    setGateOptions([]);

    for (const tenantId of accountIds) {
      const request: GetGateOptionsRequest = {
        facilityId,
        tenantId,
      };

      try {
        debugLog('API_CALLS', `Fetching gates for tenant ${tenantId}`);
        const apiGates = await fetchGateOptions(request);
        debugLog(
          'API_CALLS',
          `Received gates for tenant ${tenantId}:`,
          apiGates,
        );

        const newGates = apiGates.map(apiGate => {
          return new GateOptionAdapter(apiGate).adapt(facilityId, tenantId);
        });

        setGateOptions(prevGates => {
          const uniqueNewGates = newGates.filter(
            newGate =>
              !prevGates.some(
                existingGate => existingGate.deviceId === newGate.deviceId,
              ),
          );
          const updatedGates = [...prevGates, ...uniqueNewGates];
          logGateOptions(updatedGates);
          return updatedGates;
        });
      } catch (error) {
        debugLog(
          'API_CALLS',
          `Error fetching gates for tenant ${tenantId}:`,
          error,
        );
      }
    }
  };

  const getNokeLocksForAccounts = async (facilityId: number, accountIds: number[]) => {
    try {
      // First try to get from storage
      const request: GetNokeLocksRequest = { facilityId, tenantId: accountIds[0] };
      console.log('[NOKE] Fetching cached locks with request:', request);
      const cachedLocks = await getNokeLocks(request);
      console.log('[NOKE] Cached locks response:', cachedLocks);

      const locks: NokeLock[] = [];
      if (
        cachedLocks?.nokeResponse?.data?.units &&
        cachedLocks.nokeResponse.data.units.length > 0
      ) {
        console.log('[NOKE] Found valid cached locks, processing...');
        cachedLocks.nokeResponse.data.units.forEach(unit => {
          unit.locks?.forEach(apiLock => {
            const adaptedLock = new NokeLockAdapter(apiLock).adapt(facilityId, accountIds[0]);
            console.log('[NOKE] Adapted cached lock:', adaptedLock);
            locks.push(adaptedLock);
          });
        });
        console.log('[NOKE] Setting cached locks:', locks);
        setNokeLocks(locks);
      }

      Sentry.addBreadcrumb({
        category: 'Noke',
        level: 'info',
        message: 'Completed getNokeLocksForAccounts',
        data: {
          facilityId: facilityId.toString(),
          accountIds: accountIds.map(id => id.toString()),
          locksCount: locks?.length || 0,
          locks: locks?.map(l => ({
            id: l.id,
            name: l.name,
            offlineKey: !!l.offlineKey,
            hasUnlockCmd: !!l.unlockCmd
          }))
        }
      });

    } catch (error) {
      console.error('[NOKE] Error in getNokeLocksForAccounts:', error);
      Sentry.captureException(error, {
        extra: {
          facilityId: facilityId.toString(),
          accountIds: accountIds.map(id => id.toString()),
          context: 'getNokeLocksForAccounts'
        }
      });

      // If fetch fails, use cached data
      const request: GetNokeLocksRequest = { facilityId, tenantId: accountIds[0] };
      console.log('[NOKE] Attempting to fetch cached locks after error:', request);
      const cachedLocks = await getNokeLocks(request);
      if (cachedLocks?.nokeResponse?.data?.units && cachedLocks.nokeResponse.data.units.length > 0) {
        console.log('[NOKE] Found valid cached locks after error');
        const locks: NokeLock[] = [];
        cachedLocks.nokeResponse.data.units.forEach(unit => {
          unit.locks?.forEach(apiLock => {
            const adaptedLock = new NokeLockAdapter(apiLock).adapt(facilityId, accountIds[0]);
            console.log('[NOKE] Using cached lock:', adaptedLock);
            locks.push(adaptedLock);
          });
        });
        console.log('[NOKE] Setting cached locks after error:', locks);
        setNokeLocks(locks);
      }
    }
  };

  useEffect(() => {
    if (isFocus && data) {
      void callGetAccessCodeApi();
    }
  }, [isFocus, data]);

  const isLocationInRange = () => {
    if (!latLong || !data?.[currentPage]) return false;
    const location = data[currentPage];

    // get distance to facility in km
    const distance = getDistanceFromLatLonInKm(
      latLong.lat,
      latLong.long,
      location.latitude,
      location.longitude,
    );
    return distance < 3.21869; // 2 miles
  };

  const gates: SSDore[] = data
    ? [
      ...gateOptions.map(gate => ({
        name: gate.friendlyName,
        id: gate.deviceId,
        type: data[currentPage].gateSystemType as GateSystemType,
        facilityId: gate.facilityId,
        tenantId: gate.tenantId,
      })),
      ...nokeLocks.map(lock => ({
        name: lock.name,
        id: lock.mac,
        type: GateSystemType.NOKE,
        nokeOfflineKey: lock.offlineKey,
        nokeOfflineKeyExpiration: lock.offlineKeyObj.offlineExpiration,
        nokeOfflineUnlockCommand: lock.unlockCmd,
        nokeScheduledOfflineUnlockCommand: lock.scheduledUnlockCmd,
        facilityId: lock.facilityId,
        tenantId: lock.tenantId,
      })),
    ]
    : [];

  useEffect(() => {
    if (isFocus && data) {
      setCurrentScreen('Access', {
        gateSystemType: data?.[currentPage]?.gateSystemType,
        hasBluetoothEnabled: bluetoothOn,
        hasLocationEnabled: locationStatus === RESULTS.GRANTED || locationStatus === RESULTS.LIMITED,
        hasInternetAccess,
        gateCount: gates?.length,
        facilityId: data?.[currentPage]?.units[0]?.facilityId?.toString(),
        isInRange: isLocationInRange(),
      });
    }
  }, [
    isFocus,
    data,
    currentPage,
    bluetoothOn,
    locationStatus,
    hasInternetAccess,
    gates?.length,
    setCurrentScreen,
  ]);

  useEffect(() => {
    if (
      data?.[currentPage].gateSystemType === GateSystemType.NOKE &&
      bluetoothStatus === RESULTS.GRANTED &&
      bluetoothOn &&
      isFocus
    ) {
      console.log('initNoke');
      NokeModule.initNoke();
    }
  }, [data, currentPage, bluetoothStatus, bluetoothOn, isFocus]);

  useEffect(() => {
    setGateOptions([]);
    setNokeLocks([]);
    if (!data && hasInternetAccess) {
      void refetch();
    } else if (
      data &&
      isFocus &&
      (locationStatus === RESULTS.GRANTED || locationStatus === RESULTS.LIMITED)
    ) {
      const { facilityId } = data[currentPage].units[0];
      const accountIds: number[] = [];
      data[currentPage].units.forEach(unit => {
        if (!accountIds.includes(unit.accountId)) {
          accountIds.push(unit.accountId);
        }
      });
      if (
        data[currentPage].gateSystemType ===
        (GateSystemType.OPENTECH as string) ||
        data[currentPage].gateSystemType === (GateSystemType.PTI as string)
      ) {
        void getGatesForAccounts(facilityId, accountIds);
      } else if (
        data[currentPage].gateSystemType === (GateSystemType.NOKE as string)
      ) {
        // get cached noke locks for offline mode
        const cachedLocks = getCachedNokeLocks(facilityId);
        setNokeLocks(cachedLocks);
        if (hasInternetAccess) {
          void getNokeLocksForAccounts(facilityId, accountIds);
        } else {
          Sentry.addBreadcrumb({
            category: 'Noke',
            level: 'info',
            message: 'Noke facility accessed in offline mode',
            data: {
              facilityId,
              accountIds,
              locksCount: cachedLocks?.length,
              locks: cachedLocks.map(l => ({
                id: l.id,
                name: l.name,
                offlineKey: !!l.offlineKey,
                hasUnlockCmd: !!l.unlockCmd,
              })),
            },
          });
        }
      }
    }
  }, [data, currentPage, locationStatus, isFocus, hasInternetAccess]);

  useEffect(() => {
    if (isFocus && data && flatListRef && isFlatListVisible) {
      if (selectedLocationId !== 0) {
        setTimeout(() => {
          const locIndex = data.findIndex(
            item => item.locationId === selectedLocationId,
          );

          flatListRef?.current?.scrollToIndex({
            animated: false,
            index: locIndex,
          });
          setCurrentPage(locIndex);
        }, 500);
      }
    }
  }, [flatListRef, data, isFocus, isFlatListVisible]);

  useEffect(() => {
    queryClient.removeQueries({ queryKey: ['access_locations'] });
    void refetch();
  }, [queryClient, refetch]);

  useEffect(() => {
    if (data?.[currentPage]?.units[0]) {
      const unit = data[currentPage].units[0];
      setFacilityId(unit.facilityId.toString());
      setTenantId(unit.accountId.toString());
      console.log('[Analytics] Setting context in Access screen:', {
        facilityId: unit.facilityId.toString(),
        tenantId: unit.accountId.toString()
      });
    }
  }, [currentPage, data, setFacilityId, setTenantId]);

  if (error) {
    setHasStorageState(true);
  }

  if (data) {
    setHasStorageState(false);
  }

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setHasInternetAccess(!!state.isConnected);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const checkLocationStatus = async () => {
    const permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    if (!permission) return;
    const locationResult = await check(permission);
    if (
      locationResult === RESULTS.GRANTED ||
      locationResult === RESULTS.LIMITED
    ) {
      checkGPSStatus(locationResult);
    }
  };

  const checkGPSStatus = async (locationResult: PermissionStatus) => {
    if (Platform.OS === 'android') {
      const checkEnabled: boolean = await isLocationEnabled();
      if (!checkEnabled) {
        try {
          const isEnabled = await promptForEnableLocationIfNeeded();
          if (isEnabled) {
            setLocationStatus(locationResult);
            void getLocation();
          }
        } catch (error: unknown) {
          console.log('Gps Enable Error =>', error);
        }
      } else {
        setLocationStatus(locationResult);
        void getLocation();
      }
    } else {
      setLocationStatus(locationResult);
      void getLocation();
    }
  };

  const getLocation = async () => {
    try {
      const location = await GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 60000,
      } as GetCurrentPositionOptions);
      setLatLong({ lat: location.latitude, long: location.longitude });
    } catch (e) {
      console.log('location error =>', e);
    }
  };

  useEffect(() => {
    let intervalId: ReturnType<typeof setInterval>;
    if (
      isFocus &&
      (locationStatus === RESULTS.GRANTED || locationStatus === RESULTS.LIMITED)
    ) {
      void getLocation();
      intervalId = setInterval(() => {
        void getLocation();
      }, 60000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isFocus, locationStatus, currentPage]);

  const checkBluetoothOn = async () => {
    const state = await BleManager.checkState();
    setBluetoothOn(state === BleState.On);
  };

  const checkBluetoothStatus = async () => {
    const permission = Platform.select({
      ios: [PERMISSIONS.IOS.BLUETOOTH],
      android: [
        PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
      ],
    });
    if (!permission) return;
    const bluetoothResult = await checkMultiple(permission);
    let status: PermissionStatus = RESULTS.DENIED;
    if (Platform.OS === 'ios') {
      status = bluetoothResult['ios.permission.BLUETOOTH'];
    } else if (
      bluetoothResult['android.permission.BLUETOOTH_CONNECT'] ===
      RESULTS.GRANTED &&
      bluetoothResult['android.permission.BLUETOOTH_SCAN'] === RESULTS.GRANTED
    ) {
      status = RESULTS.GRANTED;
    } else {
      status = bluetoothResult['android.permission.BLUETOOTH_CONNECT'];
    }
    setBluetoothStatus(status);
  };

  useEffect(() => {
    if (isFocus) {
      void checkLocationStatus();
      void checkBluetoothOn();
      void checkBluetoothStatus();
      const subscription = AppState.addEventListener('change', nextAppState => {
        if (nextAppState === 'active') {
          void checkLocationStatus();
          void checkBluetoothOn();
          void checkBluetoothStatus();
        }
      });

      return () => {
        subscription.remove();
      };
    }
  }, [isFocus]);

  const loaderView = () => {
    return (
      <View style={[gutters.marginTop_60, layout.flex_1, layout.itemsCenter]}>
        <View
          style={[
            layout.itemsCenter,
            layout.justifySpaceEvenly,
            gutters.gap_12,
          ]}
        >
          <ActivityIndicator size="large" color={borders.blue.borderColor} />
          <Text
            style={[
              fonts.appFontBold,
              fonts.size_24,
              fonts.charcoal,
              gutters.paddingBottom_14,
              gutters.marginHorizontal_20,
              fonts.alignCenter,
            ]}
          >
            Searching for access points, please wait...
          </Text>
        </View>
      </View>
    );
  };

  const onModifyPermissionsAction = () => {
    navigation.navigate('AccountSettings');
  };

  const renderLocation = ({ item }: { item: Location }) => (
    <View
      style={[
        layout.flex_1,
        { width: width - 32 },
        gutters.marginTop_16,
        gutters.marginHorizontal_16,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <SSAccessLocationCard location={item} accessCode={getAccessCode(item)} />
      <View
        style={[
          borders.rounded_16,
          layout.absolute,
          layout.fullWidth,
          layout.fullHeight,
          {
            backgroundColor: isGateOpening
              ? 'rgba(0, 0, 0, 0.3)'
              : 'rgba(0, 0, 0, 0.0)',
          },
        ]}
        pointerEvents={!isGateOpening ? 'none' : 'box-only'}
      />
    </View>
  );

  const renderGate = ({ item, index }: { item: SSDore }) => {
    if (data) {
      return (
        <>
          <SSDoorButton
            item={item}
            icon={GateSvg}
            facilityId={item.facilityId}
            tenantId={item.tenantId}
            accessCode={getAccessCode(data[currentPage]) ?? ''}
            defultDoorState={DoorState.OPEN}
            progress={0}
            buttonOnPress={() => {
              setSelectedIndex(index);
            }}
            isGateButtonOpening={isGateButtonOpening => {
              setIsGateOpening(isGateButtonOpening);
              if (selectedIndex > -1) {
                setSelectedIndex(-1);
              }
            }}
            onError={
              item.type === GateSystemType.NOKE && hasInternetAccess
                ? () =>
                  setFallbackLockData({
                    mac: item.id,
                    facilityId: item.facilityId,
                    tenantId: item.tenantId,
                  })
                : undefined
            }
          />
          <View
            style={[
              borders.rounded_16,
              layout.absolute,
              gutters.marginTop_16,
              gutters.paddingHorizontal_16,
              layout.fullWidth,
              layout.h_56,
              {
                backgroundColor:
                  isGateOpening && selectedIndex !== index
                    ? 'rgba(0, 0, 0, 0.3)'
                    : 'rgba(0, 0, 0, 0.0)',
              },
            ]}
            pointerEvents={!isGateOpening ? 'none' : 'box-only'}
          />
        </>
      );
    }
    return null;
  };

  const getItemLayout = (data: string | any[], index: number) => ({
    length: data?.length,
    offset: Dimensions.get('window').width * index,
    index,
  });

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollY = event.nativeEvent.contentOffset?.y ?? 0;
    const screenHeight = event.nativeEvent.layoutMeasurement?.height ?? 0;

    if (flatListLayout) {
      const isVisible =
        scrollY < flatListLayout.y + (flatListLayout.height - 20) &&
        scrollY + screenHeight > flatListLayout.y;

      if (isFlatListVisible !== isVisible) {
        setIsFlatListVisible(isVisible);
      }
    }
  };

  const handleFlatListLayout = (event: LayoutChangeEvent) => {
    const { y, height } = event.nativeEvent.layout;
    setFlatListLayout({ y, height });
  };

  let errorType;
  if (data?.[currentPage] && getAccessCode(data[currentPage]) === 'Unavailable') {
    errorType = ErrorType.accessCodeUnavailable
  } else if (data?.[currentPage].gateSystemType === GateSystemType.NONE) {
    errorType = ErrorType.digitalAccessUnavailable;
  } else if (
    !hasInternetAccess &&
    (data?.[currentPage].gateSystemType !== GateSystemType.NOKE || !nokeLocks)
  ) {
    errorType = ErrorType.noInternetConnection;
  } else if (
    data?.[currentPage].gateSystemType === GateSystemType.NOKE &&
    !bluetoothOn
  ) {
    errorType = ErrorType.bluetoothDisabled;
  } else if (
    locationStatus !== RESULTS.GRANTED &&
    locationStatus !== RESULTS.LIMITED
  ) {
    errorType = ErrorType.insufficientAppPermissions;
  } else if (
    data?.[currentPage].gateSystemType === GateSystemType.NOKE &&
    bluetoothStatus !== RESULTS.GRANTED
  ) {
    errorType = ErrorType.insufficientAppPermissions;
  } else if (!isLocationInRange() && !allowOutOfRange) {
    errorType = ErrorType.outOfRange;
  }

  return (
    <View style={[layout.flex_1, backgrounds.bluePrimary]}>
      {data && !isFlatListVisible && (
        <View
          style={[
            backgrounds.white,
            gutters.padding_16,
            borders.bottomLeftRounded_16,
            borders.bottomRightRounded_16,
            layout.itemsCenter,
            layout.justifyCenter,
          ]}
        >
          <Text style={[fonts.size_20, fonts.charcoal, fonts.appFontBold]}>
            Access Code: {getAccessCode(data[currentPage])}
          </Text>
        </View>
      )}
      <ScrollView
        contentContainerStyle={[layout.flexGrow_1, backgrounds.blue10]}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            tintColor={backgrounds.white.backgroundColor}
            refreshing={isRefetching}
            onRefresh={() => void refetch()}
          />
        }
      >
        <View
          style={[
            layout.h_140,
            backgrounds.bluePrimary,
            layout.justifyCenter,
            layout.fullWidth,
            borders.bottomLeftRounded_16,
            borders.bottomRightRounded_16,
          ]}
        />
        <View
          style={[
            layout.flex_1,
            layout.col,
            layout.justifyBetween,
            { marginTop: -120 },
          ]}
        >
          {!isLoading && (
            <>
              <View style={[layout.flex_1]}>
                {data && (
                  <View>
                    <FlatList
                      ref={flatListRef}
                      data={data}
                      extraData={{ hasInternetAccess }}
                      horizontal
                      pagingEnabled
                      scrollEnabled={!isGateOpening}
                      getItemLayout={getItemLayout}
                      showsHorizontalScrollIndicator={false}
                      renderItem={renderLocation}
                      keyExtractor={item => item.locationId.toString()}
                      initialScrollIndex={currentPage}
                      onScrollToIndexFailed={info => {
                        console.log('onScrollToIndexFailed'), info;
                        setTimeout(() => {
                          flatListRef.current?.scrollToIndex({
                            index: currentPage,
                            animated: false,
                          });
                        }, 100); // Retry after a slight delay
                      }}
                      onMomentumScrollEnd={event => {
                        const page = Math.round(
                          event.nativeEvent.contentOffset.x / width,
                        );
                        if (currentPage !== page) {
                          setCurrentPage(page);
                        }
                      }}
                      onLayout={handleFlatListLayout}
                    />
                    {data?.length > 1 && (
                      <View style={[layout.selfCenter, gutters.marginTop_16]}>
                        <PageIndicator
                          color={fonts.primary.color}
                          activeColor={fonts.primary.color}
                          count={data?.length}
                          current={currentPage}
                        />
                      </View>
                    )}
                    {gates.length > 0 && !errorType && (
                      <View style={[gutters.marginHorizontal_16]}>
                        <FlatList
                          data={gates}
                          extraData={{ hasInternetAccess }}
                          renderItem={renderGate}
                          keyExtractor={item => item.id.toString()}
                          scrollEnabled={false}
                        />
                      </View>
                    )}
                  </View>
                )}
                {!!errorType && (
                  <AccessErrorView
                    error={errorType}
                    onModifyPermissionsAction={() =>
                      onModifyPermissionsAction()
                    }
                  />
                )}
              </View>
              {gates?.length === 0 &&
                locationStatus === RESULTS.GRANTED &&
                !errorType &&
                loaderView()}
              <View style={[gutters.marginBottom_24, gutters.marginTop_32]}>
                <Text
                  style={[
                    layout.selfCenter,
                    fonts.size_14,
                    fonts.charcoal,
                    fonts.appFontRegular,
                  ]}
                >
                  {'Need Support? '}
                  <Text
                    style={[
                      fonts.size_14,
                      fonts.primary,
                      fonts.appFontMedium,
                      fonts.underline,
                    ]}
                    onPress={() => navigation.navigate('Support')}
                  >
                    Let Us Help
                  </Text>
                </Text>
              </View>
              {isGateOpening ? (
                <View
                  style={[
                    layout.absolute,
                    layout.fullWidth,
                    layout.fullHeight,
                    {
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      zIndex: -1,
                    },
                  ]}
                />
              ) : null}
            </>
          )}
        </View>
        <ActivityLoader isLoading={isLoading} />
      </ScrollView>
      {!!fallbackLockData && data && (
        <SSAlertDialog
          title="We are having trouble reaching your device"
          description="Would you like to unlock over Wifi? This could take up to 30 seconds"
          negativeButtonLabel="Cancel"
          negativeButton={() => setFallbackLockData(undefined)}
          positiveButtonLabel="Unlock"
          positiveButton={() =>
            void callUnlockNokeOverNetwork({
              facilityId: fallbackLockData.facilityId,
              tenantId: fallbackLockData.tenantId,
              lockMacAddress: fallbackLockData.mac,
            })
          }
        />
      )}
    </View>
  );
}

export default Access;
