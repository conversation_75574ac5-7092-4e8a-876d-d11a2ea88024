import React, { useMemo, useState, useEffect } from 'react';
import {
  Text,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useTheme } from '@/theme';
import { PlusIconSVG } from '@/assets/svg';
import {
  ActivityLoader,
  RadioToggle,
  SSButton,
  SSDropdownInput,
  SSTextInput,
} from '@/components/atoms';
import { PaymentStackScreenProps } from '@/types/navigation';
import { BillingAddress } from '@/types/schemas/account';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getStates } from '@/services/api';
import { addBillingAddress } from '@/services/address';
import { ApiStateResponse, State } from '@/types/schemas/state';
import Adapter from '@/adapters/Adapter';
import { StateAdapter } from '@/adapters/State/StateAdapter';
import { EventStatus, Events, logEventWithStatus } from '@/utils/analytics';
import SSSuccessDialog from '@/components/molecules/SSSuccessDialog';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

function AddBillingAddress({ navigation, route }: PaymentStackScreenProps) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const isEditMode = route.params?.isEdit ?? false;
  const addressItem: BillingAddress = route.params?.item;
  const [isError, setIsError] = useState<boolean>(false);
  const [stateArray, setStateArray] = useState<State[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();
  // showSuccessDialog
  const [addressData, setAddressData] = useState({
    address: addressItem?.address ?? '',
    aptNumber: addressItem?.appartment ?? '',
    state: '',
    stateValue: addressItem?.state ?? '',
    zipCode: addressItem?.zipCode ?? '',
    isPrimary: addressItem?.isPrimary ?? false,
    city: addressItem?.city ?? '',
  });

  navigation.setOptions({
    title: isEditMode ? 'Edit Address' : 'New Address',
  });

  const setValues = (key: string, value: string | boolean | number) => {
    setAddressData(previousState => ({
      ...previousState,
      [key]: value,
    }));
  };

  const fetchState = async () => {
    setIsLoading(true);
    const response = await getStates();
    if (response.success) {
      const stateData: State[] = Adapter.from(response).to(
        (item: ApiStateResponse) => new StateAdapter(item).adapt(),
      );
      setStateArray(stateData);
      const state =
        stateData.find(item => item.abbreviation === addressItem?.state)
          ?.name ?? '';
      setValues('state', state);
    } else {
      setIsError(true);
    }
    setIsLoading(false);
    return response;
  };

  useQuery({
    queryKey: ['getStateData'],
    queryFn: fetchState,
  });

  const addressMutation = useMutation({
    mutationFn: () => {
      return addBillingAddress({
        address: addressData.address,
        apartment: addressData.aptNumber,
        city: addressData.city,
        state: addressData.stateValue,
        zipCode: addressData.zipCode,
        setPrimary: addressData.isPrimary,
      });
    },
    onSuccess: data => {
      setIsLoading(false);
      if (data.success) {
        setShowSuccessDialog(true);
      } else {
        Alert.alert('Error', data.message ?? '');
      }
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.addBillingAddress, EventStatus.failed, {
        error: JSON.stringify(error),
      });
      Alert.alert('Error', error.message ?? '');
    },
    onSettled: async data => {
      if (data) {
        setIsLoading(false);
        await logEventWithStatus(
          Events.addBillingAddress,
          EventStatus.completed,
        );
      }
    },
  });

  const callAddBillingAddressApi = async () => {
    setIsLoading(true);
    addressMutation.reset();
    await logEventWithStatus(Events.addBillingAddress, EventStatus.initiated);
    await addressMutation.mutateAsync();
  };

  const isFormReady = useMemo(() => {
    if (
      !addressData.address ||
      !addressData.state ||
      !addressData.city ||
      !addressData.zipCode
    ) {
      return false;
    }
    return true;
  }, [addressData]);

  const submitAddress = () => {
    void callAddBillingAddressApi();
  };

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('AddBillingAddress', {
        isEditMode,
        hasAddress: Boolean(addressData.address),
        hasAptNumber: Boolean(addressData.aptNumber),
        hasState: Boolean(addressData.state),
        hasCity: Boolean(addressData.city),
        hasZipCode: Boolean(addressData.zipCode),
        isPrimaryAddress: addressData.isPrimary,
        isFormComplete: isFormReady,
        isLoading,
        hasError: isError,
        showingSuccessDialog: showSuccessDialog,
        availableStates: stateArray?.length ?? 0,
      });
    }
  }, [
    isFocused,
    isEditMode,
    addressData,
    isFormReady,
    isLoading,
    isError,
    showSuccessDialog,
    stateArray?.length ?? 0,
    setCurrentScreen,
  ]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[layout.flex_1]}
    >
      <View style={[layout.flex_1, backgrounds.blue10]}>
        <ScrollView style={[gutters.padding_15]}>
          <View
            style={[
              backgrounds.white,
              borders.rounded_12,
              gutters.padding_15,
              gutters.marginVertical_10,
              gutters.gap_15,
            ]}
          >
            <Text style={[fonts.appFontBold, fonts.charcoal, fonts.size_20]}>
              {isEditMode ? 'Edit Address' : 'New Address'}
            </Text>
            <View
              style={[layout.fullWidth, layout.h_1, backgrounds.springGrass20]}
            />
            <SSTextInput
              label="Address*"
              value={addressData.address}
              autoCapitalize="words"
              isError={isError}
              onChangeText={(val: string) => setValues('address', val)}
            />
            <SSTextInput
              label="Apt Number (Optional)"
              value={addressData.aptNumber}
              isError={isError}
              onChangeText={(val: string) => setValues('aptNumber', val)}
            />
            <SSTextInput
              label="City*"
              value={addressData.city}
              autoCapitalize="words"
              isError={isError}
              onChangeText={(val: string) => setValues('city', val)}
            />
            <View
              style={[
                layout.fullWidth,
                layout.row,
                gutters.gap_15,
                layout.justifyBetween,
              ]}
            >
              <View style={[layout.percentW_48]}>
                <SSDropdownInput
                  label="State*"
                  value={addressData.state}
                  isError={isError}
                  data={stateArray.map(state => ({
                    label: state.name,
                    value: state.abbreviation,
                  }))}
                  onSelect={(item: { label: string; value: string }) => {
                    setValues('stateValue', item.value);
                    setValues('state', item.label);
                  }}
                />
              </View>
              <View style={[layout.percentW_48, layout.h_60]}>
                <SSTextInput
                  label="Zip Code*"
                  isError={isError}
                  value={addressData.zipCode}
                  onChangeText={(val: string) => setValues('zipCode', val)}
                />
              </View>
            </View>
            <View style={[layout.row, gutters.gap_8]}>
              <RadioToggle
                isSelect={addressData.isPrimary}
                onPress={() => setValues('isPrimary', !addressData.isPrimary)}
              />
              <Text
                style={[fonts.appFontRegular, fonts.charcoal, fonts.size_14]}
              >
                Make this my default address
              </Text>
            </View>
          </View>
        </ScrollView>
        <View style={[gutters.margin_20]}>
          <SSButton
            title="add address"
            disabled={!isFormReady}
            variant={isFormReady ? 'dark' : 'transparent'}
            fontColorStyle={isFormReady ? fonts.white : fonts.lightGray}
            customBorderColorStyle={
              isFormReady ? borders.blue : borders.lightGray
            }
            fontSizeStyle={fonts.size_18}
            borderRadiusStyle={borders.rounded_25}
            onPress={() => submitAddress()}
            customIcon={
              <PlusIconSVG
                fill={
                  isFormReady
                    ? backgrounds.white.backgroundColor
                    : backgrounds.lightGray.backgroundColor
                }
              />
            }
          />
        </View>
      </View>
      <ActivityLoader isLoading={isLoading} />
      {showSuccessDialog && (
        <SSSuccessDialog
          onClose={() => {
            // setshowSuccessDialog(false);
            navigation.goBack();
          }}
          buttonTitle="return to addresses"
          title="Your Address Has Been Updated"
          description="Your new address has been saved successfully."
        />
      )}
    </KeyboardAvoidingView>
  );
}

export default AddBillingAddress;
