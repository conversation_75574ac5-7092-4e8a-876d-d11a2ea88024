import React, { useEffect, useState } from 'react';
import { FlatList, View, Text, ScrollView } from 'react-native';
import { useTheme } from '@/theme';
import { ActivityLoader, SSButton, SavedAddressCard } from '@/components/atoms';
import Adapter from '@/adapters/Adapter';
import { useIsFocused } from '@react-navigation/native';
import { fetchAccountData } from '@/services/accounts';
import { useMutation } from '@tanstack/react-query';
import { AccountAdapter } from '@/adapters/Accounts/AccountAdapter';
import {
  AccountData,
  ApiAccountResponse,
  BillingAddress,
} from '@/types/schemas/account';
import { EventStatus, Events, logEventWithStatus } from '@/utils/analytics';
import { deleteAddress, setPrimaryAddress } from '@/services/address';
import { PlusIconSVG } from '@/assets/svg';
import { PaymentStackScreenProps } from '@/types/navigation';
import SSAlertDialog from '@/components/molecules/SSAlertDialog';

type ListItem = {
  item: BillingAddress;
};

function SavedAddress({ navigation }: PaymentStackScreenProps) {
  const { layout, gutters, fonts, backgrounds, borders } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteAlertDialog, setShowDeleteAlertDialog] = useState(false);
  const [selectedAddressId, setSelectedAddressId] = useState<number>();
  const isFocused = useIsFocused();
  const [savedAddresses, setSavedAddresses] = useState<Array<BillingAddress>>();

  const accountMutation = useMutation({
    mutationFn: () => {
      return fetchAccountData();
    },
    onSuccess: data => {
      setIsLoading(false);
      const accountData: AccountData = Adapter.from(data).to(
        (item: ApiAccountResponse) => new AccountAdapter(item).adapt(),
      );
      const sortedBillingAddresses = [...accountData.billingAddresses].sort(
        (a, b) => Number(b.isPrimary) - Number(a.isPrimary),
      );
      setSavedAddresses(sortedBillingAddresses);
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.account, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      setIsLoading(false);
      if (data) {
        await logEventWithStatus(Events.account, EventStatus.completed);
      }
    },
  });

  const callAccountDataApi = async () => {
    setIsLoading(true);
    accountMutation.reset();
    await logEventWithStatus(Events.account, EventStatus.initiated);
    await accountMutation.mutateAsync();
  };

  const deleteAddressMutation = useMutation({
    mutationFn: (addressId: number) => {
      return deleteAddress({ addressId });
    },
    onSuccess: async () => {
      accountMutation.reset();
      await logEventWithStatus(Events.account, EventStatus.initiated);
      await accountMutation.mutateAsync();
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.account, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      setIsLoading(false);
      if (data) {
        await logEventWithStatus(Events.account, EventStatus.completed);
      }
    },
  });

  const setAsDefaultAddressMutation = useMutation({
    mutationFn: (addressId: number) => {
      return setPrimaryAddress({ addressId });
    },
    onSuccess: () => {
      setIsLoading(false);
      void callAccountDataApi();
    },
    onError: async error => {
      console.error(error);
      setIsLoading(false);
      await logEventWithStatus(Events.account, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      setIsLoading(false);
      if (data) {
        await logEventWithStatus(Events.account, EventStatus.completed);
      }
    },
  });

  const callSetPrimaryAddressApi = async (addressId: number) => {
    setIsLoading(true);
    setAsDefaultAddressMutation.reset();
    await logEventWithStatus(Events.account, EventStatus.initiated);
    await setAsDefaultAddressMutation.mutateAsync(addressId);
  };

  const callDeleteAddressApi = async (addressId: number) => {
    setIsLoading(true);
    deleteAddressMutation.reset();
    await logEventWithStatus(Events.account, EventStatus.initiated);
    await deleteAddressMutation.mutateAsync(addressId);
  };

  useEffect(() => {
    if (isFocused) {
      void callAccountDataApi();
    }
  }, [isFocused]);

  const onEditPress = (item: BillingAddress) => {
    navigation.navigate({
      name: 'AddBillingAddress',
      params: { item, isEdit: true },
    });
  };
  const onDeletePress = (item: BillingAddress) => {
    setShowDeleteAlertDialog(true);
    setSelectedAddressId(item.addressId);
  };
  const onSetAsDefaultPress = (item: BillingAddress) => {
    void callSetPrimaryAddressApi(item.addressId);
  };

  const renderListItem = ({ item }: ListItem) => {
    return (
      <SavedAddressCard
        onEditPress={() => onEditPress(item)}
        onDeletePress={() => onDeletePress(item)}
        onSetAsDefaultPress={() => onSetAsDefaultPress(item)}
        item={item}
      />
    );
  };

  const renderHeader = () => (
    <View style={[layout.fullWidth, gutters.gap_6, gutters.marginBottom_20]}>
      <Text style={[fonts.appFontBold, fonts.size_32, fonts.white]}>
        Saved Addresses
      </Text>
      <Text style={[fonts.appFontRegular, fonts.size_14, fonts.white]}>
        Please add or remove your billing addresses below.
      </Text>
    </View>
  );

  return (
    <View style={[layout.flex_1, layout.col, backgrounds.blue10]}>
      <ScrollView contentContainerStyle={[layout.flexGrow_1]} scrollEnabled>
        <View style={[backgrounds.blue10, layout.flex_1]}>
          <View
            style={[
              layout.h_208,
              backgrounds.bluePrimary,
              layout.justifyCenter,
              borders.bottomLeftRounded_16,
              borders.bottomRightRounded_16,
            ]}
          />
          <View
            style={[
              layout.fullWidth,
              layout.fullHeight,
              gutters.paddingHorizontal_16,
              { marginTop: -180 },
            ]}
          >
            <FlatList
              data={savedAddresses}
              renderItem={renderListItem}
              style={[layout.flex_1, gutters.paddingBottom_20]}
              contentContainerStyle={[gutters.gap_10]}
              ListHeaderComponent={renderHeader()}
              scrollEnabled={false}
            />
          </View>
        </View>
      </ScrollView>
      <View
        style={[
          layout.fullWidth,
          gutters.paddingHorizontal_16,
          gutters.marginBottom_20,
        ]}
      >
        <SSButton
          title="ADD ADDRESS"
          customIcon={<PlusIconSVG />}
          variant="transparent"
          onPress={() => navigation.navigate('AddBillingAddress')}
        />
      </View>
      <ActivityLoader isLoading={isLoading} />
      {showDeleteAlertDialog && selectedAddressId && (
        <SSAlertDialog
          positiveButton={() => {
            setShowDeleteAlertDialog(false);
            void callDeleteAddressApi(selectedAddressId);
          }}
          negativeButton={() => {
            setShowDeleteAlertDialog(false);
          }}
          negativeButtonLabel="cancel"
          positiveButtonLabel="Delete Address"
          title="Delete Address?"
          description="Are you sure you want to delete this address? This action cannot be undone."
        />
      )}
    </View>
  );
}

export default SavedAddress;
