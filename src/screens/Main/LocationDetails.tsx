import React, { useEffect } from 'react';
import { Image, Text, View, ScrollView, StatusBar } from 'react-native';
import { useTheme } from '@/theme';
import {
  HomeStackParamList,
  HomeStackScreenProps,
  LocationDetailsParams,
} from '@/types/navigation';
import { Location } from '@/types/schemas/location';
import { Unit } from '@/types/schemas/unit';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { getNoteStorageKeyForUnitId } from '@/utils/storage';
import useStorage from '@/hooks/useStorage';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

function LocationDetailsCard({ location }: { location: Location }) {
  const { layout, gutters, backgrounds, borders, fonts } = useTheme();

  return (
    <View
      style={[
        layout.col,
        gutters.padding_8,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <View
        style={[
          layout.row,
          layout.justifyBetween,
          borders.bottom_1,
          borders.gray100,
        ]}
      >
        <View style={[layout.flex_1]}>
          <Image />
        </View>

        <View style={[layout.flex_1, layout.col, gutters.padding_24]}>
          <Text>{location.address}</Text>
          <Text>
            {location.city}, {location.state} {location.zip}
          </Text>
          <Text style={[gutters.marginTop_8]}>
            {location?.units?.length ?? 0} units at this location
          </Text>
        </View>
      </View>
      <View
        style={[
          layout.row,
          layout.justifyBetween,
          layout.itemsCenter,
          gutters.padding_12,
        ]}
      >
        <Text style={[layout.flex_1, fonts.alignCenter]}>Directions</Text>
        <Text style={[layout.flex_1, fonts.alignCenter]}>Call</Text>
      </View>
    </View>
  );
}

function UnitDetailsCard({ unit }: { unit: Unit }) {
  const { layout, gutters, backgrounds, borders, fonts } = useTheme();
  const storage = useStorage('global-storage');
  const navigation = useNavigation<NavigationProp<HomeStackParamList>>();

  const noteStorageKey = getNoteStorageKeyForUnitId(unit.rentalId.toString());
  const unitNotes = storage.getString(noteStorageKey) ?? '';

  const handleOnUnitNotesPressed = (item: Unit) => {
    navigation.navigate('MainLocationUnitNotes', { unit: item });
  };

  return (
    <View
      style={[
        layout.col,
        gutters.paddingVertical_20,
        gutters.paddingHorizontal_10,
        gutters.marginTop_8,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <View
        style={[
          layout.col,
          layout.justifyBetween,
          borders.bottom_1,
          borders.gray100,
          gutters.gap_12,
          gutters.paddingBottom_16,
        ]}
      >
        <View>
          <Text style={[fonts.size_16]}>Unit {unit.unitNumber}</Text>
        </View>

        <View style={[layout.row, gutters.gap_16]}>
          <Text style={[fonts.size_12]}>
            Rent{' '}
            <Text style={[fonts.medium]}>${unit.unitTotal.toFixed(2)}</Text>
          </Text>
          <Text style={[fonts.size_12]}>
            Due on <Text style={[fonts.medium]}>{unit.nextPaymentDue}</Text>
          </Text>
          <Text style={[fonts.size_12]}>
            Autopay is{' '}
            <Text style={[fonts.medium, fonts.underline]}>
              {unit.autopay ? 'ON' : 'OFF'}
            </Text>
          </Text>
        </View>

        <View>
          <Text style={[fonts.size_12, fonts.medium]}>
            Notes: <Text style={[fonts.normal]}>{unitNotes}</Text>
          </Text>
        </View>
      </View>
      <View
        style={[
          layout.row,
          layout.justifyBetween,
          layout.itemsCenter,
          gutters.paddingTop_12,
          gutters.gap_20,
        ]}
      >
        <View style={[layout.flex_1]}>
          <SSButton
            title="Unit Notes"
            onPress={() => handleOnUnitNotesPressed(unit)}
          />
        </View>
        <View style={[layout.flex_1]}>
          <SSButton title="Make Payment" />
        </View>
      </View>
    </View>
  );
}

function UnitDetailsSection({ units }: { units: Unit[] }) {
  const { layout, gutters, fonts } = useTheme();

  const renderUnits = () => {
    return units.map((unit: Unit) => {
      return <UnitDetailsCard key={unit.rentalId} unit={unit} />;
    });
  };

  return (
    <View style={[layout.flex_1, layout.col, gutters.padding_16]}>
      <Text style={[fonts.size_16, gutters.paddingTop_12]}>
        Unit Information
      </Text>
      {renderUnits()}
    </View>
  );
}

function LocationDetails({ route, navigation }: HomeStackScreenProps) {
  const { layout } = useTheme();
  const routeParams = route.params as LocationDetailsParams;
  const { location } = routeParams;
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (isFocused && location) {
      setCurrentScreen('LocationDetails', {
        facilityId: location.units[0]?.facilityId?.toString(),
        tenantId: location.units[0]?.accountId?.toString(),
        locationId: location.locationId?.toString(),
        unitCount: location.units?.length,
        hasUnits: location.units?.length > 0,
        address: location.address ?? '',
        city: location.city ?? '',
        state: location.state ?? '',
        zip: location.zip ?? '',
      });
    }
  }, [isFocused, location, setCurrentScreen]);

  return (
    <ScrollView style={[layout.flex_1, layout.col]}>
      <LocationDetailsCard location={location} />
      <UnitDetailsSection units={location.units} />
    </ScrollView>
  );
}

export default LocationDetails;
