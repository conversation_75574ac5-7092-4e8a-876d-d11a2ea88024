import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  View,
  Text,
  FlatList,
  Linking,
  RefreshControl,
} from 'react-native';
import { useTheme } from '@/theme';
import {
  ActivityLoader,
  ListCardButton,
  PaymentInfo,
  SSButton,
} from '@/components/atoms';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaymentStackScreenProps } from '@/types/navigation';
import { EventStatus, Events, logEventWithStatus } from '@/utils/analytics';
import { ApiWebViewResponse, WebViewUrl } from '@/types/schemas/webview';
import Adapter from '@/adapters/Adapter';
import { OneTimePaymentAdapter } from '@/adapters/Payments/OneTimePaymentAdapter';
import { GetAutopayPaymentAdapter } from '@/adapters/Payments/GetAutopayPaymentAdapter';
import { fetchMakeAutopayUrl, fetchMakePaymentUrl } from '@/services/payments';
import fetchLocations from '@/services/locations/fetchLocations';
import { Unit } from '@/types/schemas/unit';
import { useIsFocused } from '@react-navigation/native';
import AddStorageAccountDialog from '@/components/molecules/AddStorageAccountDialog';

const tekNumber = '(833) 374-0597';

type ListItem = {
  item: Unit;
};

function Payments({ navigation }: PaymentStackScreenProps) {
  const { layout, gutters, backgrounds, fonts, borders } = useTheme();
  const [showAddUnitDialog, setShowAddUnitDialog] = useState<boolean>(false);
  const [isUnitAdded, setIsUnitAdded] = useState<boolean>(false);
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const [selectedUnit, setSelectedUnit] = useState<number>(-1);

  const isFocused = useIsFocused();
  const queryClient = useQueryClient();

  const paymentUnitInfoQuery = useQuery({
    queryKey: ['fetch_units_payment'],
    queryFn: () => {
      return fetchLocations();
    },
    select: data => {
      const allUnits: Unit[] = [];
      data.forEach(loc => {
        allUnits.push(...[...loc.units]);
      });
      return allUnits;
    },
  });

  useEffect(() => {
    if (isFocused) {
      // Clear the cache and refetch data when the component mounts
      queryClient.removeQueries({ queryKey: ['fetch_units_payment'] });
      void paymentUnitInfoQuery.refetch();
    }
  }, [isFocused, queryClient]);

  const getOneTimePaymentMutation = useMutation({
    mutationFn: (item: Unit) => {
      return fetchMakePaymentUrl({
        facilityId: item.facilityId,
        accountId: item.accountId,
        rentalId: item.rentalId,
        deepLinkCallback: 'payments',
      });
    },
    onSuccess: data => {
      const oneTimePaymentUrl: WebViewUrl = Adapter.from(data).to(
        (item: ApiWebViewResponse) => new OneTimePaymentAdapter(item).adapt(),
      );
      if (oneTimePaymentUrl?.webviewUrl) {
        navigation.navigate({
          name: 'PaymentWebView',
          params: { uri: oneTimePaymentUrl?.webviewUrl, title: 'Payment' },
        });
      }
    },
    onError: async error => {
      console.error(error);
      await logEventWithStatus(Events.oneTimePayment, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        await logEventWithStatus(Events.oneTimePayment, EventStatus.completed);
      }
    },
  });

  const resetToggleState = () => {
    setTimeout(() => {
      setIsToggled(false);
      setSelectedUnit(-1);
    }, 100);
  };

  const getAutoPayUrlMutation = useMutation({
    mutationFn: (item: Unit) => {
      return fetchMakeAutopayUrl({
        facilityId: item.facilityId,
        accountId: item.accountId,
        rentalId: item.rentalId,
        deepLinkCallback: 'payments',
      });
    },
    onSuccess: data => {
      resetToggleState();
      const autoPayUrl: WebViewUrl = Adapter.from(data).to(
        (item: ApiWebViewResponse) =>
          new GetAutopayPaymentAdapter(item).adapt(),
      );
      if (autoPayUrl?.webviewUrl) {
        navigation.navigate({
          name: 'PaymentWebView',
          params: { uri: autoPayUrl?.webviewUrl, title: 'Set Up SmartPay' },
        });
      }
    },
    onError: async error => {
      resetToggleState();
      console.error(error);
      await logEventWithStatus(Events.autopayAdd, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      resetToggleState();
      if (data) {
        await logEventWithStatus(Events.autopayAdd, EventStatus.completed);
      }
    },
  });

  const callGetOneTimePaymentApi = async (item: Unit) => {
    await logEventWithStatus(Events.oneTimePayment, EventStatus.initiated);
    getOneTimePaymentMutation.reset();
    await getOneTimePaymentMutation.mutateAsync(item);
  };

  const callGetAutoPayUrlApi = async (item: Unit) => {
    await logEventWithStatus(Events.autopayAdd, EventStatus.initiated);
    getAutoPayUrlMutation.reset();
    await getAutoPayUrlMutation.mutateAsync(item);
  };

  const onOneTimeMakePaymentAction = (item: Unit) => {
    if (item.canMakePayment) {
      void callGetOneTimePaymentApi(item);
    }
  };

  const onAutopayPaymentAction = (item: Unit) => {
    if (!item.autopay) {
      setIsToggled(true);
      setSelectedUnit(item.rentalId);
      void callGetAutoPayUrlApi(item);
    } else {
      navigation.navigate({
        name: 'AutoPayTurnOffAlert',
        params: { unitInfo: item },
      });
    }
  };

  const renderUnitItem = ({ item }: ListItem) => {
    let toggleVisible = false;
    if (paymentUnitInfoQuery.data) {
      toggleVisible = paymentUnitInfoQuery.data.length > 1;
    }
    return (
      <PaymentInfo
        unit={item}
        isToggled={selectedUnit === item.rentalId ? isToggled : false}
        isToggleVisible={toggleVisible}
        onPress={() => onOneTimeMakePaymentAction(item)}
        onToggleAutoPayCallBack={() => onAutopayPaymentAction(item)}
      />
    );
  };

  const handleTelSupport = (number: string) => {
    Linking.canOpenURL(`tel:${number}`)
      .then(yes => {
        if (yes) {
          void Linking.openURL(`tel:${number}`);
        }
      })
      .catch((err: unknown) => err);
  };

  const onFindStorageAccountNumber = () => {
    setShowAddUnitDialog(false);
    setTimeout(
      () =>
        navigation.navigate('FindStorageNumber', {
          isFromHome: false,
          isFromPayment: true,
        }),
      100,
    );
  };

  const onRefresh = () => {
    void paymentUnitInfoQuery.refetch();
  };

  return (
    <View style={[layout.flex_1, layout.col, backgrounds.bluePrimary]}>
      <ScrollView
        contentContainerStyle={[layout.flexGrow_1]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            tintColor={backgrounds.white.backgroundColor}
            refreshing={paymentUnitInfoQuery.isRefetching}
            onRefresh={onRefresh}
          />
        }
      >
        <View style={[backgrounds.blue10, layout.flex_1]}>
          <View
            style={[
              layout.flex_1,
              gutters.padding_16,
              layout.fullWidth,
              { zIndex: 1 },
            ]}
          >
            <View>
              <FlatList
                data={paymentUnitInfoQuery.data}
                renderItem={renderUnitItem}
              />
              <View style={[gutters.paddingVertical_16, gutters.gap_20]}>
                <Text
                  style={[
                    fonts.size_20,
                    fonts.appFontBold,
                    fonts.charcoal,
                    gutters.marginRight_120,
                  ]}
                >
                  Don&apos;t see your storage unit here?
                </Text>
                <Text
                  style={[
                    fonts.size_16,
                    fonts.appFontRegular,
                    fonts.charcoal,
                    { lineHeight: 24 },
                  ]}
                >
                  Check your reserve or rent emails to find your storage account
                  number, or call{' '}
                  <Text
                    suppressHighlighting
                    style={[fonts.primary, fonts.underline]}
                    onPress={() => handleTelSupport(tekNumber)}
                  >
                    {tekNumber}
                  </Text>{' '}
                  for assistance.
                </Text>
                <SSButton
                  title="add storage unit"
                  variant="dark"
                  onPress={() => setShowAddUnitDialog(true)}
                />
                <View>
                  <ListCardButton
                    title="Manage Billing Address"
                    onPress={() => navigation.navigate('SavedAddress')}
                  />
                  <ListCardButton
                    title="Payment History"
                    onPress={() => navigation.navigate('PaymentHistory')}
                  />
                </View>
              </View>
            </View>
          </View>
          {paymentUnitInfoQuery.data && (
            <View
              style={[
                layout.h_120,
                backgrounds.bluePrimary,
                layout.justifyCenter,
                borders.bottomLeftRounded_16,
                borders.bottomRightRounded_16,
                layout.absolute,
                layout.fullWidth,
              ]}
            />
          )}
          <ActivityLoader isLoading={paymentUnitInfoQuery.isLoading} />
          {showAddUnitDialog && (
            <AddStorageAccountDialog
              onClose={() => {
                setShowAddUnitDialog(false);
                if (isUnitAdded === true) {
                  setIsUnitAdded(false);
                  queryClient.removeQueries({
                    queryKey: ['fetch_units_payment'],
                  });
                  onRefresh();
                }
              }}
              onUnitAdded={(value: boolean) => setIsUnitAdded(value)}
              onNavigation={() => onFindStorageAccountNumber()}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}

export default Payments;
