import * as React from 'react';
import { Text, TextStyle, View } from 'react-native';
import { AlertFailed, AlertSuccess } from '@/assets/svg';
import { SSButton } from '@/components/atoms';
import { useTheme } from '@/theme';
import { AccessStackScreenProps } from '@/types/navigation';

function AccessSuccessFailedAlert({
  navigation,
  route,
}: AccessStackScreenProps) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const isSuccess = route?.params?.isSuccess as boolean;

  const transparentBackgroundStyle = {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  };
  const otherTextStyles: TextStyle = {
    textAlign: 'center',
  };

  const dismissAlert = () => {
    navigation.goBack();
  };

  const renderIcon = () => (
    <View style={[gutters.marginTop_10]}>
      {isSuccess ? <AlertSuccess /> : <AlertFailed />}
    </View>
  );

  const renderTitle = () => (
    <Text
      style={[
        fonts.size_24,
        fonts.appFontBold,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      {isSuccess ? 'Gate Open' : 'Gate Not Within Range'}
    </Text>
  );

  const renderDescription = () => (
    <Text
      style={[
        fonts.size_14,
        fonts.appFontRegular,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      {isSuccess ? 'Have a great day!' : 'Please get closer to gate'}
    </Text>
  );

  const renderReturnToAccessBtn = () => (
    <View style={[layout.fullWidth]}>
      <SSButton
        title="Return to Access"
        onPress={dismissAlert}
        variant="dark"
      />
    </View>
  );

  const renderButton = () => (
    <View style={[layout.fullWidth, gutters.marginVertical_20]}>
      {renderReturnToAccessBtn()}
    </View>
  );

  const renderAlertContent = () => (
    <View
      style={[
        layout.fullWidth,
        layout.itemsCenter,
        borders.rounded_16,
        backgrounds.white,
        gutters.paddingHorizontal_20,
        gutters.paddingVertical_20,
      ]}
    >
      {renderIcon()}
      {renderTitle()}
      {renderDescription()}
      {renderButton()}
    </View>
  );

  return (
    <View
      style={[
        layout.flex_1,
        layout.justifyCenter,
        layout.itemsCenter,
        gutters.paddingHorizontal_10,
        transparentBackgroundStyle,
      ]}
    >
      {renderAlertContent()}
    </View>
  );
}

export default AccessSuccessFailedAlert;
