import React, { useState, useLayoutEffect, useEffect } from 'react';
import { ScrollView, StatusBar, Text, View } from 'react-native';
import useStorage from '@/hooks/useStorage';
import {
  HomeStackScreenProps,
  LocationUnitNotesParams,
} from '@/types/navigation';
import { useTheme } from '@/theme';
import { getNoteStorageKeyForUnitId } from '@/utils/storage';
import SSButton from '@/components/atoms/SSButton/SSButton';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import SSDropdownInput from '@/components/atoms/SSDropdownInput/SSDropdownInput';
import {
  NavigationProp,
  NavigationState,
  useNavigation,
  useIsFocused,
} from '@react-navigation/native';
import {
  HeaderBackButton,
  HeaderButtonProps,
} from '@react-navigation/elements';
import AlertMessageDialog from '@/components/molecules/AlertMessageDialog';
import SSSuccessDialog from '@/components/molecules/SSSuccessDialog';
import { useAnalytics } from '@/context/AnalyticsContext';

// Define header options outside of the component
const getHeaderOptions = (
  navigation: Omit<
    NavigationProp<ReactNavigation.RootParamList>,
    'getState'
  > & { getState(): NavigationState | undefined },
  currentNote: string,
  savedNote: string,
  setModalVisible: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  },
) => ({
  headerShown: true,
  headerLeft: (props: HeaderButtonProps) => (
    <HeaderBackButton
      {...props}
      labelVisible={false}
      onPress={() => {
        if (currentNote === savedNote) {
          navigation.goBack(); // Go back if the note is saved
        } else {
          setModalVisible(true);
        }
      }}
    />
  ),
});

function UnitNotes({ route }: HomeStackScreenProps) {
  const routeParams = route.params as LocationUnitNotesParams;
  const { unit, selectedUnit } = routeParams;
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  const storage = useStorage('global-storage');
  const { fonts, layout, gutters, borders, backgrounds } = useTheme();

  const [selectedUnitId, setSelectedUnitId] = useState(selectedUnit.unitNumber);
  const [noteStorageKey, setNoteStorageKey] = useState(
    getNoteStorageKeyForUnitId(selectedUnit.rentalId.toString()),
  );
  const [currentNote, setCurrentNote] = useState(
    storage.getString(noteStorageKey) ?? '',
  );
  const [savedNote, setSavedNote] = useState(currentNote);
  const [modalVisible, setModalVisible] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('UnitNotes', {
        unitNumber: selectedUnitId,
        facilityId: selectedUnit.facilityId?.toString(),
        tenantId: selectedUnit.accountId?.toString(),
        hasExistingNote: Boolean(savedNote),
        noteLength: currentNote?.length ?? 0,
        hasUnsavedChanges: currentNote !== savedNote,
        totalUnits: unit?.length,
        showingSuccessDialog: showSuccessDialog,
        showingUnsavedDialog: modalVisible,
      });
    }
  }, [
    isFocused,
    selectedUnitId,
    selectedUnit,
    savedNote,
    currentNote,
    unit?.length,
    showSuccessDialog,
    modalVisible,
    setCurrentScreen,
  ]);

  // Use useLayoutEffect to set navigation options
  useLayoutEffect(() => {
    navigation.setOptions(
      getHeaderOptions(navigation, currentNote, savedNote, setModalVisible),
    );
  }, [navigation, currentNote, savedNote, setModalVisible]);

  const handleSave = () => {
    storage.set(noteStorageKey, currentNote);
    setSavedNote(currentNote);
    setShowSuccessDialog(true);
  };

  const handleUnitSelected = (unitId: string) => {
    setCurrentNote(storage.getString(getNoteStorageKeyForUnitId(unitId)) ?? '');
    setSavedNote(storage.getString(getNoteStorageKeyForUnitId(unitId)) ?? '');
    setNoteStorageKey(getNoteStorageKeyForUnitId(unitId));
  };

  const handleCloseSuccessDialog = () => {
    setShowSuccessDialog(false);
    setTimeout(() => {
      navigation.goBack();
    }, 300);
  };

  const handleAlertOnSave = () => {
    handleSave();
    setModalVisible(false);
  };

  const handleAlertOnLeave = () => {
    setModalVisible(false);
    setTimeout(() => {
      navigation.goBack();
    }, 300);
  };

  const handleUnitchanged = (item: { label: string; value: string }) => {
    handleUnitSelected(item.value);
    setSelectedUnitId(item.label);
  };

  const handleNoteChanged = (value: string) => {
    setCurrentNote(value);
  };

  return (
    <ScrollView contentContainerStyle={[layout.flex_1]}>
      <View
        style={[
          { height: 225 },
          backgrounds.bluePrimary,
          borders.bottomLeftRounded_12,
          borders.bottomRightRounded_12,
        ]}
      />
      <View
        style={[
          { marginTop: -200 },
          layout.flex_1,
          layout.fullWidth,
          // layout.absolute,
          gutters.paddingHorizontal_16,
          layout.fullHeight,
        ]}
      >
        <ScrollView contentContainerStyle={[layout.flexGrow_1]}>
          <View
            style={[layout.fullHeight, gutters.gap_16, gutters.marginBottom_10]}
          >
            <Text style={[fonts.appFontBold, fonts.white, fonts.size_32]}>
              Add Unit Notes
            </Text>
            <Text style={[fonts.appFontRegular, fonts.white, fonts.size_14]}>
              You can use the space below to add any important notes about your
              unit or keep track of your storage inventory.
            </Text>
            <View
              style={[
                layout.col,
                gutters.gap_16,
                gutters.paddingTop_24,
                gutters.paddingBottom_24,
                gutters.paddingLeft_16,
                gutters.paddingRight_16,
                backgrounds.white,
                borders.rounded_16,
              ]}
            >
              <SSDropdownInput
                label="Unit No."
                value={selectedUnitId}
                isError={false}
                data={unit.map(obj => ({
                  label: `${obj.unitNumber}`,
                  value: `${obj.rentalId}`,
                }))}
                onSelect={handleUnitchanged}
              />

              <SSTextInput
                label="Write note here..."
                value={currentNote}
                multiline
                numberOfLines={10}
                onChangeText={handleNoteChanged}
              />

              <SSButton
                onPress={handleSave}
                title="Save note"
                accessibilityLabel="Save notes"
                variant={currentNote === savedNote ? 'light' : 'dark'}
                disabled={currentNote === savedNote}
              />
            </View>
          </View>
        </ScrollView>
        {showSuccessDialog && (
          <SSSuccessDialog
            onClose={handleCloseSuccessDialog}
            buttonTitle="ok"
            title="Notes Updated"
            description="Your new note has been saved successfully."
          />
        )}
        {modalVisible && (
          <AlertMessageDialog
            onSaveNote={handleAlertOnSave}
            onLeave={handleAlertOnLeave}
          />
        )}
      </View>
    </ScrollView>
  );
}

export default UnitNotes;
