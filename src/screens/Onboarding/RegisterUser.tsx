import React, { useState, useEffect } from 'react';
import {
  Text,
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  BackHandler,
  Modal,
  ActivityIndicator,
  StatusBar,
} from 'react-native';

import { useTheme } from '@/theme';
import { RegisterStepScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import Stepper from '@/components/molecules/Brand/Stepper';
import { RegisterCompleteSVG } from '@/assets/svg';
import { useFocusEffect } from '@react-navigation/native';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { RegisterAccountRequest } from '@/types/schemas/request';
import { registerAccount } from '@/services/api';
import { useMutation } from '@tanstack/react-query';
import SSErrorView from '@/components/atoms/SSErrorView';
import { formatPhoneNumber, digitsOnly } from '@/utils/commonFunctions';
import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

function RegisterUser({ navigation }: RegisterStepScreenProps) {
  const [fNameValue, setFirstName] = useState('');
  const [lNameValue, setLastName] = useState('');
  const [storageNumber, setStorageNumber] = useState('');
  const [emailValue, setEmail] = useState('');
  const [phoneNumberValue, setPhoneNumber] = useState('');
  const [passwordValue, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [alertView, setAlertView] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [currentStep, setCurrentStep] = useState(1);
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  const { layout, gutters, fonts, colors, backgrounds, borders } = useTheme();

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('Register', {
        currentStep,
        hasPersonalInfo: Boolean(fNameValue && lNameValue && storageNumber),
        hasContactInfo: Boolean(emailValue && phoneNumberValue),
        hasValidEmail: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue),
        hasValidPhone: /^\d{10}$/.test(phoneNumberValue.replaceAll('-', '')),
        hasValidPasswords: Boolean(
          passwordValue &&
          confirmPassword &&
          passwordValue === confirmPassword &&
          passwordValue.length >= 8
        ),
        hasError: alertView,
        errorMessage: errorMessage || undefined,
        isLoading,
      });
    }
  }, [
    isFocused,
    currentStep,
    fNameValue,
    lNameValue,
    storageNumber,
    emailValue,
    phoneNumberValue,
    passwordValue,
    confirmPassword,
    alertView,
    errorMessage,
    isLoading,
    setCurrentScreen,
  ]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        switch (currentStep) {
          case 1:
            return false;
          case 2:
            setCurrentStep(1);
            return true;
          case 3:
            return true;
          default:
            return false;
        }
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, [currentStep]),
  );

  const handleOnNextPressed = () => {
    setCurrentStep(prev => (prev < 3 ? prev + 1 : prev));
  };

  const isValidSecondStep = (
    _emailValue: string,
    _phoneNumberValue: string,
    _passwordValue: string,
    _confirmPasswordValue: string,
  ): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\d{10}$/;
    const passwordRegex = /^.{8,}$/;

    // Validate email
    if (!emailRegex.test(_emailValue)) {
      return false;
    }
    // Validate phone number
    if (!phoneRegex.test(_phoneNumberValue.replaceAll('-', ''))) {
      return false;
    }

    // Validate password
    if (!passwordRegex.test(_passwordValue)) {
      return false;
    }

    // Validate confirm password
    if (_passwordValue !== _confirmPasswordValue) {
      return false;
    }
    // All validations passed
    return true;
  };

  const callRegisterAccountApi = useMutation({
    mutationFn: (data: RegisterAccountRequest) => {
      return registerAccount(data);
    },
    onSuccess: () => {
      handleOnNextPressed();
    },
    onError: async error => {
      await logEventWithStatus(Events.register, EventStatus.failed, {
        error: JSON.stringify(error),
      });
      setAlertView(true);
      setErrorMessage(error.message);
    },
    onSettled: async () => {
      await logEventWithStatus(Events.register, EventStatus.completed);
      setIsLoading(false);
    },
  });

  const callRegisterApi = async () => {
    if (
      !isValidSecondStep(
        emailValue,
        phoneNumberValue,
        passwordValue,
        confirmPassword,
      )
    ) {
      return;
    }

    const registerAccountData: RegisterAccountRequest = {
      firstName: fNameValue,
      lastName: lNameValue,
      storageAccountNumber: storageNumber,
      email: emailValue,
      phoneNumber: phoneNumberValue,
      password: passwordValue,
    };

    setIsLoading(true);
    callRegisterAccountApi.reset();
    await callRegisterAccountApi.mutateAsync(registerAccountData);
  };

  const handleStorageAccountNumberClick = () => {
    navigation.navigate('FindStorageNumber', {
      isFromHome: false,
      isFromPayment: false,
    })
  };

  const gotoLogin = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Onboarding' }, { name: 'Login' }],
    });
  };

  const handleOnCancel = () => {
    if (currentStep === 1) {
      navigation.goBack();
    } else {
      setCurrentStep(prev => (prev > 1 ? prev - 1 : prev));
    }
  };

  const renderFirstStep = () => (
    <>
      <View style={[layout.flex_1]}>
        <Text
          style={[
            fonts.appFontBold,
            fonts.charcoal,
            fonts.size_20,
            gutters.marginTop_16,
          ]}
        >
          Personal Information
        </Text>
        <View
          style={[
            layout.fullWidth,
            backgrounds.gray100,
            layout.h_1,
            gutters.marginBottom_16,
            gutters.marginTop_16,
          ]}
        />
        <View style={[layout.fullWidth, gutters.marginTop_16, gutters.gap_12]}>
          <SSTextInput
            label="First Name*"
            isError={false}
            value={fNameValue}
            autoCapitalize="words"
            onChangeText={(value: string) => {
              setFirstName(value);
            }}
          />
          <SSTextInput
            label="Last Name*"
            isError={false}
            value={lNameValue}
            autoCapitalize="words"
            onChangeText={(value: string) => {
              setLastName(value);
            }}
          />
        </View>
        <Text
          style={[
            fonts.appFontBold,
            fonts.charcoal,
            fonts.size_20,
            gutters.marginTop_16,
          ]}
        >
          Storage Account Number
        </Text>
        <View
          style={[
            layout.fullWidth,
            backgrounds.gray100,
            layout.h_1,
            gutters.marginBottom_16,
            gutters.marginTop_10,
          ]}
        />
        <Text
          style={[
            fonts.appFontRegular,
            fonts.charcoal,
            fonts.size_14,
            gutters.marginBottom_10,
          ]}
        >
          Please enter the storage account number you received when you rented
          your storage unit.
        </Text>
        <View style={[gutters.marginTop_12, gutters.marginBottom_12]}>
          <SSTextInput
            label="Storage Account Number*"
            value={storageNumber}
            keyboardType="numeric"
            isError={false}
            onChangeText={(value: string) => {
              setStorageNumber(digitsOnly(value));
            }}
          />
        </View>
        <Text style={[fonts.size_14, fonts.charcoal, fonts.appFontRegular]}>
          Can&apos;t find{' '}
          <Text
            onPress={handleStorageAccountNumberClick}
            style={[
              fonts.size_14,
              fonts.primary,
              fonts.appFontBold,
              fonts.underline,
            ]}
          >
            Storage Account Number
          </Text>
          ?
        </Text>
      </View>
      <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
        <View style={[gutters.gap_8]}>
          <SSButton
            title="Next"
            onPress={handleOnNextPressed}
            variant={
              fNameValue && lNameValue && storageNumber ? 'dark' : 'light'
            }
          />
        </View>
        <View style={[gutters.marginTop_10]}>
          <SSButton
            title="Cancel"
            onPress={handleOnCancel}
            variant="transparent"
          />
        </View>
      </View>
    </>
  );

  const renderSecondStep = () => (
    <>
      <View style={[layout.flex_1]}>
        <Text
          style={[
            fonts.appFontBold,
            fonts.charcoal,
            fonts.size_20,
            gutters.marginTop_16,
            gutters.marginBottom_10,
          ]}
        >
          Contact & Security
        </Text>
        <Text
          accessibilityLabel="Almost done! Complete the information below to finish setting up your account."
          style={[
            fonts.appFontRegular,
            fonts.charcoal,
            fonts.size_14,
            gutters.marginBottom_10,
          ]}
        >
          Almost done! Complete the information below to finish setting up your
          account. Please note that your password must contain at least 8
          characters
        </Text>
        <View
          style={[
            layout.fullWidth,
            backgrounds.gray100,
            layout.h_1,
            gutters.marginBottom_16,
            gutters.marginTop_16,
          ]}
        />
        <View style={[layout.fullWidth, gutters.gap_16]}>
          <SSTextInput
            label="Email Address*"
            isError={false}
            value={emailValue}
            onChangeText={(value: string) => {
              setEmail(value);
            }}
          />
          <SSTextInput
            label="Phone Number*"
            isError={false}
            maxLength={20}
            keyboardType="numeric"
            value={phoneNumberValue}
            onChangeText={(value: string) => {
              setPhoneNumber(formatPhoneNumber(value));
            }}
          />
          <SSTextInput
            label="Password*"
            isError={false}
            isPassword
            textContentType="none"
            value={passwordValue}
            onChangeText={(value: string) => {
              setPassword(value);
            }}
          />
          <SSTextInput
            label="Re-enter Password*"
            isError={false}
            isPassword
            textContentType="none"
            value={confirmPassword}
            onChangeText={(value: string) => {
              setConfirmPassword(value);
            }}
          />
        </View>
      </View>
      <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
        <View style={[gutters.gap_8]}>
          <SSButton
            title="Next"
            onPress={() => {
              void callRegisterApi();
            }}
            isProgress={false}
            isSuccess={false}
            variant={
              isValidSecondStep(
                emailValue,
                phoneNumberValue,
                passwordValue,
                confirmPassword,
              )
                ? 'dark'
                : 'light'
            }
          />
        </View>
        <View style={[gutters.marginTop_10]}>
          <SSButton
            title="Back"
            onPress={handleOnCancel}
            variant="transparent"
          />
        </View>
      </View>
    </>
  );

  const renderRegisterComplete = () => (
    <>
      <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
        <RegisterCompleteSVG />
        <Text
          style={[
            fonts.size_32,
            fonts.appFontBold,
            fonts.alignCenter,
            fonts.primary,
            layout.justifyCenter,
            layout.itemsCenter,
            gutters.marginTop_16,
          ]}
        >
          Registration Complete!
        </Text>
        <Text
          style={[
            fonts.appFontRegular,
            fonts.charcoal,
            fonts.size_14,
            gutters.marginBottom_10,
            gutters.marginTop_16,
            fonts.alignCenter,
          ]}
        >
          Success! Your account is set up. Login to get started!
        </Text>
      </View>
      <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
        <SSButton title="Login" onPress={gotoLogin} variant="dark" />
      </View>
    </>
  );

  const steps = [
    { stepNum: 1, label: 'STEP 1' },
    { stepNum: 2, label: 'STEP 2' },
    { stepNum: 3, label: 'STEP 3' },
  ];

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[layout.flex_1]}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={[layout.flex_1, { backgroundColor: colors.bluePrimary }]}>
          <View
            style={[
              layout.flex_1,
              gutters.padding_24,
              // eslint-disable-next-line react-native/no-inline-styles
              { borderTopLeftRadius: 20, borderTopRightRadius: 20 },
              backgrounds.white,
            ]}
          >
            <View
              style={[
                layout.relative,
                layout.justifySpaceEvenly,
                layout.row,
                gutters.marginBottom_16,
                layout.selfCenter,
              ]}
            >
              {steps.map((step, index) => (
                <Stepper
                  key={index}
                  label={step.label}
                  isFirstStep={index === 0}
                  isLastStep={index === steps.length - 1}
                  isActiveStep={currentStep === step.stepNum}
                  isCompletedStep={currentStep > step.stepNum}
                />
              ))}
            </View>
            <ScrollView contentContainerStyle={layout.flexGrow_1}>
              <View style={[layout.flex_1]}>
                {alertView && (
                  <SSErrorView
                    title="Unable to Create account"
                    description={
                      errorMessage ??
                      'Some thing want wrong, Please try again later.'
                    }
                  />
                )}
                {currentStep === 1 && renderFirstStep()}
                {currentStep === 2 && renderSecondStep()}
                {currentStep === 3 && renderRegisterComplete()}
              </View>
            </ScrollView>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {isLoading && (
        <Modal
          transparent
          animationType="none"
          visible={isLoading}
          onRequestClose={() => null}
        >
          <View
            style={[
              layout.flex_1,
              layout.itemsCenter,
              layout.justifyCenter,
              { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
            ]}
          >
            <View
              style={[
                backgrounds.white,
                borders.rounded_12,
                { height: 100, width: 100 },
                layout.itemsCenter,
                layout.justifyCenter,
              ]}
            >
              <ActivityIndicator size="large" color="#0000ff" />
            </View>
          </View>
        </Modal>
      )}
    </KeyboardAvoidingView>
  );
}

export default RegisterUser;
