import React, { useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  Animated,
  ImageSourcePropType,
  Linking,
  Easing,
  Dimensions,
} from 'react-native';
import { ApplicationScreenProps } from '@/types/navigation';
import { useTheme } from '@/theme';
import { SafeScreen } from '@/components/template';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import {
  LogoPNG,
  SplashOption1PNG,
  SplashOption3PNG,
  SplashOption4PNG,
  CombinedImagePNG,
  SplashOption5PNG,
} from '../../theme/assets/images/index';
import { SS_LINKS } from '@/utils/linking';
import { HomeLogoSVG } from '@/assets/svg';

const splashImages: ImageSourcePropType[] = [
  SplashOption1PNG as ImageSourcePropType,
  SplashOption3PNG as ImageSourcePropType,
  SplashOption4PNG as ImageSourcePropType,
  SplashOption5PNG as ImageSourcePropType,
];

const styles = StyleSheet.create({
  boldText: {
    fontFamily: 'FilsonPro-Bold',
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 16.8,
    textAlign: 'center',
    color: '#004977',
    textDecorationLine: 'underline',
  },
  bodyText: {
    fontFamily: 'Filson Pro',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 18,
    textAlign: 'center',
    color: '#161718',
    padding: 10,
  },
});

function Onboarding({ navigation }: ApplicationScreenProps) {
  const { layout, borders, gutters, backgrounds } = useTheme();

  const slideAnim = useRef(new Animated.Value(0)).current; // Initial value for slide-up animation
  const fadeAnim = useRef(new Animated.Value(300)).current; // Initial value for fade-in animation
  const [imageVisible, setImageVisible] = useState(true); // State to control image visibility
  const [selectedImage, setSelectedImage] =
    useState<ImageSourcePropType | null>(null); // State to store the selected image
  const insets = useSafeAreaInsets();

  const handleOnLoginPressed = () => {
    navigation.navigate('Login');
  };

  const handleOnRegisterPressed = async () => {
    await logEventWithStatus(Events.register, EventStatus.initiated);
    navigation.navigate('Register');
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * splashImages.length);
      setSelectedImage(splashImages[randomIndex]);
      Animated.timing(slideAnim, {
        toValue: -Dimensions.get('screen').height, // Move the image out of the screen to the top
        duration: 800,
        useNativeDriver: true,
        easing: Easing.ease,
      }).start(() => {
        setImageVisible(false); // Hide the image after the animation completes
        Animated.timing(fadeAnim, {
          toValue: 0, // Fade in
          duration: 500,
          useNativeDriver: true,
          easing: Easing.elastic(1),
        }).start();
      });
    }, 1000); // 1 second delay

    return () => clearTimeout(timeoutId); // Clear timeout if the component unmounts
  }, [slideAnim, fadeAnim]);

  const renderOnboardingScreen = () => (
    <View
      style={[
        layout.justifyBetween,
        layout.itemsCenter,
        layout.fullHeight,
      ]}
    >
      <View style={[layout.justifyEnd, { height: insets.top + 50 },]}>
        <View style={[layout.fullHeight, layout.justifyEnd]}>
          <View style={[backgrounds.blueLogo, layout.flex_1]} />
          <HomeLogoSVG />
        </View>
      </View>
      <View
        style={[
          layout.flex_1,
          layout.relative,
          layout.itemsCenter,
          layout.fullWidth,
        ]}
      >
        <View style={[layout.fullHeight, layout.fullWidth, layout.absolute]}>
          {selectedImage && (
            <Image
              source={selectedImage}
              style={[layout.fullWidth, layout.fullHeight]}
            />
          )}
        </View>
        {!imageVisible && (
          <Animated.View
            style={[
              layout.flex_1,
              layout.itemsCenter,
              layout.fullWidth,
              {
                transform: [{ translateY: fadeAnim }],
                justifyContent: 'flex-end',
              },
            ]}
          >
            <View
              style={[
                layout.fullWidth,
                borders.topRightRounded_25,
                borders.topLeftRounded_25,
                gutters.padding_32,
                gutters.paddingBottom_50,
                gutters.gap_16,
                backgrounds.white,
                {
                  marginBottom: -20, // some extra padding for the enter animation,
                },
              ]}
            >
              <View>
                <SSButton
                  title="Login"
                  accessibilityLabel="Login"
                  onPress={handleOnLoginPressed}
                  variant="dark"
                />
              </View>
              <View>
                <SSButton
                  title="Create Account"
                  accessibilityLabel="Register account with SmartStop"
                  onPress={() => void handleOnRegisterPressed()}
                  variant="transparent"
                />
              </View>
              <View>
                <Text
                  style={[
                    styles.bodyText,
                    layout.justifyCenter,
                    layout.itemsCenter,
                    layout.fullWidth,
                  ]}
                >
                  Need Storage?{' '}
                  <Text
                    style={[styles.boldText]}
                    onPress={() => {
                      void Linking.openURL(SS_LINKS.HOME);
                    }}
                  >
                    Let Us Help
                  </Text>
                </Text>
                <Text
                  style={[styles.boldText]}
                  onPress={() => {
                    void Linking.openURL(SS_LINKS.PRIVACY_POLICY);
                  }}
                >
                  Privacy Policy
                </Text>
              </View>
            </View>
          </Animated.View>
        )}
      </View>
    </View>
  );

  return (
    <SafeScreen isFullScreen statusBarStyle="dark-content">
      <View
        style={[
          layout.flex_1,
          layout.fullHeight,
          layout.fullWidth,
          layout.relative,
          backgrounds.springGrass20,
        ]}
      >
        {renderOnboardingScreen()}
        {imageVisible && (
          <Animated.View
            style={[
              layout.fullHeight,
              layout.fullWidth,
              layout.absolute,
              { transform: [{ translateY: slideAnim }] },
            ]}
          >
            <Image
              source={CombinedImagePNG as ImageSourcePropType}
              style={[
                layout.fullHeight,
                layout.fullWidth,
                { resizeMode: 'cover' },
              ]}
            />
          </Animated.View>
        )}
      </View>
    </SafeScreen>
  );
}

export default Onboarding;
