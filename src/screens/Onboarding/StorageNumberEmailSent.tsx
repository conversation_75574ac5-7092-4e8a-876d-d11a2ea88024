import { BackHandler, StatusBar, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import { RegisterStepScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { EmailSentSvg } from '@/assets/svg';
import React, { useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';

type Params = {
  isFromResetPassword: boolean;
  description: string;
  isFromHome?: boolean;
  isFromPayment?: boolean;
};

function StorageNumberEmailSent({
  navigation,
  route,
}: RegisterStepScreenProps) {
  const { description, isFromResetPassword, isFromHome, isFromPayment } =
    route.params as Params;
  const { layout, gutters, fonts, colors, backgrounds } = useTheme();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        return true;
      };
      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );

  const handleOnCancel = () => {
    let whereToGo = isFromResetPassword ? 'LoginScreen' : 'RegisterUser';
    if (isFromHome) {
      whereToGo = 'MainHome';
    } else if (isFromPayment) {
      whereToGo = 'PaymentsHome';
    }

    navigation.reset({
      index: 0,
      routes: [{ name: whereToGo }],
    });
  };

  const buttonTitle = () => {
    let title = isFromResetPassword
      ? 'Back to login'
      : 'BACK TO CREATE AN ACCOUNT';
    if (isFromHome || isFromPayment) {
      title = 'Back to Home';
    }
    return title;
  };

  return (
    <View style={[layout.flex_1, { backgroundColor: colors.bluePrimary }]}>
      <View
        style={[
          layout.flex_1,
          gutters.padding_24,
          { borderTopLeftRadius: 20, borderTopRightRadius: 20 },
          backgrounds.white,
        ]}
      >
        <View style={[layout.flex_1, layout.itemsCenter, layout.justifyCenter]}>
          <EmailSentSvg />
          <Text
            style={[
              fonts.size_32,
              fonts.alignCenter,
              fonts.primary,
              layout.justifyCenter,
              layout.itemsCenter,
              gutters.marginTop_16,
              fonts.appFontBold,
            ]}
          >
            Email Sent
          </Text>
          <Text
            style={[
              fonts.appFontRegular,
              fonts.charcoal,
              fonts.size_14,
              gutters.marginTop_16,
              fonts.alignCenter,
            ]}
          >
            {description}
          </Text>
        </View>
        <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
          <SSButton
            title={buttonTitle()}
            onPress={handleOnCancel}
            accessibilityLabel="Log in to SmartStop"
            variant="transparent"
          />
        </View>
      </View>
    </View>
  );
}

export default StorageNumberEmailSent;
