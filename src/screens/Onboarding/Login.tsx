import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  StyleSheet,
  Alert,
} from 'react-native';
import { useMutation } from '@tanstack/react-query';
import { RegisterStepScreenProps } from '@/types/navigation';
import { SafeScreen } from '@/components/template';
import { useTheme } from '@/theme';
import { passwordGrantRequest } from '@/services/oauth';
import Adapter from '@/adapters/Adapter';
import { TokensAdapter } from '@/adapters/Tokens/TokensAdapter';
import { ApiTokenResponse, TokenData } from '@/types/schemas/tokenData';
import { setTokenData } from '@/utils/auth';
import { EventStatus, Events, logEventWithStatus } from '@/utils/analytics';
import SSButton from '@/components/atoms/SSButton/SSButton';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { isValidEmail } from '@/utils/validate';
import { ClickbaleLinkButton } from '@/components/atoms';
import SSErrorView from '@/components/atoms/SSErrorView';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

const styles = StyleSheet.create({
  keyboardViewStyle: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
});

function Login({ navigation }: RegisterStepScreenProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { layout, gutters, backgrounds, fonts } = useTheme();
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  const addDelayToLoginUser = (tokenData: TokenData) => {
    setTimeout(() => {
      setTokenData(tokenData);
    }, 30);
  };

  const loginMutation = useMutation({
    mutationFn: (data: { username: string; password: string }) => {
      return passwordGrantRequest(data.username, data.password);
    },
    onSuccess: data => {
      const tokenData: TokenData = Adapter.from(data).to(
        (item: ApiTokenResponse) => new TokensAdapter(item).adapt(),
      );
      addDelayToLoginUser(tokenData);
    },
    onError: async error => {
      await logEventWithStatus(Events.login, EventStatus.failed, {
        error: JSON.stringify(error),
      });
    },
    onSettled: async data => {
      if (data) {
        await logEventWithStatus(Events.login, EventStatus.completed);
      }
    },
  });

  useEffect(() => {
    if (loginMutation.isError) {
      loginMutation.reset();
    }
  }, [username, password]);

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('Login', {
        hasValidEmail: isValidEmail(username),
        hasPassword: password?.length > 0,
        hasError: loginMutation.isError,
        isAttemptingLogin: loginMutation.isPending,
      });
    }
  }, [isFocused, username, password, loginMutation.isError, loginMutation.isPending, setCurrentScreen]);

  const handleOnLoginSubmit = async () => {
    loginMutation.reset();
    await logEventWithStatus(Events.login, EventStatus.initiated);
    await loginMutation.mutateAsync({ username, password });
  };

  /**
   * A method meant to be directly used in an "onPress" property. These
   * properties expect a void-returning function so the linter freaks out if
   * you give it a function that is marked as "async" and returns a Promise.
   * The solution here is to wrap our Promise-returning function in a
   * void-returning function and mark the Promise-returning function with "void".
   * This ensures the function will still be invoked while satisfying the linter.
   */
  const onLoginSubmit = () => {
    const isValid = isValidEmail(username);
    if (!isValid) {
      Alert.alert('SmartStop', 'Please enter a valid email address.');
      return null;
    }
    return void handleOnLoginSubmit();
  };

  const onCancel = () => {
    navigation.goBack();
  };

  const onCreateAccountAction = () => {
    navigation.navigate('Register');
  };

  const onForgotPasswordAction = () => {
    navigation.navigate('ResetPassword');
  };

  return (
    <SafeScreen
      statusBarStyle="light-content"
      style={[
        backgrounds.bluePrimary,
        gutters.paddingTop_0,
        gutters.paddingBottom_0,
      ]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[
          layout.flex_1,
          layout.itemsCenter,
          gutters.paddingHorizontal_16,
          backgrounds.white,
          gutters.paddingTop_40,
          styles.keyboardViewStyle,
        ]}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={[
              layout.col,
              layout.flex_1,
              layout.fullWidth,
              layout.itemsCenter,
            ]}
          >
            <View style={[layout.fullWidth, gutters.paddingBottom_20]}>
              <Text style={[fonts.size_40, fonts.primary, fonts.appFontBold]}>
                Login
              </Text>
              <Text
                style={[
                  fonts.size_14,
                  fonts.charcoal,
                  gutters.paddingTop_10,
                  fonts.appFontRegular,
                ]}
              >
                Securely access your storage account for unit access, to make a
                payment, view your account info and more!
              </Text>
            </View>
            {loginMutation.isError && (
              <SSErrorView title="Incorrect email or password" />
            )}
            <View
              style={[layout.fullWidth, gutters.marginTop_16, gutters.gap_12]}
            >
              <View style={[layout.row, layout.h_60]}>
                <SSTextInput
                  label="Email Address*"
                  value={username}
                  isError={loginMutation.isError}
                  onChangeText={(value: string) => setUsername(value?.trim() ?? '')}
                  keyboardType="email-address"
                  autoComplete="username"
                  textContentType="username"
                  importantForAutofill="yes"
                />
              </View>
              <View style={[layout.row, layout.h_60]}>
                <SSTextInput
                  label="Password*"
                  isPassword
                  togglePassword
                  value={password}
                  isError={loginMutation.isError}
                  onChangeText={(value: string) => setPassword(value?.trim())}
                  autoComplete="password"
                  textContentType="password"
                  importantForAutofill="yes"
                />
              </View>
            </View>
            <View
              style={[
                gutters.marginTop_16,
                layout.row,
                layout.fullWidth,
                layout.justifyBetween,
              ]}
            >
              <ClickbaleLinkButton
                onPress={() => onForgotPasswordAction()}
                title="Forgot password?"
              />
              <ClickbaleLinkButton
                onPress={() => onCreateAccountAction()}
                title="Create Account"
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
      <View
        style={[
          layout.fullWidth,
          gutters.gap_12,
          gutters.paddingHorizontal_16,
          backgrounds.white,
          gutters.paddingBottom_60,
        ]}
      >
        <SSButton
          title="LOGIN"
          isProgress={loginMutation.isPending}
          isSuccess={loginMutation.isSuccess}
          variant={
            username && password && isValidEmail(username) ? 'dark' : 'light'
          }
          onPress={onLoginSubmit}
        />
        <SSButton title="CANCEL" variant="transparent" onPress={onCancel} />
      </View>
    </SafeScreen>
  );
}

export default Login;
