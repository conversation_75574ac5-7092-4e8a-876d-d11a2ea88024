import React, { useState, useEffect } from 'react';
import {
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useTheme } from '@/theme';
import { RegisterStepScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { useMutation } from '@tanstack/react-query';
import { requestPasswordReset } from '@/services/api';
import SSErrorView from '@/components/atoms/SSErrorView';
import { PasswordResetRequest } from '@/types/schemas/request';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { isValidEmail } from '@/utils/validate';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

function ResetPassword({ navigation }: RegisterStepScreenProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [alertView, setAlertView] = useState<boolean>(false);
  const [emailValue, setEmail] = useState('');
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  const { layout, gutters, fonts, colors, backgrounds, borders } = useTheme();

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('ResetPassword', {
        hasValidEmail: isValidEmail(emailValue),
        hasError: alertView,
        isLoading,
        emailEntered: Boolean(emailValue),
      });
    }
  }, [
    isFocused,
    emailValue,
    alertView,
    isLoading,
    setCurrentScreen,
  ]);

  const callRequestPasswordApi = useMutation({
    mutationFn: (data: PasswordResetRequest) => {
      return requestPasswordReset(data);
    },
    onSuccess: () => {
      setIsLoading(false);
      navigation.navigate('StorageNumberEmailSent', {
        isFromResetPassword: true,
        description:
          'A password reset link has been sent to your email. Please check your inbox for next steps!',
      });
    },
    onError: () => {
      setAlertView(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const handleOnCancel = () => {
    navigation.goBack();
  };

  const SentEmailClick = async () => {
    const passwordResetRequest: PasswordResetRequest = {
      email: emailValue,
    };
    setIsLoading(true);
    callRequestPasswordApi.reset();
    await callRequestPasswordApi.mutateAsync(passwordResetRequest);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[layout.flex_1]}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={[layout.flex_1, { backgroundColor: colors.bluePrimary }]}>
          <View
            style={[
              layout.flex_1,
              gutters.padding_24,
              { borderTopLeftRadius: 20, borderTopRightRadius: 20 },
              backgrounds.white,
            ]}
          >
            <ScrollView contentContainerStyle={layout.flexGrow_1}>
              <View style={[layout.flex_1, gutters.gap_32]}>
                <Text style={[fonts.appFontBold, fonts.primary, fonts.size_40]}>
                  Reset Password
                </Text>
                <Text
                  style={[fonts.appFontRegular, fonts.charcoal, fonts.size_14]}
                >
                  Please enter your email address to receive a password reset
                  link
                </Text>
                {alertView && (
                  <SSErrorView
                    title="Unable to find account"
                    description="Sorry, we couldn't find an account with that email address . Please double check your information and try again or create an account. "
                  />
                )}
                <View style={[layout.fullWidth, gutters.gap_16]}>
                  <SSTextInput
                    label="Email Address*"
                    isError={alertView}
                    value={emailValue}
                    onChangeText={(value: string) => {
                      setEmail(value);
                    }}
                  />
                </View>
              </View>
              <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
                <View style={[gutters.gap_8]}>
                  <SSButton
                    title="SEND EMAIL"
                    onPress={() => void SentEmailClick()}
                    disabled={!isValidEmail(emailValue)}
                    variant={isValidEmail(emailValue) ? 'dark' : 'light'}
                  />
                </View>
                <View style={[gutters.marginTop_10]}>
                  <SSButton
                    title="CANCEL"
                    onPress={handleOnCancel}
                    variant="transparent"
                  />
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {isLoading && (
        <Modal
          transparent
          animationType="none"
          visible={isLoading}
          onRequestClose={() => null}
        >
          <View
            style={[
              layout.flex_1,
              layout.itemsCenter,
              layout.justifyCenter,
              { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
            ]}
          >
            <View
              style={[
                backgrounds.white,
                borders.rounded_12,
                { height: 100, width: 100 },
                layout.itemsCenter,
                layout.justifyCenter,
              ]}
            >
              <ActivityIndicator size="large" color="#0000ff" />
            </View>
          </View>
        </Modal>
      )}
    </KeyboardAvoidingView>
  );
}

export default ResetPassword;
