/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useTheme } from '@/theme';
import { RegisterStepScreenProps } from '@/types/navigation';
import SSButton from '@/components/atoms/SSButton/SSButton';
import { useMutation, useQuery } from '@tanstack/react-query';
import { findAccount, getLocationsInState, getStates } from '@/services/api';
import Adapter from '@/adapters/Adapter';
import { ApiStateResponse, State } from '@/types/schemas/state';
import { StateAdapter } from '@/adapters/State/StateAdapter';
import {
  ApiLocationsInStateResponse,
  FacilitiesInState,
} from '@/types/schemas/location';
import { LocationInStateAdapter } from '@/adapters/Locations/LocationInStateAdapter';
import SSErrorView from '@/components/atoms/SSErrorView';
import { FindAccountRequest } from '@/types/schemas/request';
import SSTextInput from '@/components/atoms/SSTextInput/SSTextInput';
import { InfoSvg } from '@/assets/svg';
import SSDropdownInput from '@/components/atoms/SSDropdownInput/SSDropdownInput';
import Tooltip from 'react-native-walkthrough-tooltip';
import SSToolTip from '@/components/atoms/SSToolTip';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { formatPhoneNumber } from '@/utils/commonFunctions';
import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';

type Params = {
  isFromHome?: boolean;
  isFromPayment?: boolean;
};

function FindStorageNumber({ navigation, route }: RegisterStepScreenProps) {
  const { isFromHome, isFromPayment } = route.params as Params;
  const [stateArray, setStateArray] = useState<State[]>([]);
  const [locationInStateArray, setLocationInStateArray] = useState<
    FacilitiesInState[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [alertView, setAlertView] = useState<boolean>(false);
  const [selectedStateLable, setSelectedStateLable] = useState<string>('');
  const [selectedLocationCode, setSelectedLocationCode] = useState<number>(0);
  const [selectedLocationLable, setSelectedLocationLable] =
    useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [toolTipVisible, setToolTipVisible] = useState<boolean>(false);
  const { setCurrentScreen } = useAnalytics();
  const isFocused = useIsFocused();

  const { layout, gutters, fonts, colors, backgrounds, borders } = useTheme();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (isFocused) {
      setCurrentScreen('FindStorageNumber', {
        isFromHome,
        isFromPayment,
        hasSelectedState: Boolean(selectedStateLable),
        selectedState: selectedStateLable || undefined,
        hasSelectedLocation: Boolean(selectedLocationLable),
        selectedLocationId: selectedLocationCode || undefined,
        hasPhoneNumber: Boolean(phoneNumber),
        hasLastName: Boolean(lastName),
        isValidForm: validForm(phoneNumber, lastName, selectedLocationCode),
        hasError: alertView,
        isLoading,
        showingTooltip: toolTipVisible,
        availableLocations: locationInStateArray?.length ?? 0,
        availableStates: stateArray?.length ?? 0,
      });
    }
  }, [
    isFocused,
    isFromHome,
    isFromPayment,
    selectedStateLable,
    selectedLocationLable,
    selectedLocationCode,
    phoneNumber,
    lastName,
    alertView,
    isLoading,
    toolTipVisible,
    locationInStateArray?.length ?? 0,
    stateArray?.length ?? 0,
    setCurrentScreen,
  ]);

  // =============================================================================
  // API REQUESTS
  // =============================================================================

  const fetchState = async () => {
    setIsLoading(true);
    const response = await getStates();
    if (response.success) {
      const stateData: State[] = Adapter.from(response).to(
        (item: ApiStateResponse) => new StateAdapter(item).adapt(),
      );
      setStateArray(stateData);
    } else {
      setAlertView(true);
    }
    setIsLoading(false);
    return response;
  };

  useQuery({
    queryKey: ['getStateData'],
    queryFn: fetchState,
  });

  const FetchLocationInState = useMutation({
    mutationFn: (stateCode: string) => {
      setIsLoading(true);
      return getLocationsInState(stateCode);
    },
    onSuccess: data => {
      const locationData: FacilitiesInState[] = Adapter.from(data).to(
        (item: ApiLocationsInStateResponse) =>
          new LocationInStateAdapter(item).adapt(),
      );
      setLocationInStateArray(locationData);
    },
    onError: error => {
      setAlertView(true);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const callFindStorageAccountApi = useMutation({
    mutationFn: (data: FindAccountRequest) => {
      return findAccount(data);
    },
    onSuccess: async data => {
      setIsLoading(false);
      await logEventWithStatus(Events.findStorageUnit, EventStatus.completed);
      navigation.navigate('StorageNumberEmailSent', {
        isFromResetPassword: false,
        isFromHome,
        isFromPayment,
        description:
          'Your account ID is on its way! Please check your email inbox for next steps.',
      });
    },
    onError: async error => {
      await logEventWithStatus(Events.findStorageUnit, EventStatus.failed);
      setAlertView(true);
    },
    onSettled: data => {
      setIsLoading(false);
    },
  });

  const getLocationFromState = async (stateCode: string) => {
    await logEventWithStatus(Events.findStorageUnit, EventStatus.initiated);
    FetchLocationInState.reset();
    await FetchLocationInState.mutateAsync(stateCode);
  };

  const validForm = (
    _phoneNumberValue: string,
    _lastName: string,
    _selectLocationId: number,
  ): boolean => {
    const phoneRegex = /^\d{10}$/;

    if (!phoneRegex.test(_phoneNumberValue.replaceAll('-', ''))) {
      return false;
    }

    if (!_lastName || !_selectLocationId) {
      return false;
    }

    // All validations passed
    return true;
  };

  const handleOnCancel = () => {
    navigation.goBack();
  };

  const handleOnInfoClick = () => {
    navigation.goBack();
  };

  const renderTooltip = () => (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SSToolTip
        title="Account Number Identification"
        description="Your account number is listed on the lease agreement you received when you rented your unit."
        onClose={() => setToolTipVisible(false)}
      />
    </GestureHandlerRootView>
  );

  const SentEmailClick = async () => {
    setIsLoading(true);
    const findAccountRequestData: FindAccountRequest = {
      phoneNumber,
      lastName,
      firstName: '',
      locationId: selectedLocationCode,
    };
    callFindStorageAccountApi.reset();
    await callFindStorageAccountApi.mutateAsync(findAccountRequestData);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[layout.flex_1]}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={[layout.flex_1, { backgroundColor: colors.bluePrimary }]}>
          <View
            style={[
              layout.flex_1,
              gutters.padding_24,
              { borderTopLeftRadius: 20, borderTopRightRadius: 20 },
              backgrounds.white,
            ]}
          >
            <ScrollView contentContainerStyle={layout.flexGrow_1}>
              <View style={[layout.flex_1, gutters.gap_16]}>
                <View style={[layout.justifyBetween, layout.row, layout.itemsCenter]}>
                  <Text
                    style={[fonts.appFontBold, fonts.charcoal, fonts.size_20]}>
                    Find Storage Account Number
                  </Text>

                  <Tooltip
                    isVisible={toolTipVisible}
                    contentStyle={[
                      { backgroundColor: '#E6F2F9' },
                      borders.rounded_8,
                      gutters.padding_16,
                      gutters.gap_8,
                    ]}
                    arrowSize={{
                      width: 16,
                      height: 16,
                    }}
                    displayInsets={{ top: 0, bottom: 0, left: 12, right: 16 }}
                    topAdjustment={Platform.OS === 'android' ? -27.1 : -2}
                    content={renderTooltip()}
                    placement="bottom"
                    onClose={() => setToolTipVisible(false)}
                    closeOnChildInteraction={false}
                    closeOnContentInteraction={false}
                  >
                    <TouchableOpacity onPress={() => setToolTipVisible(true)}>
                      <InfoSvg />
                    </TouchableOpacity>
                  </Tooltip>
                </View>

                <Text
                  style={[fonts.appFontRegular, fonts.charcoal, fonts.size_14]}
                >
                  Please enter the location, phone number, and last name
                  associated with your account, and we&apos;ll send the account
                  ID to the email on file for that unit.
                </Text>
                <Text
                  style={[fonts.appFontRegular, fonts.charcoal, fonts.size_14]}
                >
                  Not sure if you provided an email when you signed your lease?
                  Please call (************* to speak with a customer
                  representative.
                </Text>
                <View
                  style={[layout.fullWidth, backgrounds.gray100, layout.h_1]}
                />
                {alertView && (
                  <SSErrorView
                    title="Unable to find account"
                    description="Sorry, we couldn't find your account with the information you entered. Please double check your details and try again."
                  />
                )}

                <View style={[layout.fullWidth, gutters.gap_16]}>
                  <SSDropdownInput
                    label="State*"
                    value={selectedStateLable}
                    isError={alertView}
                    data={stateArray.map(state => ({
                      label: state.name,
                      value: state.abbreviation,
                    }))}
                    onSelect={(item: { label: string; value: string }) => {
                      setSelectedLocationCode(0);
                      setSelectedLocationLable('');
                      setSelectedStateLable(item.label);
                      void getLocationFromState(item.value);
                    }}
                  />
                  <SSDropdownInput
                    label="Location*"
                    isError={alertView}
                    disabled={selectedStateLable?.length <= 0 ? true : false}
                    value={selectedLocationLable}
                    data={locationInStateArray.map(location => ({
                      label: location.name,
                      value: `${location.locationId}`,
                    }))}
                    onSelect={(item: { label: string; value: string }) => {
                      setSelectedLocationCode(0);
                      setSelectedLocationCode(Number(item.value));
                      setSelectedLocationLable(item.label);
                    }}
                  />
                  <SSTextInput
                    label="Phone Number*"
                    isError={alertView}
                    maxLength={20}
                    keyboardType="numeric"
                    value={phoneNumber}
                    onChangeText={(value: string) => {
                      setPhoneNumber(formatPhoneNumber(value));
                    }}
                  />

                  <SSTextInput
                    label="Last Name*"
                    isError={alertView}
                    value={lastName}
                    onChangeText={(value: string) => {
                      setLastName(value);
                    }}
                  />
                </View>
              </View>
              <View style={[gutters.paddingBottom_20, gutters.paddingTop_20]}>
                <View style={[gutters.gap_8]}>
                  <SSButton
                    title="SEND EMAIL"
                    onPress={() => void SentEmailClick()}
                    accessibilityLabel="SEND EMAIL"
                    variant={
                      validForm(phoneNumber, lastName, selectedLocationCode)
                        ? 'dark'
                        : 'light'
                    }
                  />
                </View>
                <View style={[gutters.marginTop_10]}>
                  <SSButton
                    title="CANCEL"
                    onPress={handleOnCancel}
                    accessibilityLabel="CANCEL"
                    variant="transparent"
                  />
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {isLoading && (
        <Modal
          transparent
          animationType="none"
          visible={isLoading}
          onRequestClose={() => null}
        >
          <View
            style={[
              layout.flex_1,
              layout.itemsCenter,
              layout.justifyCenter,
              { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
            ]}
          >
            <View
              style={[
                backgrounds.white,
                borders.rounded_12,
                { height: 100, width: 100 },
                layout.itemsCenter,
                layout.justifyCenter,
              ]}
            >
              <ActivityIndicator size="large" color="#0000ff" />
            </View>
          </View>
        </Modal>
      )}
    </KeyboardAvoidingView>
  );
}

export default FindStorageNumber;
