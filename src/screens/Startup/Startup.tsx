import React from 'react';
import { Image, ImageSourcePropType, View, StatusBar } from 'react-native';
import { useTheme } from '@/theme';
import { CombinedImagePNG } from '../../theme/assets/images/index';

function Startup() {
  const { layout, backgrounds } = useTheme();

  // using methods instead of component because the component settings were not sticking on android for some reason
  StatusBar.setBarStyle('dark-content');
  StatusBar.setTranslucent(true);
  StatusBar.setBackgroundColor('transparent');

  return (
    <View
      style={[
        layout.flex_1,
        layout.col,
        layout.itemsCenter,
        layout.justifyCenter,
        backgrounds.blueLight,
      ]}
    >
      <Image
        source={CombinedImagePNG as ImageSourcePropType}
        style={[layout.fullHeight, layout.fullWidth, { resizeMode: 'cover' }]}
      />
    </View>
  );
}

export default Startup;
