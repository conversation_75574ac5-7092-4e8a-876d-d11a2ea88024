import { NativeModules, NativeEventEmitter, NativeModule } from 'react-native';
import Config from 'react-native-config';

const { NOKE_MODE } = Config;

export enum NokeModes {
  NOKE_LIBRARY_SANDBOX = 0,
  NOKE_LIBRARY_PRODUCTION = 1,
  NOKE_LIBRARY_DEVELOP = 2,
  NOKE_LIBRARY_OPEN = 3,
  NOKE_LIBRARY_CUSTOM = 4,
}

type NokeNativeModuleType = {
  getConstants: () => {
    NOKE_DISCOVERED: string;
    NOKE_CONNECTING: string;
    NOKE_CONNECTED: string;
    NOKE_SYNCING: string;
    NOKE_UNLOCKED: string;
    NOKE_SHUTDOWN: string;
    NOKE_DISCONNECTED: string;
    DATA_UPLOADED: string;
    BLUETOOTH_STATUS_CHANGED: string;
    ERROR: string;
    NOKE_ERROR?: string; // iOS Only
    NOKE_FIRMWARE_UPDATE?: string; // iOS Only
  };

  initNoke: (mode: NokeModes) => void;
  searchForLock: (name: string, mac: string) => void;
  stopSearchingForLock: (mac: string) => void;
  stopSearchingForAllLocks: () => void;
  connectToLock: (mac: string) => void;
  disconnectFromLock: () => void;
  sendCommand: (commandString: string) => void;
  offlineUnlock: (offlineKey: string, unlockCommand: string) => void;
  scheduledOfflineUnlock: (offlineKey: string, unlockCommand: string) => void;
};

const nokeNativeModule = NativeModules.NokeModule as NokeNativeModuleType &
  NativeModule;
const eventEmitter = new NativeEventEmitter(nokeNativeModule);

let initialized = false;
const initNoke = () => {
  if (!initialized) {
    let mode = NokeModes.NOKE_LIBRARY_SANDBOX;
    if (NOKE_MODE) {
      const modeInt = parseInt(NOKE_MODE, 10);
      if (!Number.isNaN(modeInt) && typeof NokeModes[modeInt] !== undefined) {
        mode = modeInt as NokeModes;
      }
    }
    nokeNativeModule.initNoke(mode);
    initialized = true;
  }
};

export default {
  initNoke,
  searchForLock: nokeNativeModule.searchForLock,
  stopSearchingForLock: nokeNativeModule.stopSearchingForLock,
  stopSearchingForAllLocks: nokeNativeModule.stopSearchingForAllLocks,
  connectToLock: nokeNativeModule.connectToLock,
  disconnectFromLock: nokeNativeModule.disconnectFromLock,
  sendCommand: nokeNativeModule.sendCommand,
  offlineUnlock: nokeNativeModule.offlineUnlock,
  scheduledOfflineUnlock: nokeNativeModule.scheduledOfflineUnlock,

  events: nokeNativeModule.getConstants(),
  eventEmitter,
};

export type NokeDeviceData = {
  mac: string;
  name: string;
  session: string;
};

export type NokeDeviceEvent = { noke: NokeDeviceData };
export type NokeErrorEvent = {
  error: number;
  message: string;
  noke?: NokeDeviceData;
};
