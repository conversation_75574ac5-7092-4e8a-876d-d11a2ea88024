import React from 'react';
import { View, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/theme';
import type { ComponentProps } from 'react';

interface SafeModalScreenProps extends ComponentProps<typeof View> {
  isFullScreen?: boolean;
}

function SafeModalScreen({
  children,
  style,
  isFullScreen = false,
}: SafeModalScreenProps) {
  const { layout } = useTheme();
  const insets = useSafeAreaInsets();

  const containerStyle = [
    layout.flex_1,
    !isFullScreen && {
      paddingTop: insets.top,
      paddingBottom: insets.bottom,
      paddingLeft: insets.left,
      paddingRight: insets.right,
    },
    style,
  ];

  return (
    <View style={containerStyle}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />
      {children}
    </View>
  );
}

export default SafeModalScreen;
