import React from 'react';
import { View, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/theme';
import type { ComponentProps } from 'react';

interface SafeScreenProps extends ComponentProps<typeof View> {
  isFullScreen?: boolean;
  statusBarStyle?: 'light-content' | 'dark-content';
}

function SafeScreen({
  children,
  style,
  isFullScreen = false,
  statusBarStyle,
}: SafeScreenProps) {
  const { layout } = useTheme();
  const insets = useSafeAreaInsets();

  const containerStyle = [
    layout.flex_1,
    !isFullScreen && {
      paddingTop: insets.top,
      paddingBottom: insets.bottom,
      paddingLeft: insets.left,
      paddingRight: insets.right,
    },
    style,
  ];

  return (
    <View style={containerStyle}>
      {!!statusBarStyle && (
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor="transparent"
          translucent
        />
      )}
      {children}
    </View>
  );
}

export default SafeScreen;
