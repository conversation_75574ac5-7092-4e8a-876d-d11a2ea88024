import useTheme from '@/theme/hooks/useTheme';
import React, { ReactElement } from 'react';
import { View, Text, Pressable } from 'react-native';

type SSActionButtonProps = {
  icon: ReactElement<unknown, string>;
  label: string;
  onPress: () => void;
};

function SSActionButton(props: SSActionButtonProps) {
  const { icon, label, onPress } = props;
  const { layout, fonts, gutters } = useTheme();
  return (
    <Pressable style={[layout.flex_1]} onPress={onPress}>
      <View
        style={[
          gutters.gap_8,
          gutters.padding_8,
          layout.selfCenter,
          layout.itemsCenter,
        ]}
      >
        {icon}
        <Text
          style={[
            layout.flex_1,
            fonts.alignCenter,
            fonts.charcoal,
            fonts.size_12,
            fonts.appFontBold,
            fonts.uppercase,
          ]}
        >
          {label}
        </Text>
      </View>
    </Pressable>
  );
}

export default SSActionButton;
