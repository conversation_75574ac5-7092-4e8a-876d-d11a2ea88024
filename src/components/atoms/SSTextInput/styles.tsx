import { Platform, StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    color: '#49658c',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 11,
    backgroundColor: '#00000000',
    paddingTop: 10,
    paddingBottom: 10,
    alignContent: 'flex-start',
    justifyContent: 'flex-start',
  },
  input: {
    minHeight: 28,
    fontFamily: 'FilsonPro-Medium',
    fontSize: 16,
    color: '#161718',
    verticalAlign: 'top',
    paddingTop: 10,
    flex: 1,
    zIndex: 10,
    marginVertical: Platform.OS === 'android' ? undefined : 8,
    paddingVertical: Platform.OS === 'android' ? 0 : undefined,
    marginBottom: 0,
  },
  img: {
    height: 25,
    width: 25,
    alignSelf: 'center',
  },
  toggleButton: {
    zIndex: 11,
    alignSelf: 'center',
    justifyContent: 'flex-end',
  },
  countdown: {
    position: 'absolute',
    right: 11,
    bottom: 0,
    color: '#49658c',
    fontSize: 10,
  },
});
