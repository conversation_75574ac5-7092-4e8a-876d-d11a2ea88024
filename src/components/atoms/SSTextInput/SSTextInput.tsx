import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  TextProps,
  TextInputProps,
  TextStyle,
  ViewStyle,
  ImageStyle,
  TouchableWithoutFeedback,
  LayoutChangeEvent,
  StyleSheet,
  KeyboardTypeOptions,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
  Easing,
  useDerivedValue,
  interpolateColor,
  useSharedValue,
} from 'react-native-reanimated';
import {
  getValueWithCurrencyMask,
  getValueWithNonCurrencyMask,
} from '@/components/atoms/SSTextInput/utils';
import { styles } from '@/components/atoms/SSTextInput/styles';

import EyeOpenSVG from '@/assets/svg/EyeOpen';
import EyeCloseSVG from '@/assets/svg/EyeClose';
import { BiomatricInputSVG } from '@/assets/svg';
import { useTheme } from '@/theme';

export interface Props extends Omit<TextInputProps, 'secureTextEntry'> {
  /** Style to the container of whole component */
  containerStyles?: ViewStyle;
  /** Changes the color for hide/show password image */
  darkTheme?: true | false;
  /** Set this to true if you want the label to be always at a set position. Commonly used with hint for displaying both label and hint for your input. Default false. */
  staticLabel?: boolean;
  /** Hint displays only when staticLabel prop is set to true. This prop is used to show a preview of the input to the user */
  hint?: string;
  /** Set the color to the hint */
  hintTextColor?: string;
  /** Value for the label, same as placeholder */
  label: React.ReactNode;
  /** Style to the label */
  labelStyles?: TextStyle;
  /** Set this to true if is password to have a show/hide input and secureTextEntry automatically */
  isPassword?: true | false;
  /** Callback for action submit on the keyboard */
  onSubmit?: () => void;
  /** Style to the show/hide password container */
  showPasswordContainerStyles?: ViewStyle;
  /** Style to the show/hide password image */
  showPasswordImageStyles?: ImageStyle;
  /** Style to the input */
  inputStyles?: TextStyle;
  /** Path to your custom image for show input */
  customShowPasswordImage?: string;
  /** Path to your custom image for hide input */
  customHidePasswordImage?: string;
  /** Custom Style for position, size and color for label, when it's focused or blurred */
  customLabelStyles?: CustomLabelProps;
  /** Required if onFocus or onBlur is overrided */
  isFocused?: boolean;
  /** Set a mask to your input */
  mask?: string;
  /** Set mask type */
  maskType?: 'currency' | 'phone' | 'date' | 'card';
  /** Set currency thousand dividers */
  currencyDivider?: ',' | '.';
  /** Maxinum number of decimal places allowed for currency mask. */
  maxDecimalPlaces?: number;
  /** Set currency on input value */
  currency?: string;
  /** Changes the input from single line input to multiline input */
  multiline?: true | false;
  /** Maxinum number of characters allowed. Overriden by mask if present */
  maxLength?: number;
  /** Shows the remaining number of characters allowed to be typed if maxLength or mask are present */
  showCountdown?: true | false;
  /** Style to the countdown text */
  showCountdownStyles?: TextStyle;
  /** Label for the remaining number of characters allowed shown after the number */
  countdownLabel?: string;
  /** Callback for show/hide password */
  onTogglePassword?: (show: boolean) => void;
  /** Prop for force toggling show/hide password. If set to true, shows the password, and when set to false hides it. */
  togglePassword?: boolean;
  /** Set custom animation duration. Default 300 ms */
  animationDuration?: number;
  /** Label Props */
  labelProps?: TextProps;
  /** Prop for show error view. */
  isError?: boolean;
  /** Prop for show biomatric image. */
  isBioMatric?: boolean;
  /** Callback for biomatric action */
  onBiomatricAction?: () => void;
  keyboardType?: KeyboardTypeOptions;
  numberOfLines?: number;
  isEditable?: boolean;
  autoComplete?: TextInputProps['autoComplete'];
  textContentType?: TextInputProps['textContentType'];
  importantForAutofill?: 'auto' | 'yes' | 'no';
}

export interface CustomLabelProps {
  /** Absolute distance from left-most side of the input when focused */
  leftFocused?: number;
  /** Absolute distance from left-most side of the input when blurred */
  leftBlurred?: number;
  /** Absolute distance from center of the input when focused */
  topFocused?: number;
  /** Absolute distance from center of the input when blurred */
  topBlurred?: number;
  /** Font size of label the when focused */
  fontSizeFocused?: number;
  /** Font size of label the when blurred */
  fontSizeBlurred?: number;
  /** Font color of label the when blurred */
  colorFocused?: string;
  /** Font color of label the when blurred */
  colorBlurred?: string;
}

interface InputRef {
  onFocus(): void;
  onBlur(): void;
}

const AnimatedText = Animated.createAnimatedComponent(Text);

const SSTextInput = React.forwardRef<InputRef, Props>(
  (
    {
      label,
      labelProps,
      mask,
      isPassword,
      maxLength,
      inputStyles,
      showCountdown,
      showCountdownStyles,
      labelStyles,
      darkTheme,
      countdownLabel,
      currencyDivider,
      currency,
      maskType,
      onChangeText,
      isFocused,
      onBlur,
      onFocus,
      onTogglePassword,
      togglePassword,
      customHidePasswordImage,
      customLabelStyles: oldLabelStyles = {},
      staticLabel = false,
      hint,
      hintTextColor,
      onSubmit,
      containerStyles: oldContainerStyles,
      customShowPasswordImage,
      showPasswordContainerStyles,
      maxDecimalPlaces,
      multiline = false,
      numberOfLines = 1,
      showPasswordImageStyles,
      value = '',
      animationDuration,
      isError = false,
      isBioMatric = false,
      isEditable = true,
      onBiomatricAction,
      keyboardType = 'ascii-capable',
      autoComplete,
      textContentType,
      importantForAutofill,
      ...rest
    },
    ref,
  ) => {
    const [halfTop, setHalfTop] = useState(0);
    const [isFocusedState, setIsFocused] = useState(false);
    const [secureText, setSecureText] = useState(true);
    const inputRef = useRef<TextInput>(null);
    const isFirstRender = useRef(true);
    const { layout, borders, fonts, gutters, backgrounds } = useTheme();

    const sharedValueOpacity = useSharedValue(
      isFirstRender.current && value ? 0 : 1,
    );

    const customLabelStyles = StyleSheet.flatten([
      {
        fontSizeFocused: fonts.size_12.fontSize,
        fontSizeBlurred: fonts.size_16.fontSize,
        colorFocused: fonts.lightCharcoal.color,
        colorBlurred: fonts.lightCharcoal.color,
      },
      oldLabelStyles,
    ]);

    const [fontColorAnimated, setFontColorAnimated] = useState(0);

    const [fontSizeAnimated, setFontSizeAnimated] = useState(
      isFocused
        ? customLabelStyles.fontSizeFocused ?? fonts.size_12.fontSize
        : customLabelStyles.fontSizeBlurred ?? fonts.size_16.fontSize,
    );

    const [leftAnimated, setLeftAnimated] = useState(
      staticLabel
        ? customLabelStyles?.leftFocused ?? fonts.size_15.fontSize
        : customLabelStyles?.leftBlurred ?? fonts.size_6.fontSize,
    );

    const [topAnimated, setTopAnimated] = useState(
      staticLabel
        ? customLabelStyles?.topFocused ?? fonts.size_0.fontSize
        : customLabelStyles?.topBlurred ?? fonts.size_0.fontSize,
    );

    const setFocus = () => {
      if (!isEditable) {
        return;
      }
      if (inputRef && inputRef.current && inputRef.current.focus) {
        inputRef.current.focus();
      }
    };

    const setBlur = () => {
      if (inputRef && inputRef.current && inputRef.current.blur) {
        inputRef.current.blur();
      }
    };

    function toggleVisibility(toggle?: boolean) {
      if (toggle === undefined) {
        if (onTogglePassword) {
          onTogglePassword(!secureText);
        }
        setSecureText(!secureText);
      } else if (!((secureText && !toggle) || (!secureText && toggle))) {
        if (onTogglePassword) {
          onTogglePassword(!toggle);
        }
        setSecureText(!toggle);
        if (toggle) {
          setFocus();
        } else {
          setBlur();
        }
      }
    }

    function animateFocus() {
      if (!staticLabel) {
        setLeftAnimated(
          customLabelStyles.leftFocused
            ? customLabelStyles.leftFocused
            : fonts.size_0.fontSize,
        );
        setFontSizeAnimated(
          customLabelStyles.fontSizeFocused
            ? customLabelStyles.fontSizeFocused
            : fonts.size_12.fontSize,
        );
        setTopAnimated(
          customLabelStyles.topFocused
            ? customLabelStyles.topFocused
            : -halfTop +
            5 +
            (customLabelStyles.fontSizeFocused
              ? customLabelStyles.fontSizeFocused
              : fonts.size_12.fontSize),
        );
        setFontColorAnimated(1);
      } else {
        setFontColorAnimated(1);
      }
    }

    function animateBlur() {
      if (!staticLabel) {
        setLeftAnimated(
          customLabelStyles.leftBlurred
            ? customLabelStyles.leftBlurred
            : fonts.size_6.fontSize,
        );
        setFontSizeAnimated(
          customLabelStyles.fontSizeBlurred
            ? customLabelStyles.fontSizeBlurred
            : 14,
        );
        setTopAnimated(
          customLabelStyles.topBlurred
            ? customLabelStyles.topBlurred
            : fonts.size_0.fontSize,
        );
        setFontColorAnimated(0);
      } else {
        setFontColorAnimated(0);
      }
    }

    useEffect(() => {
      if (isFocused === undefined) {
        if (value !== '' || isFocusedState) {
          setIsFocused(true);
        } else if (value === '' || value === null) {
          setIsFocused(false);
        }
      }
    }, [value]);

    useEffect(() => {
      if (isFocused !== undefined) {
        if (value !== '' || isFocused) {
          setIsFocused(true);
        } else {
          setIsFocused(false);
        }
      }
    }, [isFocused, value]);

    useEffect(() => {
      if (togglePassword !== undefined && isFocusedState) {
        toggleVisibility(togglePassword);
      }
    }, [togglePassword]);

    useEffect(() => {
      if (isFocusedState || value !== '') {
        if (halfTop !== 0) {
          animateFocus();
        }
      } else {
        animateBlur();
      }
    }, [isFocusedState, halfTop, value]);

    useImperativeHandle(ref, () => ({
      onFocus() {
        if (inputRef && inputRef.current && inputRef.current.focus) {
          inputRef.current.focus();
        }
      },
      onBlur() {
        if (inputRef && inputRef.current && inputRef.current.blur) {
          inputRef.current.blur();
        }
      },
    }));

    function handleFocus() {
      setIsFocused(true);
    }

    function handleBlur() {
      setIsFocused(false);
    }

    const onSubmitEditing = () => {
      if (onSubmit !== undefined) {
        onSubmit();
      }
    };

    const style: TextStyle = StyleSheet.flatten([
      labelStyles,
      multiline && numberOfLines > 1
        ? { alignContent: 'flex-start' }
        : layout.selfCenter,
      layout.absolute,
      layout.flex_1,
      layout.z999,
      fonts.appFontRegular,
    ]);

    let input: TextStyle = inputStyles ?? styles.input;

    input = StyleSheet.flatten([
      input,
      layout.flex_1,
      gutters.marginTop_10,
      gutters.paddingTop_0,
      gutters.paddingBottom_0,
      fonts.appFontMedium,
      {
        color:
          input.color !== undefined
            ? input.color
            : customLabelStyles.colorFocused,
        zIndex: style?.zIndex !== undefined ? style.zIndex - 2 : 0,
      },
    ]);

    let containerStyles: ViewStyle | undefined = oldContainerStyles;

    let bordersColor: string = borders.lightGray.borderColor;
    if (isError) {
      bordersColor = borders.cherry.borderColor;
    } else if (isFocusedState) {
      bordersColor = borders.bluePrimary.borderColor;
    }
    let bgColor: string = backgrounds.white.backgroundColor;
    if (!isEditable) {
      bordersColor = borders.lightGray.borderColor;
      bgColor = backgrounds.gray10.backgroundColor;
    }
    if (!containerStyles) {
      containerStyles = {
        ...styles.container,
        borderColor: bordersColor,
        backgroundColor: bgColor,
      };
    }

    containerStyles = StyleSheet.flatten([
      containerStyles,
      layout.itemsCenter,
      layout.row,
      layout.flex_1,
      {
        zIndex: style?.zIndex !== undefined ? style.zIndex - 6 : 0,
      },
    ]);

    let toggleButton = showPasswordContainerStyles ?? styles.toggleButton;

    toggleButton = StyleSheet.flatten([toggleButton, layout.selfCenter]);

    const countdown = StyleSheet.flatten([
      styles.countdown,
      showCountdownStyles,
    ]);

    const onChangeTextCallback = (val: string): void | undefined => {
      if (onChangeText === undefined) return undefined;

      if (maskType === undefined && mask === undefined)
        return onChangeText(val);

      let newValue: string | undefined;

      if (maskType !== 'currency' && mask !== undefined) {
        newValue = getValueWithNonCurrencyMask({ value: val, mask });
      }

      if (maskType === 'currency') {
        if (
          currency !== undefined &&
          !/^[0-9]+$/g.test(val.replace(/[.,]/g, '').replace(currency, '')) &&
          val.replace(/[.,]/g, '').replace(currency, '') !== ''
        ) {
          return undefined;
        }
        if (
          currency === undefined &&
          !/^[0-9]+$/g.test(val.replace(/[.,]/g, '')) &&
          val.replace(/[.,]/g, '') !== ''
        ) {
          return undefined;
        }

        newValue = getValueWithCurrencyMask({
          value: currency !== undefined ? value.replace(currency, '') : value,
          newValue: currency !== undefined ? val.replace(currency, '') : val,
          currencyDivider,
          maxDecimalPlaces,
        });
      }

      if (newValue !== undefined) {
        return onChangeText(
          (currency !== undefined ? currency : '') + newValue,
        );
      }
      return undefined;
    };

    const onLayout = (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      if (multiline && numberOfLines > 1) {
        setHalfTop(20);
      } else {
        setHalfTop(height / 2);
      }
    };

    useEffect(() => {
      sharedValueOpacity.value = 1;
    }, []);

    const positionAnimations = useAnimatedStyle(() => {
      return {
        transform: [
          {
            translateX: withTiming(leftAnimated, {
              duration: animationDuration || 150,
              easing: Easing.inOut(Easing.ease),
            }),
          },
          {
            translateY: withTiming(topAnimated, {
              duration: animationDuration || 150,
              easing: Easing.inOut(Easing.ease),
            }),
          },
        ],
        opacity: withTiming(sharedValueOpacity.value, {
          duration: animationDuration || 300,
          easing: Easing.inOut(Easing.ease),
        }),
        fontSize: withTiming(fontSizeAnimated, {
          duration: animationDuration || 150,
          easing: Easing.inOut(Easing.ease),
        }),
      };
    });

    const progress = useDerivedValue(() => {
      return withTiming(fontColorAnimated, {
        duration: animationDuration || 100,
        easing: Easing.inOut(Easing.ease),
      });
    });

    const colorAnimation = useAnimatedStyle(() => {
      const color = interpolateColor(
        progress.value,
        [0, 1],
        [
          customLabelStyles.colorBlurred ?? '#000',
          customLabelStyles.colorFocused ?? '#000',
        ],
      );

      return {
        color,
      };
    });

    // Calculate height based on numberOfLines
    const calculatedHeight = 24 * numberOfLines; // Adjust the multiplier as needed

    return (
      <TouchableWithoutFeedback
        style={[layout.flex_1]}
        onPress={setFocus}
        onLayout={onLayout}
      >
        <View style={[layout.row, layout.flex_1]}>
          {staticLabel && (
            <AnimatedText
              {...labelProps}
              onPress={setFocus}
              style={[
                style,
                colorAnimation,
                {
                  left:
                    labelStyles?.left ??
                    customLabelStyles.leftFocused ??
                    fonts.size_20.fontSize,
                  top: -(style?.fontSize ?? fonts.size_10.fontSize) / 2,
                },
              ]}
            >
              {label}
            </AnimatedText>
          )}
          <View style={containerStyles}>
            <View style={[layout.row, layout.flex_1, gutters.gap_4]}>
              {!staticLabel && (
                <AnimatedText
                  {...labelProps}
                  suppressHighlighting
                  onPress={setFocus}
                  style={[
                    layout.opacity_0,
                    style,
                    positionAnimations,
                    colorAnimation,
                  ]}
                  accessibilityLabel={label ? String(label) : ''} // Use the label for accessibility
                  accessibilityRole="text" // 
                >
                  {label}
                </AnimatedText>
              )}
              <TextInput
                value={value}
                onSubmitEditing={onSubmitEditing}
                secureTextEntry={
                  isPassword !== undefined ? isPassword && secureText : false
                }
                onFocus={onFocus !== undefined ? onFocus : handleFocus}
                onBlur={onBlur !== undefined ? onBlur : handleBlur}
                ref={inputRef}
                autoCapitalize="none"
                {...rest}
                maxLength={mask?.length ?? maxLength ?? undefined}
                placeholderTextColor={hintTextColor}
                placeholder={
                  (staticLabel || isFocusedState) && hint ? hint : ''
                }
                multiline={multiline}
                onChangeText={onChangeTextCallback}
                style={[input, { height: calculatedHeight }, rest.style]}
                numberOfLines={numberOfLines}
                keyboardType={keyboardType}
                autoComplete={autoComplete}
                textContentType={textContentType}
                importantForAutofill={importantForAutofill}
                editable={isEditable}
                accessibilityRole="combobox"
                accessibilityLabel={String(label)}
                accessibilityValue={{ text: value }}
              />
              {isPassword && (
                <TouchableOpacity
                  accessibilityLabel={
                    secureText ? 'Hide Password Icon' : 'Show Password Icon'
                  }
                  style={toggleButton}
                  onPress={() => toggleVisibility()}
                >
                  {secureText ? <EyeOpenSVG /> : <EyeCloseSVG />}
                </TouchableOpacity>
              )}
              {isBioMatric && (
                <TouchableOpacity
                  accessibilityLabel="Use biometric authentication"
                  style={toggleButton}
                  onPress={onBiomatricAction}
                >
                  <BiomatricInputSVG />
                </TouchableOpacity>
              )}
            </View>
            {showCountdown && maxLength && (
              <Text style={countdown}>
                {maxLength - (value?.length ?? 0)} {countdownLabel}
              </Text>
            )}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  },
);

export default SSTextInput;
