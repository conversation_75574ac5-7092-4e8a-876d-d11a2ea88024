import { UpArrowSVG, DownArrowSVG } from '@/assets/svg';
import { Unit } from '@/types/schemas/unit';
import React, { useState } from 'react';
import { Pressable, View, Text, Platform } from 'react-native';
import useTheme from '@/theme/hooks/useTheme';
import OverduePaymentMessage from '@/components/atoms/OverduePaymentMessage';

import { fixedFontSize } from '@/utils/commonFunctions';
import SSButton from './SSButton/SSButton';

/**
 * Props for the unit header component
 * @typedef {Object} UnitHeaderProps
 * @property {() => void} [toggleVisibility] - Function to toggle the visibility of unit details
 * @property {boolean} isVisible - Whether the unit details are visible
 * @property {boolean} [isDropDownVisible] - Whether to show the dropdown arrow
 * @property {string} unitNumber - The unit's identification number
 */
type UnitHeaderProps = {
  toggleVisibility?: () => void;
  isVisible: boolean;
  isDropDownVisible?: boolean;
  unitNumber: string;
};

/**
 * Props for the main SSUnitInfo component
 * @typedef {Object} SSUnitInfoProps
 * @property {Unit} unit - The unit object containing all unit details
 * @property {boolean} [isToggleVisible] - Whether the toggle functionality is enabled
 * @property {(unit: Unit) => void} onPress - Callback function when unit is pressed
 * @property {(unit: Unit) => void} handleOnUnitNotesPressed - Callback function when unit notes are pressed
 */
type SSUnitInfoProps = {
  unit: Unit;
  isToggleVisible?: boolean | undefined;
  onPress: (unit: Unit) => void;
  handleOnUnitNotesPressed: (unit: Unit) => void;
};

/**
 * Props for the unit details component
 * @typedef {Object} UnitDetailsProps
 * @property {Unit} unit - The unit object containing all unit details
 * @property {(unit: Unit) => void} onPress - Callback function when unit is pressed
 * @property {(unit: Unit) => void} handleOnUnitNotesPressed - Callback function when unit notes are pressed
 */
type UnitDetailsProps = {
  unit: Unit;
  onPress: (unit: Unit) => void;
  handleOnUnitNotesPressed: (unit: Unit) => void;
};

/**
 * Renders the header section of a unit with optional toggle functionality
 * @param {UnitHeaderProps} props - The component props
 * @returns {JSX.Element} The unit header component
 */
function UnitHeader(props: UnitHeaderProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { toggleVisibility, isVisible, isDropDownVisible, unitNumber } = props;

  return (
    <Pressable onPress={() => toggleVisibility?.()}>
      {!isVisible && (
        <View
          style={[
            layout.flex_1,
            layout.h_16,
            layout.fullWidth,
            backgrounds.blue,
            borders.topLeftRounded_8,
            borders.topRightRounded_8,
          ]}
        />
      )}
      <View
        style={[
          layout.row,
          layout.flex_1,
          layout.justifyBetween,
          borders.gray100,
          gutters.padding_4,
          isVisible ? backgrounds.blue : backgrounds.white,
          isVisible ? borders.topLeftRounded_16 : borders.bottomLeftRounded_8,
          isVisible ? borders.topRightRounded_12 : borders.bottomRightRounded_8,
        ]}
      >
        <Text
          style={[
            gutters.padding_8,
            fonts.uppercase,
            fonts.size_14,
            fonts.appFontBold,
            isVisible ? fonts.white : fonts.charcoal,
          ]}
        >
          Unit No.{' '}
          <Text
            style={[
              fonts.size_14,
              fonts.appFontMedium,
              isVisible ? fonts.white : fonts.charcoal,
            ]}
          >
            {unitNumber}
          </Text>
        </Text>
        {isDropDownVisible && (
          <View style={[layout.selfCenter, gutters.paddingRight_16]}>
            {isVisible ? <UpArrowSVG /> : <DownArrowSVG />}
          </View>
        )}
      </View>
    </Pressable>
  );
}

/**
 * Renders detailed information about a unit including rent, due date, and action buttons
 * @param {UnitDetailsProps} props - The component props
 * @returns {JSX.Element} The unit details component
 */
function UnitDetails(props: UnitDetailsProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { unit, handleOnUnitNotesPressed, onPress } = props;
  const formattedPayment = parseFloat(unit.nextPayment).toFixed(2);

  return (
    <View>
      <View
        style={[
          layout.row,
          layout.flex_1,
          layout.justifyBetween,
          gutters.paddingTop_24,
          gutters.paddingRight_16,
          gutters.paddingBottom_8,
          gutters.paddingLeft_16,
          gutters.gap_32,
        ]}
      >
        <View style={[gutters.gap_4]}>
          <Text
            style={[
              fonts.uppercase,
              fonts.appFontBold,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
              fonts.lightCharcoal,
            ]}
            allowFontScaling={false}
          >
            RENT
          </Text>
          <Text
            style={[
              fonts.uppercase,
              fonts.appFontRegular,
              fonts.charcoal,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
            ]}
            allowFontScaling={false}
          >
            {formattedPayment}
          </Text>
        </View>
        <View style={[gutters.gap_4, layout.flex_1]}>
          <Text
            style={[
              fonts.uppercase,
              fonts.appFontBold,
              fonts.lightCharcoal,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
            ]}
            allowFontScaling={false}
          >
            DUE ON
          </Text>
          <Text
            style={[
              fonts.uppercase,
              fonts.appFontRegular,
              fonts.charcoal,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
            ]}
            allowFontScaling={false}
          >
            {unit.nextPaymentDue}
          </Text>
        </View>
        <View
          style={[
            unit.autopay ? backgrounds.olive : backgrounds.lightGreen,
            borders.rounded_12,
            gutters.paddingTop_4,
            gutters.paddingRight_12,
            gutters.paddingBottom_4,
            gutters.paddingLeft_12,
            layout.itemsCenter,
            layout.justifyCenter,
            layout.h_24,
          ]}
        >
          <Text
            style={[
              unit.autopay ? fonts.white : fonts.charcoal,
              fonts.appFontBold,
              fonts.uppercase,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
            ]}
            allowFontScaling={false}
          >
            {unit.autopay ? 'SmartPay On' : 'SmartPay off'}
          </Text>
        </View>
      </View>
      <View style={[gutters.paddingLeft_16]}>
        <Text
          style={[fonts.size_12, fonts.appFontRegular, fonts.lightCharcoal]}
        >
          Protection plan: {unit.currentInsurance}
        </Text>
      </View>
      <View style={[gutters.padding_16, gutters.gap_16]}>
        {!unit.autopay && !unit.canMakePayment && (
          <OverduePaymentMessage reason={unit.noPaymentReason} />
        )}
        {!unit.autopay && unit.canMakePayment && (
          <SSButton
            title="Make payment"
            bgColorStyle={backgrounds.olive}
            customBorderColorStyle={borders.olive}
            fontColorStyle={fonts.white}
            onPress={() => onPress(unit)}
          />
        )}
        <SSButton
          title="Unit Notes"
          variant="transparent"
          onPress={() => handleOnUnitNotesPressed(unit)}
        />
      </View>
    </View>
  );
}

/**
 * A component that displays comprehensive unit information with collapsible details
 * @param {SSUnitInfoProps} props - The component props
 * @returns {JSX.Element} The SSUnitInfo component
 */
function SSUnitInfo(props: SSUnitInfoProps) {
  const { unit, isToggleVisible, handleOnUnitNotesPressed, onPress } = props;
  const [isVisible, setIsVisible] = useState<boolean>(!isToggleVisible);
  const { layout, gutters, backgrounds, borders } = useTheme();

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  return (
    <View
      accessibilityLabel="Unit Information"
      style={[
        layout.col,
        gutters.marginTop_16,
        layout.flex_1,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <UnitHeader
        unitNumber={unit.unitNumber}
        isVisible={isVisible}
        isDropDownVisible={isToggleVisible}
        toggleVisibility={() => {
          if (isToggleVisible) {
            toggleVisibility();
          }
        }}
      />
      {isVisible && (
        <UnitDetails
          unit={unit}
          onPress={onPress}
          handleOnUnitNotesPressed={handleOnUnitNotesPressed}
        />
      )}
    </View>
  );
}

export default SSUnitInfo;
