import React from 'react';
import { Text, View, Image } from 'react-native';
import { useTheme } from '@/theme';
import { ContactInfo } from '@/types/schemas/account';
import profileUserIcon from '@/assets/profileUserIcon.png';
import SSButton from './SSButton/SSButton';
import { formatPhoneNumber } from '../../utils/commonFunctions';

type Props = {
  profilePicture: string | undefined;
  accountInfo: ContactInfo;
  onEditProfilePress?: () => void;
};
function ProfileCard(props: Props) {
  const { fonts, gutters, backgrounds, layout, borders } = useTheme();
  const { onEditProfilePress, accountInfo, profilePicture } = props;

  const renderUserProfileIcon = () => (
    <Image
      style={[
        layout.w_100,
        layout.h_100,
        borders.rounded_50,
        backgrounds.blue10,
        layout.absolute,
      ]}
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      source={profilePicture ? { uri: profilePicture } : profileUserIcon}
    />
  );

  const renderUserName = () => (
    <Text
      style={[
        fonts.size_24,
        fonts.charcoal,
        gutters.paddingTop_15,
        fonts.appFontBold,
        gutters.paddingTop_65,
      ]}
    >
      {`${accountInfo?.firstName} ${accountInfo?.lastName}`}
    </Text>
  );

  const renderEmail = () => (
    <Text
      style={[
        fonts.size_16,
        fonts.lightCharcoal,
        gutters.paddingTop_6,
        fonts.appFontRegular,
      ]}
    >
      {accountInfo?.email}
    </Text>
  );

  const renderPhoneNumber = () => (
    <Text
      style={[
        fonts.size_16,
        fonts.lightCharcoal,
        gutters.paddingTop_6,
        fonts.appFontRegular,
      ]}
    >
      {formatPhoneNumber(accountInfo?.phone)}
    </Text>
  );

  const renderEditProfileButton = () => (
    <View style={[gutters.marginTop_16]}>
      <SSButton
        title="EDIT PROFILE"
        accessibilityLabel="Edit profile"
        onPress={onEditProfilePress}
        variant="transparent"
        fontColorStyle={fonts.blue}
        customBorderColorStyle={borders.blue}
        fontSizeStyle={fonts.size_12}
        borderRadiusStyle={borders.rounded_13}
        heightStyle={layout.h_26}
        widthStyle={layout.w_120}
      />
    </View>
  );

  return (
    <View style={[gutters.marginTop_40, layout.itemsCenter]}>
      <View
        style={[
          gutters.marginTop_50,
          backgrounds.white,
          borders.rounded_16,
          layout.itemsCenter,
          layout.fullWidth,
          gutters.paddingHorizontal_15,
          gutters.paddingBottom_16,
        ]}
      >
        {renderUserName()}
        {renderEmail()}
        {renderPhoneNumber()}
        {renderEditProfileButton()}
      </View>
      {renderUserProfileIcon()}
    </View>
  );
}

export default ProfileCard;
