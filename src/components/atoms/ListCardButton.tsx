import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import { RightArrowCircleSVG } from '@/assets/svg';

type Props = {
  title: string;
  description?: string;
  onPress?: () => void;
};
function ListCardButton(props: Props) {
  const { fonts, layout, backgrounds, borders, gutters } = useTheme();
  const { title, description, onPress } = props;
  return (
    <TouchableOpacity
      style={[
        backgrounds.white,
        borders.rounded_16,
        layout.justifyBetween,
        layout.row,
        gutters.marginTop_16,
        gutters.paddingHorizontal_16,
        layout.itemsCenter,
      ]}
      activeOpacity={1}
      onPress={onPress}
    >
      <View
        style={[layout.col, gutters.paddingTop_16, gutters.paddingBottom_16]}
      >
        <Text style={[fonts.size_16, fonts.charcoal, fonts.appFontMedium]}>
          {title}
        </Text>
        {description && (
          <Text style={[fonts.size_14, fonts.charcoal, fonts.appFontRegular]}>
            {description}
          </Text>
        )}
      </View>
      <RightArrowCircleSVG />
    </TouchableOpacity>
  );
}

export default ListCardButton;
