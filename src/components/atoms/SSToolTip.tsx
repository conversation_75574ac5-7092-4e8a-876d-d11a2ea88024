import { CrossPrimarySmallSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import React from 'react';
import { Text, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

function SSToolTip(props: {
  title?: string;
  description?: string;
  onClose?: () => void;
}) {
  const { layout, gutters, fonts } = useTheme();
  const { title, description, onClose } = props;

  return (
    <View style={[gutters.padding_8]}>
      <View style={[layout.row, gutters.paddingRight_10]}>
        <View style={[gutters.gap_8, gutters.marginRight_16]}>
          {title && (
            <Text style={[fonts.appFontBold, fonts.charcoal, fonts.size_14]}>
              {title}
            </Text>
          )}
          {description && (
            <Text style={[fonts.appFontRegular, fonts.charcoal, fonts.size_12]}>
              {description}
            </Text>
          )}
        </View>
        <TouchableOpacity
          onPress={() => {
            if (onClose !== undefined) {
              onClose();
            }
          }}
        >
          <CrossPrimarySmallSVG style={[layout.itemsCenter]} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

export default SSToolTip;
