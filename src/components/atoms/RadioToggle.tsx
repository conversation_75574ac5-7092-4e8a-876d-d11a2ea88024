import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@/theme';

type Props = {
  isSelect: boolean;
  onPress?: () => void;
};
function RadioToggle(props: Props) {
  const { borders, layout, backgrounds } = useTheme();
  const { isSelect = false, onPress } = props;
  const customStyle = [
    isSelect ? borders.blue : borders.lightGray,
    borders.w_1,
    borders.rounded_10,
    layout.w_20,
    layout.h_20,
    layout.justifyCenter,
    layout.itemsCenter,
  ];
  return (
    <TouchableOpacity style={customStyle} onPress={onPress}>
      {isSelect ? (
        <View
          style={[
            backgrounds.blue,
            borders.rounded_8,
            layout.w_16,
            layout.h_16,
          ]}
        />
      ) : null}
    </TouchableOpacity>
  );
}

export default RadioToggle;
