import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import { PaymentHistoryItem } from '@/types/schemas/paymentHistory';

import { DateTime } from 'luxon';
import ClickbaleLinkButton from './ClickbaleLinkButton';

type Props = {
  onPress?: () => void;
  onEmailReceiptPress?: () => void;
  item: PaymentHistoryItem;
};
function PaymentHistoryCard(props: Props) {
  const { fonts, layout, backgrounds, borders, gutters } = useTheme();
  const { onPress, onEmailReceiptPress, item } = props;

  return (
    <TouchableOpacity
      style={[
        backgrounds.white,
        borders.rounded_16,
        layout.justifyBetween,
        layout.row,
        layout.itemsCenter,
        gutters.paddingHorizontal_16,
      ]}
      activeOpacity={1}
      onPress={onPress}
    >
      <View
        style={[
          layout.col,
          layout.flex_1,
          gutters.paddingVertical_16,
          gutters.gap_6,
        ]}
      >
        <Text style={[fonts.size_18, fonts.charcoal, fonts.appFontMedium]}>
          {DateTime.fromFormat(item.date, 'MM/dd/yyyy')
            .toFormat('MMM dd, yyyy')
            .toString()}
        </Text>
        <View style={[layout.row, layout.flex_1, gutters.gap_4]}>
          <Text style={[fonts.size_14, fonts.charcoal, fonts.appFontRegular]}>
            ${item?.amount ?? ''}
          </Text>
          <Text
            style={[fonts.size_14, fonts.lightCharcoal, fonts.appFontRegular]}
          >
            {item.card}
          </Text>
        </View>
      </View>
      <ClickbaleLinkButton
        title="Email Receipt"
        onPress={onEmailReceiptPress}
      />
    </TouchableOpacity>
  );
}

export default PaymentHistoryCard;
