import { WarningSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import React from 'react';
import { Text, View } from 'react-native';

function SSErrorView(props: { title?: string; description?: string }) {
  const { layout, gutters, backgrounds, borders, fonts } = useTheme();
  const { title, description } = props;

  return (
    <View
      style={[
        backgrounds.lightCherry,
        borders.rounded_12,
        gutters.paddingTop_12,
        gutters.paddingRight_16,
        gutters.paddingBottom_12,
        gutters.paddingLeft_16,
      ]}
    >
      <View style={[layout.row]}>
        <WarningSVG style={[layout.itemsCenter]} />
        <View
          style={[gutters.gap_8, gutters.marginLeft_8, gutters.marginRight_8]}
        >
          {title && (
            <Text style={[fonts.appFontBold, fonts.charcoal, fonts.size_16]}>
              {title}
            </Text>
          )}
          {description && (
            <Text style={[fonts.appFontRegular, fonts.charcoal, fonts.size_14]}>
              {description}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
}

export default SSErrorView;
