import React, { useState, useEffect } from 'react';
import {
  Alert,
  AppState,
  NativeEventEmitter,
  NativeModules,
  Platform,
  Switch,
  Text,
  View,
  NativeModule,
  Linking,
  ViewStyle,
} from 'react-native';

import {
  check,
  checkMultiple,
  //   checkNotifications,
  openSettings,
  PERMISSIONS,
  PermissionStatus,
  request,
  requestMultiple,
  RESULTS,
} from 'react-native-permissions';
import AndroidOpenSettings from 'react-native-android-open-settings';

import Biometrics from '@/utils/biometrics';
import { useTheme } from '@/theme';
import useStorage from '@/hooks/useStorage';

import BleManager, { BleState } from 'react-native-ble-manager';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
const BleManagerModule: NativeModule | undefined = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

interface PermissionTogglesProps {
  containerStyle?: ViewStyle | ViewStyle[];
  toggleContainerStyle?: ViewStyle | ViewStyle[];
}

function PermissionToggles({
  containerStyle,
  toggleContainerStyle,
}: PermissionTogglesProps) {
  const { layout, gutters, fonts, backgrounds } = useTheme();
  const globalStorage = useStorage('global-storage');

  const [isLocationToggleEnabled, setIsLocationToggleEnabled] = useState(false);
  const [isBioAvail, setIsBioAvail] = useState(false);
  const [bioEnabled, setBioEnabled] = useState(
    globalStorage.getBoolean('bioEnabled') || false,
  );
  const [locationStatus, setLocationStatus] = useState<PermissionStatus>(
    RESULTS.DENIED,
  );
  const [bluetoothStatus, setBluetoothStatus] = useState<PermissionStatus>(
    RESULTS.DENIED,
  );
  const [bluetoothEnabled, setBluetoothEnabled] = useState(false);
  //   const [notificationStatus, setNotificationStatus] =
  //     useState<PermissionStatus>(RESULTS.DENIED);

  const saveBioEnabled = async (value: boolean) => {
    console.log('bioEnabled', value);
    await logEventWithStatus(
      Events.permissionBiometrics,
      value ? EventStatus.enabled : EventStatus.disabled,
    );
    globalStorage.set('bioEnabled', value);
  };

  useEffect(() => {
    const queryBiometrics = async () => {
      const isBiometricsAvailable = await Biometrics.isSensorAvailable();
      setIsBioAvail(isBiometricsAvailable?.available);
    };
    void queryBiometrics();

    const queryBluetooth = async () => {
      const bleState: BleState = await BleManager.checkState();
      const isEnabled =
        bleState === BleState.On && bluetoothStatus === RESULTS.GRANTED;
      setBluetoothEnabled(isEnabled);
    };
    void queryBluetooth();
  }, [bluetoothStatus]);

  const checkBiometics = async () => {
    try {
      const { success, error } = await Biometrics.simplePrompt({
        promptMessage: 'Enable Biometrics',
        fallbackPromptMessage: 'Use Passcode',
        cancelButtonText: 'Log out',
      });

      if (success) {
        setBioEnabled(!bioEnabled);
        void saveBioEnabled(!bioEnabled);
      }
      if (error) {
        console.error('Biometrics failed:', error);
      }
    } catch (err) {
      console.error('Biometrics prompt failure:', err);
      setBioEnabled(bioEnabled);
      void saveBioEnabled(bioEnabled);
    }
  };

  useEffect(() => {
    const logEvent = async () => {
      console.log('bluetoothEnabled', bluetoothEnabled);
      await logEventWithStatus(
        Events.permissionLocation,
        bluetoothEnabled ? EventStatus.enabled : EventStatus.disabled,
      );
    };

    void logEvent();
  }, [bluetoothEnabled]);

  const onBioToggle = () => {
    if (!isBioAvail) {
      Alert.alert(
        'Biometrics Not Enabled',
        'This device does not support biometrics.',
      );
    }
    void checkBiometics();
  };

  useEffect(() => {
    const listener = bleManagerEmitter.addListener(
      'BleManagerDidUpdateState',
      (args: { state: string }) => {
        const isOn =
          args?.state === 'on' && bluetoothStatus === RESULTS.GRANTED;
        setBluetoothEnabled(isOn);
      },
    );

    return () => {
      listener.remove();
    };
  }, [bluetoothStatus]);

  const enableBluetooth = () => {
    if (!bluetoothEnabled) {
      try {
        BleManager.enableBluetooth()
          .then(() => {
            setBluetoothEnabled(true);
          })
          .catch(error => {
            console.error('Bluetooth error', error);
          });
      } catch (error) {
        console.error('[enableBluetooth] thrown', error);
      }
    } else {
      AndroidOpenSettings.bluetoothSettings();
    }
  };

  const requestAndroidPermissions = async () => {
    const permission = Platform.select({
      android: [
        PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
      ],
    });
    if (!permission) return;
    const result = await requestMultiple(permission);
    let status: PermissionStatus = RESULTS.DENIED;
    if (
      result['android.permission.BLUETOOTH_CONNECT'] === RESULTS.GRANTED &&
      result['android.permission.BLUETOOTH_SCAN'] === RESULTS.GRANTED
    ) {
      void enableBluetooth();
      status = RESULTS.GRANTED;
    } else if (
      result['android.permission.BLUETOOTH_CONNECT'] === RESULTS.BLOCKED ||
      result['android.permission.BLUETOOTH_SCAN'] === RESULTS.BLOCKED
    ) {
      Alert.alert(
        'Permission blocked',
        'Please allow to permission from settings',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          { text: 'Go to settings', onPress: () => void openSettings() },
        ],
      );
      status = result['android.permission.BLUETOOTH_CONNECT'];
    }
    setBluetoothStatus(status);
  };

  const goToBluetoothSettings = async () => {
    if (await Linking.canOpenURL('App-Prefs:Bluetooth')) {
      void Linking.openURL('App-Prefs:Bluetooth');
    }
  };

  // eslint-disable-next-line @typescript-eslint/require-await
  const onBluetoothToggle = async () => {
    if (Platform.OS === 'android') {
      if (bluetoothStatus === RESULTS.GRANTED) {
        void enableBluetooth();
      } else {
        void requestAndroidPermissions();
      }
    } else if (bluetoothStatus === RESULTS.GRANTED) {
      if (bluetoothEnabled) {
        void goToBluetoothSettings();
      } else {
        Alert.alert(
          'Enable Bluetooth!',
          'Please make sure Bluetooth is enabled from both settings and control center',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Go to settings',
              onPress: () => void goToBluetoothSettings(),
            },
          ],
        );
      }
    } else {
      void openSettings();
    }
  };

  //   const checkNotificationStatus = async () => {
  //     const { status } = await checkNotifications();
  //     setNotificationStatus(status);
  //   };

  const checkLocationStatus = async () => {
    const permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });
    if (!permission) return;
    const locationResult = await check(permission);
    setLocationStatus(locationResult);
    setIsLocationToggleEnabled(false);
  };

  const checkBluetoothStatus = async () => {
    const permission = Platform.select({
      ios: [PERMISSIONS.IOS.BLUETOOTH],
      android: [
        PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
      ],
    });
    if (!permission) return;
    const bluetoothResult = await checkMultiple(permission);
    let status: PermissionStatus = RESULTS.DENIED;
    if (Platform.OS === 'ios') {
      status = bluetoothResult['ios.permission.BLUETOOTH'];
    } else if (
      bluetoothResult['android.permission.BLUETOOTH_CONNECT'] ===
        RESULTS.GRANTED &&
      bluetoothResult['android.permission.BLUETOOTH_SCAN'] === RESULTS.GRANTED
    ) {
      status = RESULTS.GRANTED;
    } else {
      status = bluetoothResult['android.permission.BLUETOOTH_CONNECT'];
    }
    setBluetoothStatus(status);
  };

  const checkAppPermissions = () => {
    void checkLocationStatus();
    // void checkNotificationStatus();
    void checkBluetoothStatus();
  };

  useEffect(() => {
    checkAppPermissions();
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        checkAppPermissions();
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const onLocationToggle = async (value: boolean) => {
    setIsLocationToggleEnabled(true);
    const permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_ALWAYS,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });
    if (value && permission && locationStatus === RESULTS.DENIED) {
      request(permission)
        .then(async locationResult => {
          await logEventWithStatus(
            Events.permissionLocation,
            value ? EventStatus.enabled : EventStatus.disabled,
          );
          setLocationStatus(locationResult);
        })
        .catch(error => {
          setIsLocationToggleEnabled(false);
          console.error(error);
        });
    } else {
      setIsLocationToggleEnabled(false);
      void openSettings();
    }
  };

  // const onNotificationToggle = (value: boolean) => {
  //   if (value && notificationStatus === RESULTS.DENIED) {
  //     requestNotifications(['alert', 'sound'])
  //       .then(({ status }) => {
  //         setNotificationStatus(status);
  //       })
  //       .catch(error => console.error(error));
  //   } else {
  //     void openSettings();
  //   }
  // };

  const renderSettingToggle = (params: {
    name: string;
    description?: string;
    enabled: boolean;
    accessibilityLabelForSwitch?: string;
    onToggle: (value: boolean) => void;
  }) => {
    const {
      name,
      description,
      enabled,
      onToggle,
      accessibilityLabelForSwitch,
    } = params;
    return (
      <View
        style={[
          layout.row,
          layout.justifyBetween,
          layout.itemsCenter,
          toggleContainerStyle,
        ]}
      >
        <View style={[layout.flex_1, layout.col, gutters.gap_8]}>
          <Text style={[fonts.appFontMedium, fonts.size_18, fonts.charcoal]}>
            {name}
          </Text>
          {!!description && (
            <Text
              style={[fonts.appFontRegular, fonts.size_14, fonts.lightCharcoal]}
            >
              {description}
            </Text>
          )}
        </View>
        <Switch
          trackColor={{
            true: backgrounds.limeGreen.backgroundColor,
            false: backgrounds.lightGray.backgroundColor,
          }}
          thumbColor={backgrounds.white.backgroundColor}
          ios_backgroundColor={backgrounds.lightGray.backgroundColor}
          accessibilityLabel={accessibilityLabelForSwitch}
          onValueChange={onToggle}
          value={enabled}
        />
      </View>
    );
  };

  return (
    <View style={[layout.col, gutters.gap_16, containerStyle]}>
      {/* {renderSettingToggle({
          name: 'Notifications',
          description: 'Allow SmartStop to send you notifications.',
          enabled:
            notificationStatus === RESULTS.GRANTED ||
            notificationStatus === RESULTS.LIMITED,
          onToggle: onNotificationToggle,
        })} */}
      {renderSettingToggle({
        name: 'Use Locations',
        accessibilityLabelForSwitch: 'Location Switch',
        description: '“Always” is recommended.',
        enabled: isLocationToggleEnabled
          ? true
          : locationStatus === RESULTS.GRANTED ||
            locationStatus === RESULTS.LIMITED,
        onToggle: onLocationToggle,
      })}
      {renderSettingToggle({
        name: 'Bluetooth',
        description: 'Required for gate access',
        accessibilityLabelForSwitch: 'Bluetooth Switch',
        enabled: bluetoothEnabled,
        onToggle: () => void onBluetoothToggle(),
      })}

      {renderSettingToggle({
        name: 'Biometrics',
        description: 'Enable biometrics for quick access to your account',
        accessibilityLabelForSwitch: 'Biometrics Switch',
        enabled: bioEnabled,
        onToggle: onBioToggle,
      })}
    </View>
  );
}

export default PermissionToggles;
