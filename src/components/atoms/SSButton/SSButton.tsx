import React from 'react';
import { useTheme } from '@/theme';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  Text,
  View,
} from 'react-native';
import { CheckSVG } from '@/assets/svg';
import LottieView from 'lottie-react-native';

type SSButtonProps = TouchableOpacityProps & {
  title: string;
  bgColorStyle?: { backgroundColor: string };
  fontColorStyle?: { color: string };
  variant?: 'dark' | 'light' | 'transparent';
  isSuccess?: boolean;
  isProgress?: boolean;
  disabled?: boolean;
  customBorderColorStyle?: { borderColor: string };
  fontSizeStyle?: { fontSize: number };
  widthStyle?: { width: number };
  heightStyle?: { height: number };
  borderRadiusStyle?: { borderRadius: number };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  customIcon?: any;
};

function SSButton({
  title,
  bgColorStyle,
  fontColorStyle,
  variant,
  isSuccess,
  isProgress,
  disabled,
  customBorderColorStyle,
  customIcon,
  fontSizeStyle,
  widthStyle,
  heightStyle,
  borderRadiusStyle,
  ...pressableProps
}: SSButtonProps) {
  const { layout, backgrounds, fonts, borders, gutters } = useTheme();

  const getVariantBackgroundColor = () => {
    switch (variant) {
      case 'dark':
        return backgrounds.bluePrimary;
      case 'transparent':
        return backgrounds.transparent;
      case 'light':
        return backgrounds.lightGray;
      default:
        return backgrounds.transparent;
    }
  };

  const getVariantFontColor = () => {
    switch (variant) {
      case 'dark':
        return fonts.white;
      case 'transparent':
        return fonts.bluePrimary;
      case 'light':
        return fonts.midCharcoal;
      default:
        return fonts.bluePrimary;
    }
  };

  const getVariantBorderColor = () => {
    switch (variant) {
      case 'dark':
        return borders.bluePrimary;
      case 'transparent':
        return borders.bluePrimary;
      case 'light':
        return borders.lightGray;
      default:
        return borders.bluePrimary;
    }
  };

  let bgColor = variant ? getVariantBackgroundColor() : bgColorStyle;
  let bordersColor: string =
    customBorderColorStyle?.borderColor || getVariantBorderColor().borderColor;
  if (isProgress) {
    bgColor = backgrounds.bluePrimary;
    bordersColor = borders.bluePrimary.borderColor;
  }
  if (isSuccess) {
    bgColor = backgrounds.olive;
    bordersColor = borders.olive.borderColor;
  }
  const fontColor = fontColorStyle || getVariantFontColor();

  const width = widthStyle || layout.fullWidth;
  const height = heightStyle || layout.h_50;
  const borderRadius = borderRadiusStyle || borders.rounded_25;

  const isDisabled = variant === 'light' || disabled;

  return (
    <TouchableOpacity
      style={[
        layout.itemsCenter,
        layout.justifyCenter,
        borderRadius,
        borders.w_1,
        { borderColor: bordersColor },
        width,
        height,
        bgColor || backgrounds.gray100,
        isProgress ? gutters.padding_5 : undefined,
      ]}
      disabled={isProgress || isDisabled}
      {...pressableProps}
    >
      {isProgress ? (
        <LottieView
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          source={require('../../../assets/loader.json')}
          autoPlay
          resizeMode="cover"
          style={[layout.h_35, layout.w_100, gutters.padding_0]}
          loop
        />
      ) : null}
      {isSuccess ? <CheckSVG /> : null}
      {!isProgress && !isSuccess ? (
        <View style={[layout.row, layout.justifyCenter]}>
          <Text
            style={[
              fontSizeStyle || fonts.size_16,
              fontColor,
              fonts.appFontBold,
              { letterSpacing: 1.2 },
            ]}
          >
            {title?.toUpperCase()}
          </Text>
          {customIcon ? (
            <View style={[layout.justifyCenter, gutters.paddingLeft_10]}>
              {customIcon}
            </View>
          ) : null}
        </View>
      ) : null}
    </TouchableOpacity>
  );
}

export default SSButton;
