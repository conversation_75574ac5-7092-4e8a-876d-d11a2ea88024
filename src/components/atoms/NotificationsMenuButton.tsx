import { BellSVG } from '@/assets/svg';
import { HomeStackParamList } from '@/types/navigation';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React from 'react';
import { Pressable } from 'react-native';

function NotificationsMenuButton(props: { badge?: boolean; color?: string }) {
  const { badge, color } = props;
  const navigation: NativeStackNavigationProp<HomeStackParamList> =
    useNavigation();
  return (
    <Pressable
      accessibilityLabel="Notifications"
      onPress={() => navigation.navigate('MainNotifications')}
    >
      <BellSVG badge={badge} color={color} />
    </Pressable>
  );
}

export default NotificationsMenuButton;
