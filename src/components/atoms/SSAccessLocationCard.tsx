import useTheme from '@/theme/hooks/useTheme';
import React from 'react';
import { View, Text, Image, Platform } from 'react-native';
import { KeyOuterGraySVG, TimerOuterGraySVG } from '@/assets/svg';
import { Location } from '@/types/schemas/location';
import { fixedFontSize } from '@/utils/commonFunctions';

type SSAccessLocationCardProps = {
  location: Location;
  accessCode: string | undefined;
};

function SSAccessLocationCard(props: SSAccessLocationCardProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { location, accessCode } = props;
  const { day, hours } = location.todaysGateHours;
  return (
    <View
      style={[
        layout.flex_1,
        backgrounds.white,
        borders.rounded_16,
        { overflow: 'hidden' },
      ]}
    >
      <View
        style={[layout.h_16, layout.fullWidth, backgrounds.springGrass40]}
      />
      <View
        style={[
          layout.row,
          gutters.marginHorizontal_16,
          gutters.marginVertical_26,
        ]}
      >
        <Image
          style={[layout.w_72, layout.h_72, borders.rounded_8]}
          source={{ uri: location.image.url }}
        />

        <View
          style={[gutters.paddingLeft_16, layout.flex_1, layout.justifyCenter]}
        >
          <Text style={[fonts.charcoal, fonts.appFontBold, fonts.size_20]}>
            {location.address}
          </Text>
          <Text style={[fonts.charcoal, fonts.appFontBold, fonts.size_20]}>
            {location.city}, {location.state} {location.zip}
          </Text>
        </View>
      </View>
      <View
        style={[
          layout.row,
          layout.justifyBetween,
          gutters.marginRight_12,
          gutters.marginBottom_20,
        ]}
      >
        <View style={[layout.row, layout.flex_1, gutters.marginHorizontal_16]}>
          <TimerOuterGraySVG />
          <View style={[gutters.paddingLeft_10, layout.justifyCenter]}>
            <Text
              style={[fonts.lightCharcoal, fonts.appFontBold, Platform.OS === 'ios' ? fonts.size_14 : { fontSize: fixedFontSize(14) }]}
              allowFontScaling={false}
            >
              GATE HOURS
            </Text>
            <Text
              style={[
                gutters.paddingTop_5,
                fonts.charcoal,
                fonts.appFontRegular,
                Platform.OS === 'ios' ? fonts.size_14 : { fontSize: fixedFontSize(14) }]}
              allowFontScaling={false}
            >
              {day}
            </Text>
            <Text style={[fonts.charcoal, fonts.appFontRegular, Platform.OS === 'ios' ? fonts.size_14 : { fontSize: fixedFontSize(14) }]}
              allowFontScaling={false}
            >

              {hours}
            </Text>
          </View>
        </View>
        <View style={[layout.row, layout.flex_1, gutters.marginHorizontal_16]}>
          <KeyOuterGraySVG />
          <View style={[gutters.paddingLeft_10]}>
            <Text
              style={[fonts.lightCharcoal, fonts.appFontBold,
              Platform.OS === 'ios' ? fonts.size_14 : { fontSize: fixedFontSize(14) }]}
              allowFontScaling={false}
            >
              ACCESS CODE
            </Text>
            <Text
              style={[
                gutters.paddingTop_5,
                fonts.charcoal,
                fonts.appFontRegular,
                Platform.OS === 'ios' ? fonts.size_14 : { fontSize: fixedFontSize(14) }]}
              allowFontScaling={false}
            >
              {accessCode || ''}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

export default SSAccessLocationCard;
