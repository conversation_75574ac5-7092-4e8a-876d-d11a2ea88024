import React from 'react';
import { Modal, View, ActivityIndicator } from 'react-native';
import { useTheme } from '@/theme';

type Props = {
  isLoading: boolean;
};
function ActivityLoader(props: Props) {
  const { layout, backgrounds, borders } = useTheme();
  const { isLoading } = props;
  return (
    <Modal
      transparent
      animationType="none"
      visible={isLoading}
      onRequestClose={() => null}
    >
      <View
        style={[
          layout.flex_1,
          layout.itemsCenter,
          layout.justifyCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <View
          style={[
            backgrounds.white,
            borders.rounded_12,
            layout.w_100,
            layout.h_100,
            layout.itemsCenter,
            layout.justifyCenter,
          ]}
        >
          <ActivityIndicator size="large" color={borders.blue.borderColor} />
        </View>
      </View>
    </Modal>
  );
}

export default ActivityLoader;
