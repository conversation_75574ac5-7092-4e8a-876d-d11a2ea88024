import React from 'react';
import { TouchableOpacity, Text, TextStyle } from 'react-native';
import { useTheme } from '@/theme';

type Props = {
  title: string;
  fontSize?: number | undefined;
  color?: string;
  fontFamily?: string;
  isUnderline?: boolean;
  onPress?: () => void;
};
function ClickbaleLinkButton(props: Props) {
  const { fonts } = useTheme();
  const {
    fontSize = fonts.size_16.fontSize,
    title,
    color = fonts.primary.color,
    fontFamily = fonts.appFontBold.fontFamily,
    isUnderline = true,
    onPress,
  } = props;
  const customStyle: TextStyle = {
    fontSize,
    color,
    fontFamily,
    textDecorationLine: isUnderline ? 'underline' : undefined,
  };
  return (
    <TouchableOpacity onPress={onPress}>
      <Text style={customStyle}>{title}</Text>
    </TouchableOpacity>
  );
}

export default ClickbaleLinkButton;
