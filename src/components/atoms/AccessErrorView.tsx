import React from 'react';
import { Text, View } from 'react-native';
import { useTheme } from '@/theme';
import {
  AccessSystemUnavailable,
  AlertFailed,
  BluetoothDisabled,
  DigitalAccessUnavailable,
  InsufficientPermissions,
  NoInternetConnection,
  OutOfRange,
} from '@/assets/svg';
import SSButton from './SSButton/SSButton';

export enum ErrorType {
  digitalAccessUnavailable = 'digitalAccessUnavailable',
  outOfRange = 'outOfRange',
  accessSystemUnavailable = 'accessSystemUnavailable',
  noInternetConnection = 'noInternetConnection',
  bluetoothDisabled = 'bluetoothDisabled',
  insufficientAppPermissions = 'insufficientAppPermissions',
  somethingWentWrong = 'somethingWentWrong',
  accessCodeUnavailable = 'accessCodeUnavailable'
}

export enum ErrorTitle {
  digitalAccessUnavailable = 'Digital access is currently unavailable at this location',
  outOfRange = 'Out of Range',
  accessSystemUnavailable = 'Access System Unavilable',
  noInternetConnection = 'No internet connection',
  bluetoothDisabled = 'Bluetooth disabled',
  insufficientAppPermissions = 'Insufficient App Permissions',
  somethingWentWrong = 'Something Went Wrong',
  accessCodeUnavailable = 'Access Code\nIssued at Property'
}

export enum ErrorDiscription {
  digitalAccessUnavailable = '',
  outOfRange = 'You\'re too far away. Please move closer for access.',
  accessSystemUnavailable = 'Our access system is currently unavailable. Please try again later.',
  noInternetConnection = 'Internet connection needed. Please connect to the internet and try again.',
  bluetoothDisabled = 'Bluetooth connection needed. Please connect to the bluetooth and try again.',
  insufficientAppPermissions = 'Permissions needed. Please update your app settings for access.',
  somethingWentWrong = 'We encountered an unexpected error. Please try again later.',
  accessCodeUnavailable = 'Your storage unit is now ready for you. Please bring your government issued photo ID when you check-in to access your unit during our regular office hours.'
}

type Props = {
  error: string;
  onModifyPermissionsAction?: () => void;
};
function AccessErrorView(props: Props) {
  const { fonts, layout, gutters } = useTheme();
  const { error, onModifyPermissionsAction } = props;

  const getErrorIcon = () => {
    switch (error) {
      case ErrorType.digitalAccessUnavailable.toString():
        return <DigitalAccessUnavailable />;
      case ErrorType.outOfRange.toString():
        return <OutOfRange />;
      case ErrorType.accessSystemUnavailable.toString():
        return <AccessSystemUnavailable />;
      case ErrorType.bluetoothDisabled.toString():
        return <BluetoothDisabled />;
      case ErrorType.noInternetConnection.toString():
        return <NoInternetConnection />;
      case ErrorType.insufficientAppPermissions.toString():
        return <InsufficientPermissions />;
      case ErrorType.somethingWentWrong.toString():
        return <AccessSystemUnavailable />;
      case ErrorType.accessCodeUnavailable.toString():
        return <AlertFailed />
      default:
        return '';
    }
  };

  const getErrorTitle = () => {
    switch (error) {
      case ErrorType.digitalAccessUnavailable.toString():
        return ErrorTitle.digitalAccessUnavailable;
      case ErrorType.outOfRange.toString():
        return ErrorTitle.outOfRange;
      case ErrorType.accessSystemUnavailable.toString():
        return ErrorTitle.accessSystemUnavailable;
      case ErrorType.bluetoothDisabled.toString():
        return ErrorTitle.bluetoothDisabled;
      case ErrorType.noInternetConnection.toString():
        return ErrorTitle.noInternetConnection;
      case ErrorType.insufficientAppPermissions.toString():
        return ErrorTitle.insufficientAppPermissions;
      case ErrorType.somethingWentWrong.toString():
        return ErrorTitle.somethingWentWrong;
      case ErrorType.accessCodeUnavailable.toString():
        return ErrorTitle.accessCodeUnavailable;
      default:
        return '';
    }
  };

  const getErrorDiscription = () => {
    switch (error) {
      case ErrorType.digitalAccessUnavailable.toString():
        return ErrorDiscription.digitalAccessUnavailable;
      case ErrorType.outOfRange.toString():
        return ErrorDiscription.outOfRange;
      case ErrorType.accessSystemUnavailable.toString():
        return ErrorDiscription.accessSystemUnavailable;
      case ErrorType.bluetoothDisabled.toString():
        return ErrorDiscription.bluetoothDisabled;
      case ErrorType.noInternetConnection.toString():
        return ErrorDiscription.noInternetConnection;
      case ErrorType.insufficientAppPermissions.toString():
        return ErrorDiscription.insufficientAppPermissions;
      case ErrorType.somethingWentWrong.toString():
        return ErrorDiscription.somethingWentWrong;
      case ErrorType.accessCodeUnavailable.toString():
        return ErrorDiscription.accessCodeUnavailable;
      default:
        return '';
    }
  };

  const managePermissionButton = () => {
    return error === ErrorType.insufficientAppPermissions.toString() ? (
      <View
        style={[
          gutters.marginTop_26,
          layout.fullWidth,
          gutters.paddingHorizontal_40,
        ]}
      >
        <SSButton
          title="Modify permissions"
          variant="transparent"
          onPress={onModifyPermissionsAction}
        />
      </View>
    ) : null;
  };

  return (
    <View
      style={[
        gutters.marginTop_26,
        layout.flex_1,
        layout.fullHeight,
        layout.fullWidth,
        layout.itemsCenter,
        layout.selfCenter,
        layout.justifyCenter,
      ]}
    >
      <View
        style={[layout.itemsCenter, layout.justifySpaceEvenly, gutters.gap_12]}
      >
        {getErrorIcon()}
        <Text
          style={[
            fonts.appFontMedium,
            error === ErrorType.accessCodeUnavailable.toString()
              ? fonts.size_26
              : fonts.size_20,
            error === ErrorType.accessCodeUnavailable.toString()
              ? fonts.charcoal
              : fonts.primary,
            gutters.paddingBottom_14,
            gutters.marginHorizontal_20,
            fonts.alignCenter,
            error === ErrorType.accessCodeUnavailable.toString()
              ? fonts.bold
              : fonts.normal,
          ]}
        >
          {getErrorTitle()}
        </Text>
      </View>
      <Text
        style={[
          fonts.appFontRegular,
          fonts.size_14,
          fonts.charcoal,
          fonts.alignCenter,
          gutters.paddingBottom_14,
          gutters.marginHorizontal_40,
        ]}
      >
        {getErrorDiscription()}
      </Text>
      {managePermissionButton()}
    </View>
  );
}

export default AccessErrorView;
