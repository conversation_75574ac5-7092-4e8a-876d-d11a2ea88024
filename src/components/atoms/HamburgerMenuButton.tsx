import { Pressable } from 'react-native';

import { HamburgerMenuSVG } from '@/assets/svg';
import { MainDrawerScreenProps } from '@/types/navigation';
import React from 'react';

interface HamburgerMenuButtonProps {
  color?: string;
}

function HamburgerMenuButton({
  navigation,
  color,
}: MainDrawerScreenProps & HamburgerMenuButtonProps) {
  return (
    <Pressable
      accessibilityLabel="Hamburger Menu"
      onPress={() => navigation.openDrawer()}
    >
      <HamburgerMenuSVG color={color} />
    </Pressable>
  );
}

export default HamburgerMenuButton;
