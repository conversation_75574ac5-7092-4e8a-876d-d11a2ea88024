import React, { useState } from 'react';
import {
  View,
  Text,
  TextStyle,
  ViewStyle,
  LayoutChangeEvent,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Keyboard,
  ScrollView,
} from 'react-native';
import { DownArrowSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import { opacity } from 'react-native-reanimated/lib/typescript/reanimated2/Colors';

type Item = {
  label: string;
  value: string;
};

type ListItem = {
  item: Item;
};

export interface Props {
  /** Style to the container of whole component */
  containerStyles?: ViewStyle;
  /** Value for the label, same as placeholder */
  label: React.ReactNode;
  /** Prop for show error view. */
  isError?: boolean;
  value?: string;
  data: Array<Item>;
  onSelect: (item: Item) => void;
  disabled?: boolean;
}

const SSDropdownInput = React.forwardRef<TextStyle, Props>(
  ({
    label,
    containerStyles: oldContainerStyles,
    value = '',
    isError = false,
    data,
    onSelect,
    disabled,
  }) => {
    const [halfTop, setHalfTop] = useState(0);
    const [visible, setVisible] = useState(false);

    const { layout, fonts, gutters, borders, backgrounds } = useTheme();

    const toggleDropdown = (): void => {
      Keyboard.dismiss();
      setVisible(!visible);
    };

    const onItemPress = (item: Item): void => {
      onSelect(item);
      toggleDropdown();
    };

    let containerStyles: ViewStyle | undefined = oldContainerStyles;
    let bordersColor: string = borders.lightGray.borderColor;
    if (isError) {
      bordersColor = borders.cherry.borderColor;
    }

    containerStyles = StyleSheet.flatten([
      layout.row,
      borders.w_1,
      borders.rounded_12,
      gutters.paddingHorizontal_12,
      gutters.paddingTop_10,
      gutters.paddingBottom_10,
      layout.alignContentCenter,
      layout.justifyCenter,
      layout.itemsCenter,
      layout.row,
      layout.flex_1,
      layout.h_60,
      { borderColor: bordersColor },
    ]);

    const onLayout = (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      setHalfTop(height / 2);
    };

    const renderItem = ({ item }: ListItem) => (
      <TouchableOpacity
        style={[gutters.paddingHorizontal_10, gutters.paddingVertical_10]}
        onPress={() => onItemPress(item)}
      >
        <Text style={[fonts.size_16, fonts.charcoal, fonts.appFontRegular]}>
          {item.label}
        </Text>
      </TouchableOpacity>
    );

    const renderDropdown = () => {
      const MIN_HEIGHT = 110;
      const MAX_HEIGHT = 210;
      const ITEM_HEIGHT = 60;
      const OFFSET = 30;

      const calculatedHeight = ITEM_HEIGHT * (data?.length ?? 0) + OFFSET;
      const normalizedHeight = Math.min(Math.max(MIN_HEIGHT, calculatedHeight), MAX_HEIGHT);

      return (
        <View style={[layout.justifyCenter, layout.fullWidth]}>
          <TouchableOpacity
            style={[layout.fullWidth, { height: normalizedHeight }]}
            onPress={toggleDropdown}
            disabled={disabled}
          >
            <View
              style={[
                layout.absolute,
                backgrounds.white,
                layout.fullWidth,
                borders.rounded_12,
                borders.w_1,
                borders.bluePrimary,
                {
                  shadowRadius: 4,
                  shadowOffset: { height: 4, width: 0 },
                  shadowOpacity: 0.5,
                },
              ]}
            >
              <View
                style={[
                  layout.flex_1,
                  layout.row,
                  borders.blueLight,
                  gutters.paddingHorizontal_12,
                  layout.alignContentCenter,
                  layout.justifyCenter,
                  layout.h_60,
                  borders.bottom_1,
                ]}
              >
                <View style={[layout.flex_1, layout.row]}>
                  <Text
                    style={[
                      layout.selfCenter,
                      layout.flex_1,
                      fonts.size_16,
                      fonts.charcol,
                      fonts.appFontMedium,
                    ]}
                  >
                    {value?.length > 0 ? value : label}
                  </Text>
                  <View style={[layout.justifyCenter]}>
                    <DownArrowSVG />
                  </View>
                </View>
              </View>
              <ScrollView horizontal contentContainerStyle={[layout.flexGrow_1]}>
                <FlatList
                  nestedScrollEnabled
                  style={[
                    gutters.paddingHorizontal_8,
                    gutters.paddingBottom_10,
                    layout.fullWidth,
                    { maxHeight: 150 },
                  ]}
                  data={data}
                  renderItem={item => renderItem(item)}
                  keyExtractor={(item, index) => index.toString()}
                />
              </ScrollView>
            </View>
          </TouchableOpacity>
        </View>
      );
    };

    const renderDropDownContainer = () => (
      <View style={[layout.row]}>
        <View style={containerStyles}>
          <TouchableOpacity
            style={[layout.flex_1, layout.row]}
            onPress={toggleDropdown}
            disabled={disabled}
          >
            <Text
              style={[
                layout.selfCenter,
                layout.absolute,
                layout.flex_1,
                value?.length ? fonts.size_12 : fonts.size_16,
                fonts.lightCharcoal,
                fonts.appFontRegular,
                {
                  height: value?.length ? halfTop + 12 : undefined,
                  opacity: disabled ? 0.5 : undefined
                },
              ]}
            >
              {label}
            </Text>
            <TextInput
              pointerEvents="none"
              value={value}
              editable={false}
              style={[
                fonts.size_16,
                fonts.charcoal,
                gutters.marginTop_10,
                gutters.paddingTop_0,
                gutters.paddingBottom_0,
                layout.flex_1,
                layout.z10,
                fonts.appFontMedium,
                {
                  minHeight: 28,
                },
              ]}
            />
            <View style={[layout.justifyCenter]}>
              <DownArrowSVG
                color={disabled ? borders.lightGray.borderColor : undefined} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );

    return (
      <TouchableOpacity
        style={[backgrounds.white]}
        onPress={toggleDropdown}
        onLayout={onLayout}
        disabled={disabled}
      >
        {visible ? renderDropdown() : renderDropDownContainer()}
      </TouchableOpacity>
    );
  },
);

export default SSDropdownInput;
