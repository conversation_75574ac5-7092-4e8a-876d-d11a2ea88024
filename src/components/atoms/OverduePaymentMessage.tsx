// OverduePaymentMessage.tsx
import React from 'react';
import { Text } from 'react-native';
import useTheme from '@/theme/hooks/useTheme';

interface OverduePaymentMessageProps {
  reason: string;
}

function OverduePaymentMessage(props: OverduePaymentMessageProps) {
  const { reason } = props;
  const { fonts } = useTheme();
  return <Text style={[fonts.appFontRegular]}>{reason}</Text>;
}
export default OverduePaymentMessage;
