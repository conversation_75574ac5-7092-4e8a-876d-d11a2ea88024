import { UpArrowSVG, DownArrowSVG, CardSVG } from '@/assets/svg';
import { Unit } from '@/types/schemas/unit';
import React, { useState } from 'react';
import {
  Pressable,
  View,
  Text,
  Switch,
  LayoutAnimation,
  Platform,
} from 'react-native';
import useTheme from '@/theme/hooks/useTheme';
import OverduePaymentMessage from '@/components/atoms/OverduePaymentMessage';

import { fixedFontSize } from '@/utils/commonFunctions';
import SSButton from './SSButton/SSButton';

type UnitHeaderProps = {
  toggleVisibility?: () => void;
  isVisible: boolean;
  isDropDownVisible?: boolean;
  unitNumber: string;
};

type PaymentInfoProps = {
  unit: Unit;
  isToggleVisible?: boolean | undefined;
  onToggleChange?: (isToggle: boolean) => void | undefined;
  onToggleAutoPayCallBack: () => void;
  onPress: () => void;
  isToggled?: boolean | undefined;
};

type UnitDetailsProps = {
  unit: Unit;
  autoPay?: boolean | undefined;
  onToggleAutoPay?: () => void;
  onPress: () => void;
};

function UnitHeader(props: UnitHeaderProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { toggleVisibility, isVisible, isDropDownVisible, unitNumber } = props;

  return (
    <Pressable onPress={() => toggleVisibility?.()}>
      <View
        style={[
          borders.gray100,
          isVisible ? backgrounds.springGrass40 : backgrounds.white,
          {
            borderTopLeftRadius: 16,
            borderBottomLeftRadius: isVisible ? 0 : 16,
            borderBottomRightRadius: isVisible ? 0 : 16,
            borderTopRightRadius: 16,
            overflow: 'hidden',
          },
        ]}
      >
        <View style={[backgrounds.springGrass40, layout.h_10]} />
        <View
          style={[
            layout.row,
            gutters.padding_4,
            layout.flex_1,
            layout.justifyBetween,
          ]}
        >
          <Text
            style={[
              gutters.padding_8,
              fonts.uppercase,
              fonts.size_14,
              fonts.appFontBold,
              fonts.charcoal,
            ]}
          >
            Unit No.{' '}
            <Text style={[fonts.size_14, fonts.appFontRegular, fonts.charcoal]}>
              {unitNumber}
            </Text>
          </Text>
          {isDropDownVisible && (
            <View style={[layout.selfCenter, gutters.paddingRight_16]}>
              {isVisible ? (
                <UpArrowSVG color={backgrounds.blue.backgroundColor} />
              ) : (
                <DownArrowSVG />
              )}
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
}

function UnitDetails(props: UnitDetailsProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { unit, autoPay, onToggleAutoPay, onPress } = props;
  const formattedPayment = parseFloat(unit.nextPayment).toFixed(2);

  return (
    <View>
      <View
        style={[
          layout.row,
          layout.flex_1,
          layout.justifyBetween,
          gutters.paddingTop_24,
          gutters.paddingRight_16,
          gutters.paddingBottom_8,
          gutters.paddingLeft_16,
          gutters.gap_32,
        ]}
      >
        <View style={[gutters.gap_4]}>
          <Text
            style={[
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
              fonts.appFontBold,
              fonts.lightCharcoal,
            ]}
            allowFontScaling={false}
          >
            Outstanding Balance
          </Text>
          <Text
            style={[
              fonts.uppercase,
              Platform.OS === 'ios'
                ? fonts.size_32
                : { fontSize: fixedFontSize(32) },
              fonts.appFontBold,
              fonts.charcoal,
            ]}
            allowFontScaling={false}
          >
            ${formattedPayment ?? ''}
          </Text>
        </View>
        <View style={[gutters.gap_4, layout.flex_1, layout.itemsEnd]}>
          <Text
            style={[
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
              fonts.appFontBold,
              fonts.lightCharcoal,
            ]}
            allowFontScaling={false}
          >
            Due Date
          </Text>
          <Text
            style={[
              fonts.uppercase,
              Platform.OS === 'ios'
                ? fonts.size_14
                : { fontSize: fixedFontSize(14) },
              fonts.appFontRegular,
              fonts.charcoal,
            ]}
            allowFontScaling={false}
          >
            {unit.nextPaymentDue}
          </Text>
        </View>
      </View>
      {!unit.autopay && !unit.canMakePayment && (
        <View style={[gutters.padding_16, gutters.gap_16]}>
          <OverduePaymentMessage reason={unit.noPaymentReason} />
        </View>
      )}
      {!unit.autopay && unit.canMakePayment && (
        <View style={[gutters.paddingHorizontal_16, gutters.gap_16]}>
          <SSButton
            title="Make ONE-TIME PAYMENT"
            customBorderColorStyle={borders.olive}
            bgColorStyle={backgrounds.olive}
            fontColorStyle={fonts.white}
            onPress={onPress}
          />
        </View>
      )}
      <View style={[gutters.padding_16, gutters.gap_10]}>
        <View style={[layout.row, layout.flex_1, layout.justifyBetween]}>
          <View style={[layout.row, layout.flex_1, layout.itemsCenter]}>
            <CardSVG />
            <Text
              style={[
                fonts.size_18,
                fonts.appFontBold,
                fonts.charcoal,
                gutters.marginLeft_10,
              ]}
            >
              SmartPay
            </Text>
          </View>
          <Switch
            accessibilityLabel={unit.autopay ? 'SmartPay On' : 'SmartPay Off'}
            onValueChange={onToggleAutoPay}
            value={autoPay}
          />
        </View>
        <Text style={[fonts.size_14, fonts.appFontRegular, fonts.charcoal]}>
          Toggle to enroll in automatic payments. Your credit card information
          will be saved to your account and payments will automatically be
          charged on the anniversary date of your rental each month.
        </Text>
      </View>
    </View>
  );
}

function PaymentInfo(props: PaymentInfoProps) {
  const {
    unit,
    isToggled,
    isToggleVisible,
    onToggleChange,
    onToggleAutoPayCallBack,
    onPress,
  } = props;
  const [isVisible, setIsVisible] = useState<boolean>(!isToggleVisible);
  const { layout, gutters, backgrounds, borders } = useTheme();

  const toggleVisibility = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    const updatedToggle = !isVisible;
    setIsVisible(updatedToggle);
    if (onToggleChange) {
      onToggleChange(updatedToggle);
    }
  };

  const onToggleAutoPay = () => {
    if (onToggleAutoPayCallBack) {
      onToggleAutoPayCallBack();
    }
  };

  return (
    <View
      style={[
        layout.col,
        gutters.marginTop_16,
        layout.flex_1,
        backgrounds.white,
        borders.rounded_16,
      ]}
    >
      <UnitHeader
        unitNumber={unit.unitNumber}
        isVisible={isVisible}
        isDropDownVisible={isToggleVisible}
        toggleVisibility={() => {
          if (isToggleVisible) {
            toggleVisibility();
          }
        }}
      />
      {isVisible && (
        <UnitDetails
          onToggleAutoPay={onToggleAutoPay}
          unit={unit}
          autoPay={isToggled || unit.autopay}
          onPress={onPress}
        />
      )}
    </View>
  );
}

export default PaymentInfo;
