import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  TextStyle,
  ViewStyle,
  Platform,
} from 'react-native';
import * as Sentry from '@sentry/react-native';
import { useTheme } from '@/theme';
import { DoorState, GateSystemType } from '@/utils/commonFunctions';
import { renderSVGIcon } from '@/utils/icons';
import { getNokeUnlockCommand, openGatePTI } from '@/services/api';
import {
  GetNokeUnlockCommandRequest,
  OpenGateRequest,
} from '@/types/schemas/request';
import { useMutation } from '@tanstack/react-query';
import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import NokeModule, { NokeDeviceEvent, NokeErrorEvent } from '@/noke/NokeModule';
import { DateTime } from 'luxon';

export type SSDore = {
  name: string;
  id: string;
  type: GateSystemType;
  tenantId: number;
  facilityId: number;
  nokeOfflineKey?: string;
  nokeOfflineKeyExpiration?: string;
  nokeOfflineUnlockCommand?: string;
  nokeScheduledOfflineUnlockCommand?: string;
};

interface IconProps {
  color?: string;
  size?: number;
}

type Props = {
  item: SSDore;
  icon: React.ComponentType<IconProps>;
  facilityId: number;
  tenantId: number;
  accessCode: string;
  defultDoorState: DoorState;
  progress: number;
  isGateButtonOpening: (isGateButtonOpening: boolean) => void;
  buttonOnPress: () => void;
  onError?: (item: SSDore) => void;
};

enum NokeConnectionStatus {
  NOT_FOUND,
  DISCOVERED,
  CONNECTING,
  CONNECTED,
}

function SSDoorButton({
  item,
  icon: Icon,
  facilityId,
  tenantId,
  accessCode,
  defultDoorState,
  progress = 0,
  hasInternetAccess,
  isGateButtonOpening,
  buttonOnPress,
  onError,
}: Props) {
  const { fonts, layout, backgrounds, borders, gutters } = useTheme();
  const [currentProgress, setCurrentProgress] = useState<number>(progress);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [doorState, setDoorState] = useState(defultDoorState);
  const [connectionStatus, setConnectionStatus] = useState(
    NokeConnectionStatus.NOT_FOUND,
  );
  const [waitingToConnect, setWaitingToConnect] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isSuccessRef = useRef(isSuccess);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  useEffect(() => {
    isSuccessRef.current = isSuccess;
  }, [isSuccess]);

  useEffect(() => {
    // Start the timer to reset the door state from OPENED/FAILED to OPEN after 5 seconds
    if (doorState === DoorState.OPENED || doorState === DoorState.FAILED) {
      const timeout = setTimeout(() => {
        setDoorState(DoorState.OPEN);
        setCurrentProgress(0);
      }, 5000);

      return () => clearTimeout(timeout);
    }
    return undefined;
  }, [doorState]);

  const onErrorUnlockingNokeDevice = useCallback(() => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (connectionStatus === NokeConnectionStatus.CONNECTED) {
      NokeModule.disconnectFromLock();
    }
    setConnectionStatus(NokeConnectionStatus.NOT_FOUND);
    setDoorState(DoorState.FAILED);
    setCurrentProgress(0);
    isGateButtonOpening(false);
    onError?.(item);
  }, [connectionStatus, onError]);

  const connectionTimeout = useCallback(() => {
    setConnectionStatus(NokeConnectionStatus.NOT_FOUND);
    setWaitingToConnect(false);
    if (
      connectionStatus === NokeConnectionStatus.CONNECTING ||
      connectionStatus === NokeConnectionStatus.CONNECTED ||
      doorState === DoorState.OPENING
    ) {
      onErrorUnlockingNokeDevice();
    }
  }, [connectionStatus, doorState, onErrorUnlockingNokeDevice]);

  const updateConnectionTimeout = useCallback(
    (finished?: boolean) => {
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
      if (!finished) {
        connectionTimeoutRef.current = setTimeout(connectionTimeout, 10000);
      }
    },
    [connectionTimeout],
  );

  // reset timeout when any callback dependencies are updated
  useEffect(() => {
    if (connectionTimeoutRef.current) {
      updateConnectionTimeout();
    }
  }, [updateConnectionTimeout]);

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      NokeModule.searchForLock(item.name, item.id);
    }

    return () => {
      if (item.type === GateSystemType.NOKE) {
        NokeModule.stopSearchingForLock(item.id);
      }
    };
  }, [item]);

  const onRecievedUnlockCommand = useCallback(
    (unlockCommand: string) => {
      if (connectionStatus === NokeConnectionStatus.CONNECTED) {
        NokeModule.sendCommand(unlockCommand);
      } else {
        onErrorUnlockingNokeDevice();
      }
    },
    [connectionStatus, onErrorUnlockingNokeDevice],
  );

  const onNokeDisconnected = useCallback(() => {
    setConnectionStatus(NokeConnectionStatus.NOT_FOUND);
    if (doorState === DoorState.OPENING) {
      onErrorUnlockingNokeDevice();
    }
    updateConnectionTimeout(true);
  }, [doorState, updateConnectionTimeout, onErrorUnlockingNokeDevice]);

  const getUnlockCommandMutation = useMutation({
    mutationFn: (getUnlockCommandRequest: GetNokeUnlockCommandRequest) => {
      return getNokeUnlockCommand(getUnlockCommandRequest);
    },
    onSuccess: unlockCommandData => {
      if (unlockCommandData.nokeResponse?.data?.commands) {
        onRecievedUnlockCommand(
          unlockCommandData.nokeResponse.data.commands[0],
        );
      } else {
        onErrorUnlockingNokeDevice();
      }
    },
    onError: e => {
      console.log(e);
      onErrorUnlockingNokeDevice();
    },
  });

  const callGetUnlockCommand = (
    getUnlockCommandRequest: GetNokeUnlockCommandRequest,
  ) => {
    getUnlockCommandMutation.reset();
    getUnlockCommandMutation.mutate(getUnlockCommandRequest);
  };

  const onNokeDiscovered = useCallback(() => {
    if (waitingToConnect) {
      setWaitingToConnect(false);
      NokeModule.connectToLock(item.id);
      setConnectionStatus(NokeConnectionStatus.CONNECTING);
    } else if (connectionStatus === NokeConnectionStatus.NOT_FOUND) {
      setConnectionStatus(NokeConnectionStatus.DISCOVERED);
    } else if (
      Platform.OS === 'ios' &&
      connectionStatus === NokeConnectionStatus.CONNECTING
    ) {
      // iOS will sometimes get stuck when attempting to connect to a recently used lock
      // if we receive the discovered call after requesting to connect, resend the connection request
      NokeModule.connectToLock(item.id);
    }
    updateConnectionTimeout();
  }, [connectionStatus, waitingToConnect, updateConnectionTimeout]);

  const onNokeUnlocked = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setDoorState(DoorState.OPENED);
    setCurrentProgress(110);
    isGateButtonOpening(false);
    setIsSuccess(true);
    NokeModule.disconnectFromLock();
    setConnectionStatus(NokeConnectionStatus.NOT_FOUND);
  }, [intervalRef]);

  const onNokeConnected = useCallback(
    (lockSession: string) => {
      setConnectionStatus(NokeConnectionStatus.CONNECTED);
      let offlineKeyValid = false;
      if (item.nokeOfflineKey && item.nokeOfflineKeyExpiration) {
        const expiration = DateTime.fromISO(item.nokeOfflineKeyExpiration);
        if (expiration.isValid && expiration.diffNow().milliseconds > 0) {
          offlineKeyValid = true;
        }
      }

      if (offlineKeyValid) {
        if (item.nokeOfflineUnlockCommand) {
          NokeModule.offlineUnlock(
            item.nokeOfflineKey!,
            item.nokeOfflineUnlockCommand,
          );
        } else if (item.nokeScheduledOfflineUnlockCommand) {
          NokeModule.scheduledOfflineUnlock(
            item.nokeOfflineKey!,
            item.nokeScheduledOfflineUnlockCommand,
          );
        } else {
          callGetUnlockCommand({
            facilityId,
            tenantId,
            lockSession,
            lockMacAddress: item.id,
          });
        }
      } else {
        callGetUnlockCommand({
          facilityId,
          tenantId,
          lockSession,
          lockMacAddress: item.id,
        });
      }
    },
    [connectionStatus, item, facilityId, tenantId],
  );

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      const discoveredListener = NokeModule.eventEmitter.addListener(
        NokeModule.events.NOKE_DISCOVERED,
        (event: NokeDeviceEvent) => {
          if (event.noke.mac !== item.id) return;

          onNokeDiscovered();
        },
      );

      return () => {
        discoveredListener.remove();
      };
    }

    return () => {};
  }, [item, onNokeDiscovered]);

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      const connectedListener = NokeModule.eventEmitter.addListener(
        NokeModule.events.NOKE_CONNECTED,
        (event: NokeDeviceEvent) => {
          if (event.noke.mac !== item.id) return;

          Sentry.addBreadcrumb({
            category: 'Noke',
            level: 'info',
            message: 'Connected to Noke device',
            data: {
              deviceMac: event.noke.mac,
              deviceName: event.noke.name,
            },
          });

          onNokeConnected(event.noke.session);
        },
      );

      return () => {
        connectedListener.remove();
      };
    }

    return () => {};
  }, [item, onNokeConnected]);

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      const unlockListener = NokeModule.eventEmitter.addListener(
        NokeModule.events.NOKE_UNLOCKED,
        (event: NokeDeviceEvent) => {
          if (event.noke.mac !== item.id) return;

          Sentry.addBreadcrumb({
            category: 'Noke',
            level: 'info',
            message: 'Unlocked Noke device',
            data: {
              deviceMac: event.noke.mac,
              deviceName: event.noke.name,
            },
          });

          onNokeUnlocked();
        },
      );

      return () => {
        unlockListener.remove();
      };
    }

    return () => {};
  }, [item, onNokeUnlocked]);

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      const disconnectedListener = NokeModule.eventEmitter.addListener(
        NokeModule.events.NOKE_DISCONNECTED,
        (event: NokeDeviceEvent) => {
          if (event.noke.mac !== item.id) return;

          Sentry.addBreadcrumb({
            category: 'Noke',
            level: 'info',
            message: 'Disconnected from Noke device',
            data: {
              deviceMac: event.noke.mac,
              deviceName: event.noke.name,
            },
          });

          onNokeDisconnected();
        },
      );

      return () => {
        disconnectedListener.remove();
      };
    }

    return () => {};
  }, [item, onNokeDisconnected]);

  useEffect(() => {
    if (item.type === GateSystemType.NOKE) {
      const errorListener = NokeModule.eventEmitter.addListener(
        NokeModule.events.ERROR,
        (event: NokeErrorEvent) => {
          Sentry.addBreadcrumb({
            category: 'Noke',
            level: 'info',
            message: 'Error recieved from Noke device',
            data: {
              deviceMac: event.noke?.mac,
              deviceName: event.noke?.name,
            },
          });
          if (event.noke?.mac !== item.id) return;

          onErrorUnlockingNokeDevice();
        },
      );

      return () => {
        errorListener.remove();
      };
    }

    return () => {};
  }, [item, onErrorUnlockingNokeDevice]);

  const startProgress = () => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setCurrentProgress(prev => {
        if (prev >= 100) {
          if (isSuccessRef.current && item.type !== GateSystemType.NOKE) {
            clearInterval(intervalRef.current!);
            setDoorState(DoorState.OPENED);
            isGateButtonOpening(false);
            return 110;
          }
          return prev;
        }
        setDoorState(DoorState.OPENING);
        return prev + 2;
      });
    }, 100);
  };

  const openGatePTIMutation = useMutation({
    mutationFn: (openGaterequest: OpenGateRequest) => {
      return openGatePTI(openGaterequest);
    },
    onSuccess: async () => {
      await logEventWithStatus(
        Events.gateAccessRequest,
        EventStatus.completed,
        {
          facilityId: facilityId.toString(),
          tenantId: tenantId.toString(),
          gateType: item.type,
          deviceId: item.id,
        },
      );
      setIsSuccess(true);
    },
    onError: async e => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      await logEventWithStatus(
        Events.gateAccessRequest,
        EventStatus.failed,
        {
          facilityId: facilityId.toString(),
          tenantId: tenantId.toString(),
          gateType: item.type,
          deviceId: item.id,
          error: JSON.stringify(e),
        },
      );
      setDoorState(DoorState.FAILED);
      setCurrentProgress(0);
      isGateButtonOpening(false);
      console.log(e);
    },
  });

  const callOpenGatePTI = async () => {
    isGateButtonOpening(true);
    const openGateRequest: OpenGateRequest = {
      facilityId,
      tenantId,
      accessCode,
      gateDeviceId: item.id,
    };
    openGatePTIMutation.reset();
    await openGatePTIMutation.mutateAsync(openGateRequest);
  };

  const connectToNoke = () => {
    isGateButtonOpening(true);
    updateConnectionTimeout();
    if (connectionStatus === NokeConnectionStatus.DISCOVERED) {
      Sentry.addBreadcrumb({
        category: 'Noke',
        level: 'info',
        message: 'Initiate connection to Noke device',
        data: {
          deviceMac: item.id,
          deviceName: item.name,
          facilityId: facilityId.toString(),
          tenantId: tenantId.toString()
        },
      });
      NokeModule.connectToLock(item.id);
      setConnectionStatus(NokeConnectionStatus.CONNECTING);
    } else if (connectionStatus === NokeConnectionStatus.NOT_FOUND) {
      setWaitingToConnect(true);
    }
  };

  const onPress = async () => {
    await logEventWithStatus(
      Events.gateAccessRequest,
      EventStatus.initiated,
      {
        facilityId: facilityId.toString(),
        tenantId: tenantId.toString(),
        gateType: item.type,
        deviceId: item.id,
      },
    );
    setCurrentProgress(0);
    startProgress();
    switch (item.type) {
      case GateSystemType.OPENTECH:
        void callOpenGatePTI();
        break;
      case GateSystemType.PTI:
        void callOpenGatePTI();
        break;
      case GateSystemType.NOKE:
        connectToNoke();
        break;
      default:
        break;
    }
    buttonOnPress();
  };

  const getStyles = () => {
    const backgroundColors: Record<DoorState, ViewStyle> = {
      [DoorState.OPEN]: { backgroundColor: backgrounds.olive.backgroundColor },
      [DoorState.FAILED]: {
        backgroundColor: backgrounds.lightCherry.backgroundColor,
      },
      [DoorState.OPENING]: {
        backgroundColor: backgrounds.lightGreen.backgroundColor,
      },
      [DoorState.OPENED]: {
        backgroundColor: backgrounds.lightGreen.backgroundColor,
      },
    };

    const textColors: Record<DoorState, TextStyle> = {
      [DoorState.OPEN]: { color: fonts.white.color },
      [DoorState.FAILED]: { color: fonts.cherry.color },
      [DoorState.OPENING]: { color: fonts.charcoal.color },
      [DoorState.OPENED]: { color: fonts.olive.color },
    };

    const titleTextColors: Record<DoorState, TextStyle> = {
      [DoorState.OPEN]: { color: fonts.charcoal.color },
      [DoorState.FAILED]: { color: fonts.charcoal.color },
      [DoorState.OPENING]: { color: fonts.charcoal.color },
      [DoorState.OPENED]: { color: fonts.white.color },
    };

    const iconColors: Record<DoorState, string | undefined> = {
      [DoorState.OPEN]: fonts.white.color,
      [DoorState.FAILED]: fonts.cherry.color,
      [DoorState.OPENING]: undefined,
      [DoorState.OPENED]: fonts.olive.color,
    };

    const iconBackgroundColors: Record<DoorState, ViewStyle> = {
      [DoorState.OPEN]: {
        backgroundColor: backgrounds.blue.backgroundColor,
      },
      [DoorState.FAILED]: {
        backgroundColor: backgrounds.lightCherry.backgroundColor,
      },
      [DoorState.OPENING]: {
        backgroundColor: backgrounds.lightGreen.backgroundColor,
      },
      [DoorState.OPENED]: {
        backgroundColor: backgrounds.lightGreen.backgroundColor,
      },
    };

    const buttonTexts: Record<DoorState, string> = {
      [DoorState.OPEN]: 'Open',
      [DoorState.FAILED]: 'Retry',
      [DoorState.OPENING]: 'Opening ...',
      [DoorState.OPENED]: 'Opened',
    };

    return {
      backgroundColor: backgroundColors[doorState],
      textColor: textColors[doorState],
      titleTextColor: titleTextColors[doorState],
      iconColor: iconColors[doorState],
      iconBackgroundColor: iconBackgroundColors[doorState],
      buttonText: buttonTexts[doorState],
    };
  };

  const {
    backgroundColor,
    textColor,
    titleTextColor,
    iconColor,
    iconBackgroundColor,
    buttonText,
  } = getStyles();

  return (
    <TouchableOpacity
      style={[
        borders.rounded_16,
        layout.row,
        gutters.padding_8,
        gutters.marginTop_16,
        gutters.paddingHorizontal_16,
        layout.itemsCenter,
      ]}
      activeOpacity={1}
      disabled={doorState !== DoorState.OPEN}
      onPress={() => {
        void onPress();
      }}
    >
      <View
        style={[
          StyleSheet.absoluteFillObject,
          backgrounds.white,
          borders.rounded_16,
        ]}
      />
      <View
        style={[
          StyleSheet.absoluteFillObject,
          currentProgress >= 110
            ? backgrounds.springGrass60
            : backgrounds.springGrass40,
          borders.bottomLeftRounded_16,
          borders.topLeftRounded_16,
          currentProgress >= 110 ? borders.rounded_16 : undefined,
          { width: `${currentProgress}%` },
        ]}
      />
      <View style={[layout.flex_1, layout.row, layout.itemsCenter]}>
        <View
          style={[
            layout.h_40,
            layout.w_40,
            borders.rounded_20,
            iconBackgroundColor,
            layout.itemsCenter,
            layout.justifyCenter,
            layout.selfCenter,
          ]}
        >
          {doorState === DoorState.OPENING ? (
            <ActivityIndicator size={32} color={borders.blue.borderColor} />
          ) : (
            renderSVGIcon(Icon, iconColor)
          )}
        </View>
        <View
          style={[
            layout.flex_1,
            layout.row,
            gutters.margin_8,
            layout.itemsCenter,
            layout.justifyBetween,
          ]}
        >
          <Text
            style={[
              fonts.size_16,
              fonts.appFontMedium,
              layout.itemsCenter,
              titleTextColor,
            ]}
          >
            {item.name}
          </Text>
          <View
            style={[
              backgroundColor,
              borders.rounded_12,
              gutters.paddingTop_4,
              gutters.paddingRight_12,
              gutters.paddingBottom_4,
              gutters.paddingLeft_12,
              layout.h_24,
            ]}
          >
            <Text
              style={[
                textColor,
                fonts.size_12,
                fonts.appFontBold,
                fonts.uppercase,
                { letterSpacing: 1.2 },
              ]}
            >
              {buttonText}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export default SSDoorButton;
