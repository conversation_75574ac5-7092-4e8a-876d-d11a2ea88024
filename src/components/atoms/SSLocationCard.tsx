import useTheme from '@/theme/hooks/useTheme';
import React from 'react';
import { View, Text, Image, Linking, Platform } from 'react-native';
import {
  RoundedKeySVG,
  RoundedLocationSVG,
  RoundedCallSVG,
} from '@/assets/svg';
import { Location } from '@/types/schemas/location';
import SSActionButton from './SSActionButton';

const openMapWithAddress = (
  address: string,
  city: string,
  state: string,
  zip: string,
) => {
  const query: string = encodeURIComponent(
    `${address ?? ''}, ${city ?? ''}, ${state ?? ''} ${zip ?? ''}`
  );
  const url: string = Platform.select({
    ios: `maps://?q=${query}`,
    android: `geo:0,0?q=${query}`,
    default: `https://www.google.com/maps/search/?api=1&query=${query}`, // Default for other platforms
  });

  Linking.openURL(url).catch(err => console.error('An error occurred', err));
};

type SSLocationCardProps = {
  location: Location;
  onAccess: () => void;
};

function SSLocationCard(props: SSLocationCardProps) {
  const { layout, fonts, gutters, backgrounds, borders } = useTheme();
  const { location, onAccess } = props;

  return (
    <View
      style={[layout.col, layout.flex_1, backgrounds.white, borders.rounded_16]}
    >
      <View
        style={[
          layout.col,
          layout.flex_1,
          layout.justifyBetween,
          borders.gray100,
        ]}
      >
        <Image
          style={[
            layout.fullWidth,
            layout.h_160,
            { borderTopLeftRadius: 16, borderTopRightRadius: 16 },
          ]}
          source={{ uri: location.image.url }}
        />

        <View style={[gutters.padding_24]}>
          <Text style={[fonts.charcoal, fonts.appFontMedium, fonts.size_16]}>
            {location.address}
          </Text>
          <Text style={[fonts.charcoal, fonts.appFontMedium, fonts.size_16]}>
            {location.city}, {location.state} {location.zip}
          </Text>
        </View>
      </View>
      <View
        style={[
          layout.row,
          layout.justifyCenter,
          layout.itemsCenter,
          gutters.marginLeft_12,
          gutters.marginRight_12,
          gutters.paddingBottom_12,
        ]}
      >
        <SSActionButton
          icon={<RoundedKeySVG />}
          label="Access"
          onPress={onAccess}
        />
        <SSActionButton
          icon={<RoundedLocationSVG />}
          label="Directions"
          onPress={() =>
            openMapWithAddress(
              location.address,
              location.city,
              location.state,
              location.zip,
            )
          }
        />
        <SSActionButton
          icon={<RoundedCallSVG />}
          label="Call"
          onPress={() => void Linking.openURL(`tel:${location?.phone ?? ''}`)}
        />
      </View>
    </View>
  );
}

export default SSLocationCard;
