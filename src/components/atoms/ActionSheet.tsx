import React, { useState } from 'react';
import {
  Text,
  TouchableOpacity,
  Animated,
  Easing,
  LayoutChangeEvent,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTheme } from '@/theme';

interface Action {
  text: string;
  onPress: () => void;
  buttonStyle?: ViewStyle;
  textStyle?: TextStyle;
}

interface RouteParams {
  title?: string;
  actions?: Action[];
  onDismiss?: () => void;
}

function ActionSheet(Props: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  navigation: StackNavigationProp<any>;
  route: RouteProp<{ params: RouteParams }, 'params'>;
}) {
  const { navigation, route } = Props;
  const { layout, borders, backgrounds, gutters, fonts } = useTheme();

  const [animationDriver] = useState(new Animated.Value(0));
  const [cardHeight, setCardHeight] = useState(0);
  const [selectionMade, setSelectionMade] = useState(false);

  const animateIn = () => {
    Animated.timing(animationDriver, {
      toValue: 1,
      duration: 500,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    }).start();
  };

  const close = () => {
    Animated.timing(animationDriver, {
      toValue: 0,
      duration: 250,
      easing: Easing.in(Easing.quad),
      useNativeDriver: true,
    }).start();
    navigation.goBack();
  };

  const onActionPressed = (callBack?: () => void) => {
    if (selectionMade) return;
    setSelectionMade(true);
    close();
    setTimeout(() => {
      if (callBack && typeof callBack === 'function') {
        callBack();
      }
    }, 600);
  };

  const onCancel = () => {
    if (selectionMade) return;
    setSelectionMade(true);
    const onDismiss = route.params?.onDismiss;
    Animated.timing(animationDriver, {
      toValue: 0,
      duration: 250,
      easing: Easing.in(Easing.quad),
      useNativeDriver: true,
    }).start();
    if (onDismiss) {
      onDismiss();
    }
    navigation.goBack();
  };

  const onCardLayout = ({
    nativeEvent: {
      layout: { height },
    },
  }: LayoutChangeEvent) => {
    setCardHeight(height);
    animateIn();
  };

  const renderActions = () => {
    const actions = route?.params?.actions ?? [];
    return actions.map(action => (
      <TouchableOpacity
        key={action.text}
        onPress={() => onActionPressed(action.onPress)}
        style={[
          layout.h_60,
          layout.itemsCenter,
          layout.justifyCenter,
          layout.fullWidth,
          borders.top_1,
          borders.borderGray,
          action.buttonStyle,
        ]}
      >
        <Text style={[fonts.size_20, fonts.deepBlue, action.textStyle]}>
          {action.text}
        </Text>
      </TouchableOpacity>
    ));
  };

  const cancelButton = () => (
    <TouchableOpacity
      key="Cancel"
      onPress={onCancel}
      style={[
        backgrounds.white,
        layout.h_60,
        borders.rounded_13,
        gutters.marginBottom_32,
        layout.itemsCenter,
        layout.justifyCenter,
        layout.fullWidth,
      ]}
    >
      <Text style={[fonts.size_20, fonts.semiBold, fonts.deepBlue]}>
        Cancel
      </Text>
    </TouchableOpacity>
  );

  const title = route?.params?.title ?? '';
  const cardPosY = animationDriver.interpolate({
    inputRange: [0, 1],
    outputRange: [cardHeight, 0],
  });

  return (
    <TouchableOpacity
      activeOpacity={1}
      style={[layout.flex_1, layout.justifyEnd]}
    >
      <Animated.View
        style={[
          layout.absolute,
          layout.top0,
          layout.right0,
          layout.bottom0,
          layout.left0,
          { opacity: animationDriver, backgroundColor: 'rgba(0, 0, 0, 0.6)' },
        ]}
      />
      <Animated.View
        style={{
          transform: [{ translateY: cardPosY }],
          opacity: cardHeight ? 1 : 0,
        }}
        onLayout={onCardLayout}
      >
        <View style={[gutters.marginHorizontal_10, layout.itemsCenter]}>
          <View
            style={[
              gutters.marginHorizontal_10,
              layout.fullWidth,
              layout.itemsCenter,
              borders.rounded_13,
              gutters.marginBottom_10,
              backgrounds.argentGray,
            ]}
          >
            <Text
              style={[
                fonts.semiBold,
                fonts.size_12,
                fonts.dimGray,
                gutters.margin_12,
                backgrounds.argentGray,
              ]}
            >
              {title}
            </Text>
            {renderActions()}
          </View>
          {cancelButton()}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
}

export default ActionSheet;
