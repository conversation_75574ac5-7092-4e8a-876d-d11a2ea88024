import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import { DeleteIconSVG, DotSVG, EditIconSVG } from '@/assets/svg';
import { BillingAddress } from '@/types/schemas/account';
import SSButton from './SSButton/SSButton';

type Props = {
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onSetAsDefaultPress?: () => void;
  item: BillingAddress;
};
function SavedAddressCard(props: Props) {
  const { fonts, layout, backgrounds, borders, gutters } = useTheme();
  const { onEditPress, onDeletePress, onSetAsDefaultPress, item } = props;
  return (
    <TouchableOpacity
      style={[
        backgrounds.white,
        borders.rounded_16,
        layout.justifyBetween,
        layout.row,
        layout.itemsCenter,
        gutters.paddingHorizontal_16,
      ]}
      activeOpacity={1}
    >
      <View
        style={[
          layout.flex_1,
          gutters.paddingVertical_16,
          gutters.gap_2,
          layout.justifyBetween,
        ]}
      >
        <Text style={[fonts.size_16, fonts.charcoal, fonts.appFontRegular]}>
          {item.address} {item.appartment}
        </Text>
        <Text style={[fonts.size_16, fonts.charcoal, fonts.appFontRegular]}>
          {item.city} {item.state} {item.zipCode}
        </Text>
        <View
          style={[
            layout.flex_1,
            layout.row,
            gutters.paddingTop_10,
            layout.justifyBetween,
            layout.itemsCenter,
          ]}
        >
          <View style={[layout.justifyBetween, layout.row, layout.w_80]}>
            {!item.isPrimary ? (
              <TouchableOpacity
                accessibilityLabel="Delete Address icon"
                onPress={onDeletePress}
              >
                <DeleteIconSVG />
              </TouchableOpacity>
            ) : null}
          </View>
          <View>
            {item.isPrimary ? (
              <View style={[layout.row, layout.itemsCenter]}>
                <DotSVG />
                <Text
                  style={[
                    gutters.paddingLeft_10,
                    fonts.size_12,
                    fonts.lightCharcoal,
                    fonts.appFontBold,
                    { letterSpacing: 1.2 },
                  ]}
                >
                  DEFAULT
                </Text>
              </View>
            ) : (
              <SSButton
                title="SET AS DEFAULT"
                widthStyle={layout.w_137}
                heightStyle={layout.h_26}
                fontColorStyle={fonts.blue}
                customBorderColorStyle={borders.blue}
                fontSizeStyle={fonts.size_12}
                borderRadiusStyle={borders.rounded_13}
                variant="transparent"
                onPress={onSetAsDefaultPress}
              />
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export default SavedAddressCard;
