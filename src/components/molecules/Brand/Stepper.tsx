import React from 'react';
import { View, Text, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { CheckSmallSVG } from '@/assets/svg';

interface StepIconProps {
  isFirstStep: boolean;
  isLastStep: boolean;
  isActiveStep?: boolean;
  isCompletedStep?: boolean;
  borderWidth?: number;
  progressBarColor?: string;
  completedProgressBarColor?: string;
  activeStepIconColor?: string;
  disabledStepIconColor?: string;
  completedStepIconColor?: string;
  labelFontFamily?: string;
  labelColor?: string;
  labelFontSize?: number;
  activeLabelColor?: string;
  completedLabelColor?: string;
  activeStepNumColor?: string;
  completedStepNumColor?: string;
  disabledStepNumColor?: string;
  label: string;
}

function Stepper(props: StepIconProps) {
  const {
    isFirstStep,
    isLastStep,
    isActiveStep,
    isCompletedStep,
    borderWidth,
    progressBarColor,
    completedProgressBarColor,
    activeStepIconColor,
    completedStepIconColor,
    disabledStepIconColor,
    labelFontFamily,
    labelColor,
    labelFontSize,
    activeLabelColor,
    completedLabelColor,
    activeStepNumColor,
    completedStepNumColor,
    disabledStepNumColor,
    label,
  } = props;

  let styles: {
    circleStyle: StyleProp<ViewStyle>;
    circleText: StyleProp<TextStyle>;
    labelText: StyleProp<TextStyle>;
    leftBar?: StyleProp<ViewStyle>;
    rightBar?: StyleProp<ViewStyle>;
    stepNum?: StyleProp<TextStyle>;
  };

  if (isActiveStep) {
    styles = {
      circleStyle: {
        width: 40,
        height: 40,
        borderRadius: 20,
        borderWidth: 8,
        bottom: 2,
        borderColor: '#CCE5F3',
        backgroundColor: activeStepIconColor,
      },
      circleText: {
        alignSelf: 'center',
        top: 20 / 2,
      },
      labelText: {
        textAlign: 'center',
        flexWrap: 'wrap',
        width: 100,
        paddingTop: 4,
        fontWeight: '700',
        fontFamily: labelFontFamily,
        color: activeLabelColor,
        fontSize: labelFontSize,
      },
      leftBar: {
        position: 'absolute',
        top: 40 / 2.22,
        left: 0,
        right: 40 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: completedProgressBarColor,
        marginRight: 40 / 2 + 2,
      },
      rightBar: {
        position: 'absolute',
        top: 40 / 2.22,
        right: 0,
        left: 40 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: progressBarColor,
        marginLeft: 40 / 2 + 2,
      },
      stepNum: {
        color: activeStepNumColor,
      },
    };
  } else if (isCompletedStep) {
    styles = {
      circleStyle: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: completedStepIconColor,
      },
      circleText: {
        alignSelf: 'center',
        top: 22 / 2,
      },
      labelText: {
        textAlign: 'center',
        flexWrap: 'wrap',
        width: 100,
        paddingTop: 4,
        fontWeight: '700',
        fontFamily: labelFontFamily,
        color: completedLabelColor,
        marginTop: 4,
        fontSize: labelFontSize,
      },
      leftBar: {
        position: 'absolute',
        top: 36 / 2,
        left: 0,
        right: 36 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: completedProgressBarColor,
        marginRight: 36 / 2 + 4,
      },
      rightBar: {
        position: 'absolute',
        top: 36 / 2,
        right: 0,
        left: 36 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: completedProgressBarColor,
        marginLeft: 36 / 2 + 4,
      },
      stepNum: {
        color: completedStepNumColor,
      },
    };
  } else {
    styles = {
      circleStyle: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: disabledStepIconColor,
      },
      circleText: {
        alignSelf: 'center',
        top: 18 / 2,
      },
      labelText: {
        textAlign: 'center',
        flexWrap: 'wrap',
        width: 100,
        paddingTop: 4,
        fontWeight: '700',
        fontFamily: labelFontFamily,
        color: labelColor,
        marginTop: 4,
        fontSize: labelFontSize,
      },
      leftBar: {
        position: 'absolute',
        top: 36 / 2,
        left: 0,
        right: 36 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: progressBarColor,
        marginRight: 36 / 2 + 4,
      },
      rightBar: {
        position: 'absolute',
        top: 36 / 2,
        right: 0,
        left: 36 + 8,
        borderTopWidth: borderWidth,
        borderTopColor: progressBarColor,
        marginLeft: 36 / 2 + 4,
      },
      stepNum: {
        color: disabledStepNumColor,
      },
    };
  }

  return (
    <View style={{ flexDirection: 'column', alignItems: 'center' }}>
      <View style={styles.circleStyle}>
        <Text style={styles.circleText}>
          {isCompletedStep && <CheckSmallSVG />}
        </Text>
      </View>
      <Text style={styles.labelText}>{label}</Text>
      {!isFirstStep && <View style={styles.leftBar} />}
      {!isLastStep && <View style={styles.rightBar} />}
    </View>
  );
}

Stepper.defaultProps = {
  isActiveStep: false,
  isCompletedStep: false,
  borderWidth: 2,
  progressBarColor: '#B8C0C5',
  completedProgressBarColor: '#0077B8',
  activeStepIconColor: '#0077B8',
  completedStepIconColor: '#0077B8',
  disabledStepIconColor: '#B8C0C5',
  labelFontFamily: 'Filson-Pro',
  labelColor: '#0077B8',
  labelFontSize: 12,
  activeLabelColor: '#0077B8',
  completedLabelColor: '#0077B8',
  activeStepNumColor: 'black',
  completedStepNumColor: 'black',
  disabledStepNumColor: 'white',
};

export default Stepper;
