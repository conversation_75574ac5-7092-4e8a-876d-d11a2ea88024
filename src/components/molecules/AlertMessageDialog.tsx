import { AlertSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import React from 'react';
import { Modal, View, Text, Dimensions, ScrollView } from 'react-native';
import { SSButton } from '../atoms';

const { width } = Dimensions.get('window');

type AddUnitDialogProps = {
  onLeave?: () => void;
  onSaveNote?: () => void;
};

function AlertMessageDialog(props: AddUnitDialogProps) {
  const { onLeave, onSaveNote } = props;
  const { layout, gutters, borders, backgrounds, fonts } = useTheme();

  const renderView = () => (
    <View
      style={[
        layout.fullWidth,
        gutters.gap_24,
        gutters.paddingTop_32,
        gutters.paddingRight_16,
        gutters.paddingBottom_32,
        gutters.paddingLeft_32,
        layout.itemsCenter,
      ]}
    >
      <AlertSVG />
      <Text
        style={[
          fonts.size_24,
          fonts.appFontBold,
          fonts.charcoal,
          fonts.alignCenter,
        ]}
      >
        Oops! You forgot to save your note.
      </Text>
      <Text
        style={[
          fonts.size_14,
          fonts.appFontRegular,
          fonts.charcoal,
          fonts.alignCenter,
        ]}
      >
        Please make sure to tap the &apos;Save Note&apos; button to keep your
        changes.
      </Text>
      <View style={[layout.h_1, layout.fullWidth, backgrounds.springGrass20]} />
      <View style={[layout.fullWidth, gutters.gap_16]}>
        <SSButton title="Save Note" variant="dark" onPress={onSaveNote} />
        <SSButton title="Leave" variant="transparent" onPress={onLeave} />
      </View>
    </View>
  );

  return (
    <Modal
      transparent
      animationType="slide"
      onRequestClose={onLeave || onSaveNote}
    >
      <View
        style={[
          layout.fullWidth,
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <ScrollView
          contentContainerStyle={[
            layout.flexGrow_1,
            layout.justifyCenter,
            layout.itemsCenter,
          ]}
        >
          <View
            style={[
              { width: width - 32 },
              gutters.padding_16,
              borders.rounded_12,
              layout.itemsCenter,
              backgrounds.white,
            ]}
          >
            {renderView()}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

export default AlertMessageDialog;
