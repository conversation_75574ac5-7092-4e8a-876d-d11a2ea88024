import React from 'react';
import { Modal, Text, View } from 'react-native';
import { useTheme } from '@/theme';
import SSButton from '../atoms/SSButton/SSButton';
import PermissionToggles from '../atoms/PermissionToggles';

interface PermissionsModalProps {
  onConfirm: () => void;
}

function PermissionsDialog({ onConfirm }: PermissionsModalProps) {
  const { gutters, borders, layout, backgrounds, fonts } = useTheme();
  return (
    <Modal transparent animationType="slide">
      <View
        style={[
          layout.fullWidth,
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <View
          style={[
            { width: '95%' },
            gutters.paddingTop_32,
            gutters.paddingRight_16,
            gutters.paddingBottom_32,
            gutters.paddingLeft_16,
            borders.rounded_12,
            backgrounds.white,
            gutters.gap_16,
          ]}
        >
          <Text style={[fonts.size_24, fonts.appFontBold, fonts.charcoal]}>
            Permissions Request
          </Text>
          <PermissionToggles
            toggleContainerStyle={[
              borders.bottom_1,
              borders.springGrass20,
              gutters.paddingBottom_12,
            ]}
          />
          <Text
            style={[fonts.size_14, fonts.appFontRegular, fonts.lightCharcoal]}
          >
            These permissions are required for the app to function properly
          </Text>
          <SSButton
            variant="dark"
            title="CONFIRM"
            onPress={() => onConfirm()}
          />
        </View>
      </View>
    </Modal>
  );
}

export default PermissionsDialog;
