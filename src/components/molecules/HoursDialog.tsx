import { ClockIconSvg } from '@/assets/svg';
import { useTheme } from '@/theme';
import React from 'react';
import {
  Modal,
  View,
  Dimensions,
  ScrollView,
  Text,
  FlatList,
} from 'react-native';
import { Location } from '@/types/schemas/location';
import { DateTime } from 'luxon';
import { SSButton } from '../atoms';

const { width } = Dimensions.get('window');

type HoursDialogProps = {
  onClose?: () => void;
  location: Location;
};

function HoursDialog(props: HoursDialogProps) {
  const { onClose, location } = props;
  const { layout, gutters, borders, backgrounds, fonts } = useTheme();

  const getCurrentDay = () => {
    return DateTime.now().toFormat('ccc');
  };

  const isCurrentTimeInRange = (hours: string) => {
    if (!hours || typeof hours !== 'string' || !hours.includes('-')) {
      console.log('Invalid hours string:', hours);
      return false;
    }

    // Replace different dash characters with a consistent one
    const normalizedHours = hours.replace(/[-–—]/g, '-').replaceAll(' ', '');
    const [startStr, endStr] = normalizedHours.split('-');

    if (!startStr || !endStr) {
      console.log('Invalid hours format:', normalizedHours);
      return false;
    }

    const start = DateTime.fromFormat(startStr.trim(), 'h:mma');
    const end = DateTime.fromFormat(endStr.trim(), 'h:mma');

    if (!start.isValid || !end.isValid) {
      console.log('Invalid time format:', startStr, endStr);
      return false;
    }

    const now = DateTime.now();
    return now >= start && now <= end;
  };

  const renderItemRow = (day: string, hours: string) => {
    const currentDay = getCurrentDay();
    const isToday = day === currentDay;
    const isOpen = isCurrentTimeInRange(hours);

    return (
      <View style={[layout.row, gutters.gap_8, layout.alignContentCenter]}>
        <View
          style={[
            layout.w_76,
            isOpen ? backgrounds.lightGreen : backgrounds.lightCherry,
            borders.rounded_16,
            gutters.paddingTop_4,
            gutters.paddingRight_12,
            gutters.paddingBottom_4,
            gutters.paddingLeft_12,
            { opacity: isToday ? 1 : 0 },
          ]}
        >
          <Text
            style={[
              layout.itemsEnd,
              isOpen ? fonts.olive : fonts.cherry,
              fonts.size_12,
              {
                letterSpacing: 0.5,
              },
              fonts.alignCenter,
              fonts.appFontBold,
              fonts.uppercase,
            ]}
          >
            {isOpen ? 'open' : 'closed'}
          </Text>
        </View>
        <Text
          style={[
            layout.w_50,
            fonts.appFontBold,
            isToday ? fonts.charcoal : fonts.lightCharcoal,
            fonts.size_12,
            gutters.padding_4,
            fonts.alignCenter,
            fonts.uppercase,
          ]}
        >
          {day}
        </Text>
        <Text
          style={[
            isToday ? fonts.appFontBold : fonts.appFontRegular,
            fonts.size_12,
            gutters.padding_4,
            isToday ? fonts.charcoal : fonts.lightCharcoal,
          ]}
        >
          {hours}
        </Text>
      </View>
    );
  };

  const renderHoursView = (locationItem: Location) => (
    <View style={[layout.fullWidth, gutters.gap_8, layout.itemsCenter]}>
      <ClockIconSvg />
      <Text style={[fonts.size_24, fonts.appFontBold, fonts.charcoal]}>
        Gate Hours
      </Text>
      <View style={[layout.fullWidth, gutters.gap_8]}>
        <FlatList
          scrollEnabled={false}
          data={locationItem.gateHours}
          keyExtractor={item => item.day.toString()}
          renderItem={({ item }) => renderItemRow(item.day, item.hours)}
        />
      </View>
      <View style={[layout.h_1, layout.fullWidth, backgrounds.springGrass20]} />
      <Text style={[fonts.size_24, fonts.appFontBold, fonts.charcoal]}>
        Office Hours
      </Text>
      <View style={[layout.fullWidth, gutters.gap_8]}>
        <FlatList
          scrollEnabled={false}
          data={locationItem.officeHours}
          keyExtractor={item => item.day.toString()}
          renderItem={({ item }) => renderItemRow(item.day, item.hours)}
        />
      </View>
    </View>
  );
  return (
    <Modal transparent animationType="slide" onRequestClose={onClose}>
      <View
        style={[
          layout.fullWidth,
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <ScrollView
          contentContainerStyle={[
            layout.flexGrow_1,
            layout.justifyCenter,
            layout.itemsCenter,
          ]}
        >
          <View
            style={[
              { width: width - 32 },
              gutters.paddingTop_32,
              gutters.paddingRight_16,
              gutters.paddingBottom_32,
              gutters.paddingLeft_16,
              borders.rounded_12,
              layout.itemsCenter,
              backgrounds.white,
            ]}
          >
            {renderHoursView(location)}
            <View style={[layout.fullWidth, gutters.marginTop_16]}>
              <SSButton
                title="Back to homepage"
                variant="dark"
                onPress={onClose}
              />
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

export default HoursDialog;
