import { StorageIconSVG, StorageSuccessSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  Dimensions,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { addStorageAccount } from '@/services/api';
import { AddStorageAccountRequest } from '@/types/schemas/request';
import { useMutation } from '@tanstack/react-query';
import { logEventWithStatus, Events, EventStatus } from '@/utils/analytics';
import SSTextInput from '../atoms/SSTextInput/SSTextInput';
import { SSButton } from '../atoms';
import { digitsOnly } from '../../utils/commonFunctions';
import SSErrorView from '../atoms/SSErrorView';

const { width } = Dimensions.get('window');

type AddUnitDialogProps = {
  onClose?: () => void;
  onUnitAdded?: (value: boolean) => void;
  onNavigation: () => void;
};

function AddStorageAccountDialog(props: AddUnitDialogProps) {
  const { onClose, onUnitAdded, onNavigation } = props;
  const { layout, gutters, borders, backgrounds, fonts } = useTheme();
  const [isError, setError] = useState(false);
  const [isSuccess, setisSuccess] = useState(false);
  const [lNameValue, setLastName] = useState('');
  const [storageNumber, setStorageNumber] = useState('');

  const addStorageAccountApi = useMutation({
    mutationFn: (data: AddStorageAccountRequest) => {
      return addStorageAccount(data);
    },
    onSuccess: async () => {
      await logEventWithStatus(Events.addStorageUnit, EventStatus.completed);
      if (onUnitAdded) {
        onUnitAdded(true);
      }
      setisSuccess(true);
    },
    onError: async () => {
      await logEventWithStatus(Events.addStorageUnit, EventStatus.failed);
      setError(true);
    },
  });

  const addAccountClick = async () => {
    setError(false);
    await logEventWithStatus(Events.addStorageUnit, EventStatus.initiated);
    const addStorageAccountRequestData: AddStorageAccountRequest = {
      accountId: Number(storageNumber),
      lastName: lNameValue,
    };
    addStorageAccountApi.reset();
    await addStorageAccountApi.mutateAsync(addStorageAccountRequestData);
  };

  const renderAddStorageInputView = () => (
    <View style={[layout.fullWidth, gutters.gap_8, layout.itemsCenter]}>
      <StorageIconSVG />
      <Text style={[fonts.size_24, fonts.appFontBold, fonts.charcoal]}>
        Storage Account Number
      </Text>
      <Text
        style={[
          fonts.size_14,
          fonts.appFontRegular,
          fonts.charcoal,
          fonts.alignCenter,
        ]}
      >
        Please enter your storage account information to add your storage unit.
      </Text>
      <View
        style={[
          gutters.marginTop_12,
          gutters.marginBottom_12,
          layout.fullWidth,
          gutters.gap_8,
        ]}
      >
        {isError && (
          <SSErrorView
            title="Unable to find account"
            description="Sorry, we couldn’t find your account with the information you entered. Please double check your details and try again."
          />
        )}
        <View
          style={[
            gutters.marginTop_12,
            gutters.marginBottom_12,
            layout.h_60,
            gutters.gap_16,
          ]}
        >
          <SSTextInput
            label="Storage Account Number*"
            value={storageNumber}
            keyboardType="numeric"
            isError={isError}
            onChangeText={(value: string) => {
              setStorageNumber(digitsOnly(value));
            }}
          />
        </View>

        <Text style={[fonts.size_14, fonts.charcoal, fonts.appFontRegular]}>
          Can&apos;t find{' '}
          <Text
            style={[
              fonts.size_14,
              fonts.primary,
              fonts.appFontBold,
              fonts.underline,
            ]}
            onPress={onNavigation}
          >
            Storage Account Number
          </Text>
          ?
        </Text>
        <View
          style={[
            gutters.marginTop_12,
            gutters.marginBottom_12,
            layout.h_60,
            gutters.gap_16,
          ]}
        >
          <SSTextInput
            label="Last Name on Account*"
            isError={isError}
            value={lNameValue}
            onChangeText={(value: string) => {
              const sanitizedValue = value.slice(0, 50);
              setLastName(sanitizedValue);
            }}
            maxLength={50}
            autoCapitalize="words"
          />
        </View>
      </View>
      <View style={[layout.fullWidth, gutters.gap_16]}>
        <SSButton
          title="ADD storage unit"
          variant="dark"
          isProgress={addStorageAccountApi.isPending}
          isSuccess={addStorageAccountApi.isSuccess}
          onPress={() => {
            void addAccountClick();
          }}
        />
        <SSButton title="Cancel" variant="transparent" onPress={onClose} />
      </View>
    </View>
  );

  const renderAddStorageSuccessView = () => (
    <View
      style={[
        layout.fullWidth,
        gutters.gap_24,
        gutters.paddingTop_32,
        gutters.paddingRight_16,
        gutters.paddingBottom_32,
        gutters.paddingLeft_32,
        layout.itemsCenter,
      ]}
    >
      <StorageSuccessSVG />
      <Text style={[fonts.size_32, fonts.appFontBold, fonts.primary]}>
        Unit Added!
      </Text>
      <Text
        style={[
          fonts.size_14,
          fonts.appFontRegular,
          fonts.charcoal,
          fonts.alignCenter,
        ]}
      >
        This storage unit has been successfully added to your account.
      </Text>
      <View style={[layout.fullWidth, gutters.gap_16]}>
        <SSButton title="back to home" variant="dark" onPress={onClose} />
      </View>
    </View>
  );

  return (
    <Modal transparent animationType="slide" onRequestClose={onClose}>
      <View
        style={[
          layout.fullWidth,
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={[
              layout.flexGrow_1,
              layout.justifyCenter,
              layout.itemsCenter,
            ]}
          >
            <View
              style={[
                { width: width - 32 },
                gutters.padding_16,
                borders.rounded_12,
                layout.itemsCenter,
                backgrounds.white,
              ]}
            >
              <>
                {!isSuccess && renderAddStorageInputView()}
                {isSuccess && renderAddStorageSuccessView()}
              </>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
}

export default AddStorageAccountDialog;
