import useTheme from '@/theme/hooks/useTheme';
import { Modal, Text, TextStyle, View } from 'react-native';
import { AlertSuccess } from '@/assets/svg';
import { SSButton } from '../atoms';

type SSSuccessDialogProps = {
  onClose?: () => void;
  title: string;
  description: string;
  buttonTitle?: string;
};

function SSSuccessDialog(props: SSSuccessDialogProps) {
  const { layout, gutters, fonts, borders, backgrounds } = useTheme();
  const { onClose, title, description, buttonTitle = 'Go Back' } = props;
  const transparentBackgroundStyle = {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  };
  const otherTextStyles: TextStyle = {
    textAlign: 'center',
  };
  const renderSuccessIcon = () => (
    <View style={[gutters.marginTop_10]}>
      <AlertSuccess />
    </View>
  );

  const renderButtons = () => (
    <View style={[layout.fullWidth, gutters.gap_10, gutters.marginVertical_20]}>
      <View style={[layout.fullWidth]}>
        <SSButton title={buttonTitle} onPress={onClose} variant="dark" />
      </View>
    </View>
  );

  const renderTitle = () => (
    <Text
      style={[
        fonts.size_24,
        fonts.appFontBold,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      {title}
    </Text>
  );

  const renderDescription = () => (
    <Text
      style={[
        fonts.size_14,
        fonts.appFontRegular,
        fonts.charcoal,
        gutters.marginTop_10,
        otherTextStyles,
      ]}
    >
      {description}
    </Text>
  );

  const renderAlertContent = () => (
    <View
      style={[
        layout.fullWidth,
        layout.itemsCenter,
        borders.rounded_16,
        backgrounds.white,
        gutters.paddingHorizontal_20,
        gutters.paddingVertical_20,
      ]}
    >
      {renderSuccessIcon()}
      {renderTitle()}
      {renderDescription()}
      {renderButtons()}
    </View>
  );

  return (
    <Modal transparent animationType="slide" onRequestClose={onClose}>
      <View
        style={[
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          gutters.paddingHorizontal_10,
          transparentBackgroundStyle,
        ]}
      >
        {renderAlertContent()}
      </View>
    </Modal>
  );
}

export default SSSuccessDialog;
