import { AlertSVG } from '@/assets/svg';
import { useTheme } from '@/theme';
import React from 'react';
import { Modal, View, Text, Dimensions, ScrollView } from 'react-native';
import { SSButton } from '../atoms';

const { width } = Dimensions.get('window');

type SSAlertDialogProps = {
  title?: string;
  description?: string;
  positiveButtonLabel?: string;
  negativeButtonLabel: string;
  positiveButton?: () => void;
  negativeButton: () => void;
};

function SSAlertDialog(props: SSAlertDialogProps) {
  const {
    positiveButton,
    negativeButton,
    positiveButtonLabel,
    negativeButtonLabel,
    title,
    description,
  } = props;
  const { layout, gutters, borders, backgrounds, fonts } = useTheme();

  const renderView = () => (
    <View
      style={[
        layout.fullWidth,
        gutters.gap_24,
        gutters.paddingTop_32,
        gutters.padding_16,
        gutters.paddingBottom_32,
        layout.itemsCenter,
      ]}
    >
      <AlertSVG />
      {title && (
        <Text
          style={[
            fonts.size_24,
            fonts.appFontBold,
            fonts.charcoal,
            fonts.alignCenter,
          ]}
        >
          {title}
        </Text>
      )}
      {description && (
        <Text
          style={[
            fonts.size_14,
            fonts.appFontRegular,
            fonts.charcoal,
            fonts.alignCenter,
          ]}
        >
          {description}
        </Text>
      )}
      <View style={[layout.h_1, layout.fullWidth, backgrounds.springGrass20]} />
      <View style={[layout.fullWidth, gutters.gap_16]}>
        {positiveButtonLabel && (
          <SSButton
            title={positiveButtonLabel}
            variant="dark"
            onPress={positiveButton}
          />
        )}
        <SSButton
          title={negativeButtonLabel}
          variant="transparent"
          onPress={negativeButton}
        />
      </View>
    </View>
  );

  return (
    <Modal
      transparent
      animationType="slide"
      onRequestClose={positiveButton || negativeButton}
    >
      <View
        style={[
          layout.fullWidth,
          layout.flex_1,
          layout.justifyCenter,
          layout.itemsCenter,
          { backgroundColor: 'rgba(0, 0, 0, 0.5)' },
        ]}
      >
        <ScrollView
          contentContainerStyle={[
            layout.flexGrow_1,
            layout.justifyCenter,
            layout.itemsCenter,
          ]}
        >
          <View
            style={[
              { width: width - 32 },
              gutters.padding_16,
              borders.rounded_12,
              layout.itemsCenter,
              backgrounds.white,
            ]}
          >
            {renderView()}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

export default SSAlertDialog;
