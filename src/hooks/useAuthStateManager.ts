import { useEffect, useState, useCallback, useRef } from 'react';
import { AppState } from 'react-native';
import { DateTime } from 'luxon';
import { NavigationContainerRefWithCurrent } from '@react-navigation/native';

import { ApplicationStackParamList } from '@/types/navigation';
import { refreshTokensRequest } from '@/services/oauth';
import { ApiTokenResponse, TokenData } from '@/types/schemas/tokenData';

import {
  getTokenData,
  setTokenData,
  clearTokenData,
  tokenDataListener,
} from '@/utils/auth';
import { To } from '@react-navigation/native/lib/typescript/src/useLinkTo';
import Biometrics from '@/utils/biometrics';
import Adapter from '@/adapters/Adapter';
import { TokensAdapter } from '@/adapters/Tokens/TokensAdapter';
import { fetchAccountData } from '@/services/accounts';
import { AccountAdapter } from '@/adapters/Accounts/AccountAdapter';
import { AccountData, ApiAccountResponse } from '@/types/schemas/account';
import { PERMISSIONS_DIALOG_SHOWN, setUserInfoData } from '@/utils/storage';
import BleManager from 'react-native-ble-manager';
import { Events, EventStatus, logEventWithStatus } from '@/utils/analytics';
import useStorage from './useStorage';

const EXIRE_TIME_OFFSET = 10;

const useAuthStateManager = (
  navigation: NavigationContainerRefWithCurrent<ApplicationStackParamList>,
) => {
  const [currentTokenData, setCurrentTokenData] = useState<TokenData | null>(
    getTokenData(),
  );
  const [userInfo, setUserInfo] = useState<AccountData | null>();
  const [navReady, setNavReady] = useState(false);
  const [linkTo, setLinkTo] =
    useState<
      (
        to: To<
          ReactNavigation.RootParamList,
          keyof ReactNavigation.RootParamList
        >,
      ) => void
    >();
  const [deepLink, setDeepLink] = useState<string>();
  const appState = useRef(AppState.currentState);
  const globalStorage = useStorage('global-storage');

  // notify when nav container is fully mounted
  // Linking configuration causes this to take a little longer
  // no nav events can be dispatched until then
  const onNavReady = () => {
    setNavReady(true);
  };

  // function to dispatch navigation events based on Linking routes
  // adding here so we can defer deeplinks while we are waiting for auth
  const onLinkToCreated = (
    lt: (
      to: To<
        ReactNavigation.RootParamList,
        keyof ReactNavigation.RootParamList
      >,
    ) => void,
  ) => {
    if (!linkTo) {
      setLinkTo(() => lt);
    }
  };

  // user has interacted with a deep link
  // this function is being called from a Branch.io callback
  // so some of the available data is likely stale
  // throwing into state so we can have a useEffect listen for the relavant data
  const onDeepLinkOpened = (route: string) => {
    setDeepLink(route);
  };

  const attemptTokenRefresh = async (
    tokenData: TokenData,
  ): Promise<ApiTokenResponse> => {
    const response = await refreshTokensRequest(tokenData.refreshToken);
    return response;
  };

  const navigateToOnboarding = (): void => {
    const navState = navigation.getState();
    if (navState?.routes.find(route => route.name === 'Onboarding')) {
      // Prevent a navigation event if we are already in the Onboarding stack
      return;
    }

    navigation.reset({
      index: 0,
      routes: [{ name: 'Onboarding' }],
    });
  };

  const navigateToHome = (): void => {
    const navState = navigation.getState();
    if (navState?.routes.find(route => route.name === 'Main')) {
      // Prevent a navigation event if we are already in the Main stack
      return;
    }

    navigation.reset({
      index: 0,
      routes: [{ name: 'Main', params: { screen: 'Home' } }],
    });
  };

  /**
   * Verify if an access token exists in storage and is still valid (not expired).
   * If the current token has expired, this function will attempt to refresh the
   * access token.
   *
   * The function returns true if:
   *  - Token exists AND token is NOT expired.
   *  - Token expired and a successful refresh token request was made.
   *
   * The function return false if:
   *  - Token does not exist
   *  - Token has expired and attempting to refresh the token has failed.
   *
   * @returns void
   */
  const checkAccessToken = async (): Promise<void> => {
    const tokenData: TokenData | null = getTokenData();

    if (!tokenData) {
      return;
    }

    // Calculate an expiration sometime previous to the actual expiration to
    // give a chance to refresh the token before it becomes invalid.
    const expires = DateTime.fromISO(tokenData.expiresAt).minus({
      minutes: EXIRE_TIME_OFFSET,
    });

    if (expires <= DateTime.now()) {
      // Access token has expired. Attempt to refresh
      try {
        const refreshedTokenData = await attemptTokenRefresh(tokenData);
        const adaptedTokens = Adapter.from(refreshedTokenData)
          .to((data: ApiTokenResponse) => new TokensAdapter(data))
          .adapt();
        setTokenData(adaptedTokens);
      } catch (err) {
        console.warn('There was a problem refreshing the access token', err);
        // Introduce an Alert here maybe to let the user know what's going on?
        void clearTokenData();
      }
    }
  };

  const getUserInfo = async (): Promise<void> => {
    try {
      const userinfo = await fetchAccountData();
      const accountData: AccountData = Adapter.from(userinfo).to(
        (item: ApiAccountResponse) => new AccountAdapter(item).adapt(),
      );
      setUserInfoData(accountData);
      setUserInfo(accountData);
    } catch (err) {
      console.warn('There was a problem in fetching user data', err);
    }
  };

  const promptBiometrics = useCallback(async () => {
    try {
      const { success, error } = await Biometrics.simplePrompt({
        promptMessage: 'Unlock SmartStop',
        fallbackPromptMessage: 'Use Passcode',
        cancelButtonText: 'Log out',
      });

      if (!success) {
        void clearTokenData();
      }
      if (error) {
        console.error('Biometrics failed:', error);
      }
    } catch (err) {
      console.error('Biometrics prompt failure:', err);
      void clearTokenData();
    }
  }, []);

  /**
   * An effect that's puspose is to introduce a small delay before checking
   * for access token data. The delay is to give the splash screen some time to
   * be displayed for now until we introduce a pre-loader.
   */
  useEffect(() => {
    // This runs only the first time this component is mounted
    setTimeout(() => {
      void checkAccessToken();
    }, 2000);
  }, []);

  useEffect(() => {
    if (!deepLink) return;

    if (currentTokenData && linkTo) {
      // user already logged in
      // take them to the deeplink
      console.log('DEEPLINK: navigating', currentTokenData);
      linkTo(deepLink);
    } else {
      // user not logged in yet
      // save deep link route for after auth
      // keeping out of state so it doesn't trigger useEffect
      globalStorage.set('deepLinkRoute', deepLink);
      console.log('DEEPLINK: storing route', deepLink);
    }

    setDeepLink(undefined);
  }, [deepLink, currentTokenData, linkTo]);

  /**
   * An effect that monitors the Application State. When it has detected
   * the user is coming back into the app after being in the background,
   * it will check the access token for validity and attempt to refresh if
   * needed.
   */
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active' && appState.current.match(/background/)) {
        void checkAccessToken();
        if (
          globalStorage.getBoolean('bioEnabled') &&
          currentTokenData &&
          globalStorage.getBoolean(PERMISSIONS_DIALOG_SHOWN)
        ) {
          void promptBiometrics();
        }
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [currentTokenData]);

  /**
   * An effect that sets up the storage listener and updates the
   * currentTokenData
   */
  useEffect(() => {
    const listener = tokenDataListener(tokenData => {
      setCurrentTokenData(tokenData);
      if (tokenData?.accessToken) {
        void getUserInfo();
      } else {
        setUserInfo(null);
      }
    });

    return () => {
      listener.remove();
    };
  }, []);

  /**
   * An effect that monitors the access token expiration time. It will attempt
   * to refresh the token when the setTimeout expires.
   */
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | undefined;

    if (currentTokenData?.expiresAt) {
      const now = DateTime.now();
      // Calculate a later time than that within checkAccessToken to guarantee
      // the checkAccessToken function will trigger a token refresh.
      const expires = DateTime.fromISO(currentTokenData.expiresAt).minus({
        minutes: EXIRE_TIME_OFFSET - 1,
      });
      const timeDiff = expires.diff(now, 'milliseconds');
      const timeoutDuration = Math.max(timeDiff.as('milliseconds'), 0);
      timeoutId = setTimeout(() => {
        void checkAccessToken();
      }, timeoutDuration);
    }

    return () => {
      clearTimeout(timeoutId);
    };
  }, [currentTokenData?.accessToken]);

  useEffect(() => {
    if (currentTokenData) {
      void getUserInfo();
    }
  }, [currentTokenData]);

  const startBluetoothManager = () => {
    try {
      BleManager.start({ showAlert: false })
        .then(() => {
          console.debug('BleManager started.');
        })
        .catch((error: Error) =>
          console.error('BeManager could not be started.', error),
        );
    } catch (error) {
      console.error('unexpected error starting BleManager.', error);
    }
  };

  const returnLoginUser = async () => {
    await logEventWithStatus(Events.login, EventStatus.return);
  };

  /**
   * An effect that monitors the state of currentTokenData and will navigate
   * to the appropriate nav stack. If there is token data, the user will be
   * redirected to the Main stack. If the token data is removed, the user will
   * be redirected to the Onboarding stack.
   */
  useEffect(() => {
    if (navReady) {
      if (currentTokenData) {
        if (userInfo) {
          void returnLoginUser();
          void startBluetoothManager();
          navigateToHome();

          // check if we have a deeplink pending
          const deepLinkRoute = globalStorage.getString('deepLinkRoute');
          if (linkTo && deepLinkRoute) {
            globalStorage.delete('deepLinkRoute');
            setTimeout(() => {
              linkTo(deepLinkRoute);
            }, 200);
          }
        }
      } else {
        navigateToOnboarding();
      }
    }
  }, [currentTokenData, navReady, linkTo, userInfo]);

  return { currentTokenData, onNavReady, onLinkToCreated, onDeepLinkOpened };
};

export default useAuthStateManager;
