import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function AccessSystemUnavailable(props: SvgProps) {
  return (
    <Svg width={130} height={130} fill="none" {...props}>
      <Path
        d="M126.113 107.89L72.5425 15.1101C68.6125 8.29012 62.1725 8.29012 58.2525 15.1101L4.68255 107.89C0.742549 114.7 3.96255 120.27 11.8325 120.27H118.963C126.823 120.27 130.043 114.7 126.113 107.89ZM61.8725 50.5201H68.9126C70.0326 50.5201 70.9025 51.5001 70.8425 52.7101L69.5025 82.4401C69.4525 83.6401 68.4926 84.6301 67.3726 84.6301H63.4725C62.3526 84.6301 61.3926 83.6501 61.3326 82.4401L59.9325 52.7101C59.8725 51.5101 60.7425 50.5201 61.8625 50.5201H61.8725ZM65.4325 102.39C61.6625 102.39 59.1425 99.6701 59.1425 96.0301C59.1425 92.3901 61.7325 89.6701 65.4325 89.6701C69.1325 89.6701 71.5826 92.3201 71.6525 96.0301C71.6525 99.6701 69.2125 102.39 65.4325 102.39Z"
        fill="#99CBE7"
      />
    </Svg>
  );
}

export default AccessSystemUnavailable;
