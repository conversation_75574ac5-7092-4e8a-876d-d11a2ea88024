import { useTheme } from '@/theme';
import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function PlusIconSVG(props: SvgProps) {
  const { backgrounds } = useTheme();
  const { fill = backgrounds.blue.backgroundColor } = props;
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1429 3.14286C11.1429 2.51167 10.6312 2 10 2C9.36882 2 8.85714 2.51167 8.85714 3.14286V8.85714H3.14286C2.51167 8.85714 2 9.36882 2 10C2 10.6312 2.51167 11.1429 3.14286 11.1429H8.85714V16.8571C8.85714 17.4883 9.36882 18 10 18C10.6312 18 11.1429 17.4883 11.1429 16.8571V11.1429H16.8571C17.4883 11.1429 18 10.6312 18 10C18 9.36882 17.4883 8.85714 16.8571 8.85714H11.1429V3.14286Z"
        fill={fill}
      />
    </Svg>
  );
}

export default PlusIconSVG;
