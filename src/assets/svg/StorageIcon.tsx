import * as React from 'react';
import Svg, {
  Circle,
  G,
  <PERSON>,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function StorageIconSVG(props: SvgProps) {
  return (
    <Svg width={64} height={64} viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx={32} cy={32} r={24} fill="#0077B8" />
      <Circle
        cx={32}
        cy={32}
        r={28}
        stroke="#006CAF"
        strokeOpacity={0.3}
        strokeWidth={8}
      />
      <G clipPath="url(#clip0_5722_1342)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.4 25.657c0-1.136.92-2.057 2.057-2.057h15.086c1.136 0 2.057.921 2.057 2.057v2.057a2.057 2.057 0 01-2.057 2.058H24.457a2.057 2.057 0 01-2.057-2.058v-2.057zm17.828 5.829H23.771v7.885c0 1.137.921 2.058 2.057 2.058h12.343a2.057 2.057 0 002.057-2.058v-7.885zm-10.285 2.228a.857.857 0 100 1.715h4.114a.857.857 0 000-1.715h-4.114z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5722_1342">
          <Path fill="#fff" transform="translate(20 20)" d="M0 0H24V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default StorageIconSVG;
