import * as React from 'react';
import Svg, {
  G,
  Path,
  Circle,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function StorageSuccessSVG(props: SvgProps) {
  return (
    <Svg width={188} height={180} viewBox="0 0 188 180" fill="none" {...props}>
      <G clipPath="url(#clip0_5722_20381)">
        <Path
          d="M166.258 44.136l-75.74 44.136L14.26 44.136 90.017 0l76.24 44.136z"
          fill="#E7C097"
        />
        <Path
          d="M166.258 44.136l-.259 91.728L90.259 180l.26-91.728 75.739-44.136z"
          fill="#D2A26F"
        />
        <Path
          d="M90.518 88.272L90.258 180 14 135.864l.26-91.728 76.258 44.136z"
          fill="#B88044"
        />
        <Path
          d="M58.302 18.472l-12.364 7.192L122.197 69.8l12.364-7.192-76.259-44.136z"
          fill="#C08D59"
        />
        <Path
          d="M134.728 87.305l-.167-24.734-12.364 7.229.167 24.697 12.364-7.192z"
          fill="#C08D59"
        />
      </G>
      <Circle cx={158.333} cy={45.3333} r={21.3333} fill="#497C5C" />
      <Circle
        cx={158.333}
        cy={45.3333}
        r={25.3333}
        stroke="#497C5C"
        strokeOpacity={0.3}
        strokeWidth={8}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M167.436 37.247c.583.483.666 1.347.183 1.931l-11.026 13.343-.003.004a2.64 2.64 0 01-3.205.671 2.63 2.63 0 01-.908-.747l-.003-.003-3.452-4.438a1.371 1.371 0 012.165-1.684l3.379 4.344 10.939-13.237a1.372 1.372 0 011.931-.184z"
        fill="#fff"
      />
      <Defs>
        <ClipPath id="clip0_5722_20381">
          <Path fill="#fff" transform="translate(14)" d="M0 0H152.258V180H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default StorageSuccessSVG;
