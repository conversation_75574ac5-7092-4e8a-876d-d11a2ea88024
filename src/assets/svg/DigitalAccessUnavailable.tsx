import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function DigitalAccessUnavailable(props: SvgProps) {
  return (
    <Svg width={130} height={130} fill="none" {...props}>
      <Path
        d="M65 3C42.3564 3 24 21.4856 24 44.2891C24 51.8066 25.9954 58.8554 29.481 64.9269C35.2591 74.9896 55.2183 109.893 62.3999 122.452C63.5518 124.467 66.4482 124.467 67.6001 122.452C74.7817 109.893 94.7409 74.9896 100.519 64.9269C104.005 58.8554 106 51.8066 106 44.2891C106 21.4856 87.6436 3 65 3ZM65 69.2773C51.296 69.2773 40.1861 58.0892 40.1861 44.2891C40.1861 30.4889 51.2953 19.3008 65 19.3008C78.7047 19.3008 89.8139 30.4889 89.8139 44.2891C89.8139 58.0892 78.704 69.2773 65 69.2773ZM95.4793 44.2891C95.4793 61.263 81.8332 75.0233 65 75.0233C48.1668 75.0233 34.5206 61.263 34.5206 44.2891C34.5206 27.3151 48.1668 13.5548 65 13.5548C81.8332 13.5548 95.4793 27.3151 95.4793 44.2891ZM79.6438 34.9676L75.0295 30.3147L65.7437 39.6782L56.4579 30.3147L51.8435 34.9684L61.1293 44.3319L51.8435 53.6953L56.4579 58.3483L65.7437 48.9848L75.0295 58.3483L79.6438 53.6953L70.358 44.3319L79.6438 34.9676Z"
        fill="#99CBE7"
      />
    </Svg>
  );
}

export default DigitalAccessUnavailable;
