import React from 'react';
import Svg, { SvgProps, Path, G, Defs, Rect, ClipPath } from 'react-native-svg';

function DownArrowSVG({ color, ...props }: SvgProps) {
  const fillColor = color || '#0077B8';
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#clip0_5383_1837)">
        <Path
          d="M17.5867 8.09979L11.0604 14.55C10.919 14.6926 10.7507 14.8058 10.5654 14.883C10.38 14.9602 10.1811 15 9.98031 15C9.77949 15 9.58065 14.9602 9.39527 14.883C9.20989 14.8058 9.04163 14.6926 8.90021 14.55L2.44999 8.09979C2.3074 7.95837 2.19423 7.79011 2.117 7.60473C2.03976 7.41935 2 7.22051 2 7.01969C2 6.81886 2.03976 6.62002 2.117 6.43464C2.19423 6.24926 2.3074 6.081 2.44999 5.93958C2.73502 5.65624 3.12059 5.4972 3.52249 5.4972C3.92439 5.4972 4.30996 5.65624 4.59499 5.93958L9.98031 11.3249L15.3656 5.93958C15.649 5.65853 16.0314 5.50009 16.4305 5.49841C16.6307 5.49725 16.8292 5.53562 17.0146 5.61133C17.1999 5.68703 17.3685 5.79858 17.5106 5.93958C17.6583 6.07591 17.7775 6.24016 17.8613 6.42282C17.9452 6.60549 17.992 6.80296 17.9991 7.00383C18.0061 7.20469 17.9733 7.40496 17.9026 7.59307C17.8318 7.78118 17.7244 7.95341 17.5867 8.09979Z"
          fill={fillColor}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5383_1837">
          <Rect width="20" height="20" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default DownArrowSVG;
