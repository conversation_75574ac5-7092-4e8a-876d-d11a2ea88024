import React from 'react';
import Svg, { SvgProps, <PERSON>, Defs, G, Clip<PERSON><PERSON>, Rect } from 'react-native-svg';

function BiomatricInputSVG(props: SvgProps) {
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#clip0_5270_6878)">
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M2 4.25641C2 3.01023 3.01023 2 4.25641 2H5.89743C6.2373 2 6.51281 2.27552 6.51281 2.61538C6.51281 2.95525 6.2373 3.23077 5.89743 3.23077H4.25641C3.68996 3.23077 3.23077 3.68996 3.23077 4.25641V5.89743C3.23077 6.2373 2.95525 6.51281 2.61538 6.51281C2.27552 6.51281 2 6.2373 2 5.89743V4.25641Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.4871 2.61538C13.4871 2.27552 13.7626 2 14.1025 2H15.7435C16.9897 2 17.9999 3.01023 17.9999 4.25641V5.89743C17.9999 6.2373 17.7244 6.51281 17.3846 6.51281C17.0447 6.51281 16.7692 6.2373 16.7692 5.89743V4.25641C16.7692 3.68996 16.31 3.23077 15.7435 3.23077H14.1025C13.7626 3.23077 13.4871 2.95525 13.4871 2.61538Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.2821 6.10258C13.6219 6.10258 13.8975 6.3781 13.8975 6.71796V8.35899C13.8975 8.69885 13.6219 8.97437 13.2821 8.97437C12.9422 8.97437 12.6667 8.69885 12.6667 8.35899V6.71796C12.6667 6.3781 12.9422 6.10258 13.2821 6.10258Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.7179 6.10258C7.05777 6.10258 7.33329 6.3781 7.33329 6.71796V8.35899C7.33329 8.69885 7.05777 8.97437 6.7179 8.97437C6.37804 8.97437 6.10252 8.69885 6.10252 8.35899V6.71796C6.10252 6.3781 6.37804 6.10258 6.7179 6.10258Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M7.96905 12.8424C7.72838 12.6066 7.34212 12.6081 7.10331 12.8469C6.86299 13.0872 6.86299 13.4768 7.10331 13.7172L7.53845 13.282C7.10331 13.7172 7.10309 13.7169 7.10331 13.7172L7.1042 13.718L7.10517 13.719L7.10735 13.7212C7.10891 13.7227 7.11069 13.7244 7.11268 13.7264C7.11665 13.7302 7.12149 13.7348 7.12718 13.7402C7.13857 13.7508 7.1534 13.7644 7.17168 13.7804C7.20823 13.8124 7.25868 13.8542 7.32307 13.9025C7.45176 13.999 7.63688 14.1218 7.87863 14.2427C8.36414 14.4854 9.07316 14.7179 9.99999 14.7179C10.9268 14.7179 11.6358 14.4854 12.1213 14.2427C12.3631 14.1218 12.5482 13.999 12.6769 13.9025C12.7413 13.8542 12.7917 13.8124 12.8283 13.7804C12.8466 13.7644 12.8614 13.7508 12.8728 13.7402C12.8785 13.7348 12.8833 13.7302 12.8873 13.7264C12.8893 13.7244 12.8911 13.7227 12.8926 13.7212L12.8948 13.719L12.8958 13.718C12.896 13.7178 12.8967 13.7172 12.4768 13.2973L12.8967 13.7172C13.137 13.4768 13.137 13.0872 12.8967 12.8469C12.6579 12.6081 12.2716 12.6066 12.0309 12.8424C12.0294 12.8438 12.025 12.8478 12.0178 12.8541C12.0031 12.867 11.9766 12.8893 11.9384 12.9179C11.862 12.9752 11.7394 13.0576 11.5709 13.1419C11.2359 13.3094 10.7142 13.4871 9.99999 13.4871C9.28579 13.4871 8.76405 13.3094 8.42905 13.1419C8.26054 13.0576 8.13796 12.9752 8.06153 12.9179C8.02336 12.8893 7.99688 12.867 7.98215 12.8541C7.97494 12.8478 7.97057 12.8438 7.96905 12.8424ZM12.0272 12.846C12.0272 12.8461 12.0273 12.846 12.0272 12.846V12.846Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9.99995 6.10258C10.3398 6.10258 10.6153 6.3781 10.6153 6.71796V10.8205C10.6153 11.1604 10.3398 11.4359 9.99995 11.4359H9.17944C8.83957 11.4359 8.56405 11.1604 8.56405 10.8205C8.56405 10.4807 8.83957 10.2051 9.17944 10.2051H9.38457V6.71796C9.38457 6.3781 9.66008 6.10258 9.99995 6.10258Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M2.61538 13.4872C2.95525 13.4872 3.23077 13.7627 3.23077 14.1026V15.7436C3.23077 16.3101 3.68996 16.7692 4.25641 16.7692H5.89743C6.2373 16.7692 6.51281 17.0447 6.51281 17.3846C6.51281 17.7245 6.2373 18 5.89743 18H4.25641C3.01023 18 2 16.9898 2 15.7436V14.1026C2 13.7627 2.27552 13.4872 2.61538 13.4872Z"
          fill="#006CAF"
        />
        <Path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.3846 13.4872C17.7244 13.4872 17.9999 13.7627 17.9999 14.1026V15.7436C17.9999 16.9898 16.9897 18 15.7435 18H14.1025C13.7626 18 13.4871 17.7245 13.4871 17.3846C13.4871 17.0447 13.7626 16.7692 14.1025 16.7692H15.7435C16.31 16.7692 16.7692 16.3101 16.7692 15.7436V14.1026C16.7692 13.7627 17.0447 13.4872 17.3846 13.4872Z"
          fill="#006CAF"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5270_6878">
          <Rect width="20" height="20" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default BiomatricInputSVG;
