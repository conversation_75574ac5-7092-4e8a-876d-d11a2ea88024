import * as React from 'react';
import Svg, {
  Circle,
  G,
  <PERSON>,
  De<PERSON>,
  <PERSON><PERSON><PERSON>ath,
  SvgProps,
} from 'react-native-svg';

function RoundedLocationSVG(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Circle cx={16} cy={16} r={16} fill="#0077B8" />
      <G clipPath="url(#clip0_7145_20635)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.99 9.6c2.397 0 4.394 1.997 4.394 4.394 0 .782-.268 1.553-.644 2.252-.377.702-.877 1.357-1.373 1.915-.497.558-1 1.029-1.39 1.361-.196.166-.367.3-.499.396a2.615 2.615 0 01-.179.12.967.967 0 01-.************* 0 01-.37-.027.942.942 0 01-.09-.05 2.662 2.662 0 01-.18-.12 8.35 8.35 0 01-.497-.398 14.5 14.5 0 01-1.39-1.368c-.496-.56-.995-1.215-1.372-1.915-.376-.697-.645-1.465-.645-2.241 0-2.397 1.997-4.394 4.394-4.394zm0 5.706a1.312 1.312 0 110-2.624 1.312 1.312 0 010 2.624zm2.834 3.58c.169-.19.337-.389.503-.595h1.228c.183 0 .348.109.42.277l1.37 3.195a.457.457 0 01-.42.637H10.056a.457.457 0 01-.42-.637l1.37-3.195a.457.457 0 01.42-.277h1.238a17.612 17.612 0 002.183 2.246c.238.204.446.368.604.483.**************.**************.*************.**************.073.029a.5.5 0 00.378-.028c.036-.017.071-.037.1-.055.062-.036.134-.085.213-.142.158-.114.366-.277.604-.479a17.39 17.39 0 001.692-1.657z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_7145_20635">
          <Path fill="#fff" transform="translate(8 8)" d="M0 0H16V16H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RoundedLocationSVG;
