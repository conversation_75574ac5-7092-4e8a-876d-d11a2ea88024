import * as React from 'react';
import Svg, { SvgProps, Path, G, Defs, ClipPath } from 'react-native-svg';

function CheckSmallSVG(props: SvgProps) {
  return (
    <Svg width={18} height={18} viewBox="0 0 8 8" fill="none" {...props}>
      <G clipPath="url(#clip0_5459_6710)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.034 1.305c.195.16.222.449.061.644L3.42 6.396l-.001.002a.879.879 0 01-1.371-.026h-.001l-1.15-1.48a.457.457 0 11.721-.561l1.126 1.447L6.39 1.367a.457.457 0 01.644-.061z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5459_6710">
          <Path fill="#fff" d="M0 0H8V8H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default CheckSmallSVG;
