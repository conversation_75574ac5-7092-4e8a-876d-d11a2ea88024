import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface GateSvgProps extends SvgProps {
  color?: string;
  width?: number; // Explicitly define the type for the width prop
}

function GateSvg({ color, width, ...props }: GateSvgProps) {
  const fillColor = color || '#fff';
  const fillSize: number = width ?? 15;
  return (
    <Svg
      width={fillSize}
      height={fillSize + 1}
      viewBox="0 0 15 16"
      fill="none"
      {...props}
    >
      <Path
        d="M13.27 0c-.46 0-.83.37-.83.83v.52H1.66V.83C1.66.37 1.29 0 .83 0 .37 0 0 .37 0 .83v13.81c0 .46.37.83.83.83.46 0 .83-.37.83-.83v-.52h10.78v.52c0 .46.37.83.83.83.46 0 .83-.37.83-.83V.83c0-.46-.37-.83-.83-.83zM7.88 3.01h1.45v1.47H7.88V3.01zm-4.77 9.45H1.66V3.01h1.45v9.45zm3.11 0H4.77V3.01h1.45v9.45zm3.11 0H7.88v-1.47h1.45v1.47zm1.63-3.53c0 .48-.32.8-.8.8-.48 0-.8-.32-.8-.8V7.81c-.48-.48-.56-1.2-.08-1.68.48-.48 1.2-.56 1.68-.08.48.4.56 1.2.08 1.68l-.08.08v1.12zm1.48 3.53h-1.45v-1.47h1.45v1.47zm0-7.98h-1.45V3.01h1.45v1.47z"
        fill={fillColor}
      />
    </Svg>
  );
}

export default GateSvg;
