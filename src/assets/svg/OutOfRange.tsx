import React from 'react';
import Svg, { SvgProps, Path, G, Defs, ClipPath, Rect } from 'react-native-svg';

function OutOfRange(props: SvgProps) {
  return (
    <Svg width={130} height={130} fill="none" {...props}>
      <Path
        d="M76.6725 114.251V59.5151C76.6725 58.4875 77.2431 57.5518 78.1436 57.0693C87.2359 52.1945 93.3465 42.424 92.9875 31.2499C92.5228 16.8719 81.0547 4.94801 66.7896 4.05617C50.516 3.03693 37 16.0218 37 32.1856C37 42.9754 43.0276 52.3428 51.8751 57.0798C52.7694 57.5581 53.3296 58.4958 53.3296 59.5151V67.2597C53.3296 67.6878 53.4977 68.0993 53.8006 68.4021L59.9777 74.6199C60.6043 75.2507 60.6043 76.2741 59.9777 76.9049L54.4667 82.4522C53.84 83.083 53.84 84.1064 54.4667 84.7372L59.9777 90.2845C60.6043 90.9153 60.6043 91.9387 59.9777 92.5695L54.4667 98.1168C53.84 98.7476 53.84 99.771 54.4667 100.402L59.9777 105.949C60.6043 106.58 60.6043 107.603 59.9777 108.234L54.4667 113.781C53.84 114.412 53.84 115.436 54.4667 116.066L63.8681 125.53C64.4948 126.161 65.5115 126.161 66.1381 125.53L76.2056 115.396C76.5065 115.093 76.6766 114.682 76.6766 114.253L76.6725 114.251ZM58.3779 21.1222C58.3779 17.44 61.343 14.4554 65.001 14.4554C68.6591 14.4554 71.6242 17.44 71.6242 21.1222C71.6242 24.8044 68.6591 27.7891 65.001 27.7891C61.343 27.7891 58.3779 24.8044 58.3779 21.1222Z"
        fill="#99CBE7"
      />
      <G clipPath="url(#clip0_193_8117)">
        <Path
          d="M119.661 51.4462L114.859 48.8422C117.895 43.0693 119.383 36.5532 119.171 29.9942C118.954 23.4351 117.036 17.0296 113.625 11.4839L118.246 8.56055C122.159 14.9354 124.361 22.2805 124.609 29.8161C124.857 37.3455 123.145 44.8257 119.661 51.4524V51.4462Z"
          fill="#99CBE7"
        />
        <Path
          d="M110.268 47.6628L105.466 45.0588C107.849 40.5265 109.022 35.4106 108.847 30.258C108.678 25.1053 107.172 20.0816 104.492 15.7212L109.113 12.7979C112.295 17.9812 114.085 23.9507 114.285 30.0737C114.484 36.1967 113.093 42.2768 110.262 47.6628H110.268Z"
          fill="#99CBE7"
        />
        <Path
          d="M100.869 43.8859L96.067 41.282C97.7969 37.9902 98.6497 34.2746 98.5227 30.5283C98.4017 26.7882 97.307 23.134 95.3594 19.9712L99.9803 17.0479C102.43 21.0398 103.809 25.6336 103.96 30.3502C104.117 35.0607 103.047 39.7405 100.863 43.8921L100.869 43.8859Z"
          fill="#99CBE7"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_193_8117">
          <Rect
            width="30"
            height="44"
            fill="white"
            transform="translate(95 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default OutOfRange;
