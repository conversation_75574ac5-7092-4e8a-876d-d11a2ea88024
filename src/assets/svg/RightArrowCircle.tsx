import React from 'react';
import Svg, {
  SvgProps,
  Path,
  G,
  Defs,
  Rect,
  ClipPath,
  Circle,
} from 'react-native-svg';

function RightArrowCircleSVG(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Circle cx="16" cy="16" r="16" fill="#0077B8" />
      <G clipPath="url(#clip0_5554_8112)">
        <Path
          d="M19.6401 16.8632L14.4644 22.0389C14.3509 22.1533 14.2159 22.2442 14.0671 22.3061C13.9184 22.3681 13.7588 22.4 13.5977 22.4C13.4365 22.4 13.277 22.3681 13.1282 22.3061C12.9795 22.2442 12.8445 22.1533 12.731 22.0389C12.5036 21.8102 12.376 21.5008 12.376 21.1783C12.376 20.8558 12.5036 20.5465 12.731 20.3177L17.0522 15.9965L12.731 11.6752C12.5036 11.4465 12.376 11.1371 12.376 10.8146C12.376 10.4921 12.5036 10.1827 12.731 9.95403C12.845 9.8409 12.9803 9.75139 13.129 9.69064C13.2778 9.62989 13.437 9.5991 13.5977 9.60003C13.7583 9.5991 13.9176 9.62989 14.0663 9.69064C14.215 9.75139 14.3503 9.8409 14.4644 9.95403L19.6401 15.1298C19.7545 15.2433 19.8453 15.3783 19.9073 15.527C19.9693 15.6758 20.0012 15.8353 20.0012 15.9965C20.0012 16.1576 19.9693 16.3172 19.9073 16.4659C19.8453 16.6147 19.7545 16.7497 19.6401 16.8632Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5554_8112">
          <Rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RightArrowCircleSVG;
