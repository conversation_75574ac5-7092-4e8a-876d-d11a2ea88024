import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function WarningSVG(props: SvgProps) {
  return (
    <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.404.502A1.714 1.714 0 015.616 0h4.768c.454 0 .89.18 1.212.502l3.902 3.902c.321.322.502.758.502 1.212v4.768c0 .454-.18.89-.502 1.212l-3.902 3.902a1.714 1.714 0 01-1.212.502H5.616c-.454 0-.89-.18-1.212-.502L.502 11.596A1.714 1.714 0 010 10.384V5.616c0-.454.18-.89.502-1.212L4.404.502zM8 3.572c.473 0 .857.383.857.857v3.714a.857.857 0 01-1.714 0V4.429c0-.474.384-.858.857-.858zm1.143 7.714a1.143 1.143 0 11-2.286 0 1.143 1.143 0 012.286 0z"
        fill="#9C0A0A"
      />
    </Svg>
  );
}

export default WarningSVG;
