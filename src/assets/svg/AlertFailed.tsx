import React from 'react';
import Svg, {
  SvgProps,
  Path,
  Circle,
  G,
  Defs,
  Rect,
  ClipPath,
} from 'react-native-svg';

function AlertFailed(props: SvgProps) {
  return (
    <Svg width="64" height="64" viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx="32" cy="32" r="24" fill="#9C0A0A" />
      <Circle
        cx="32"
        cy="32"
        r="28"
        stroke="#9C0A0A"
        strokeOpacity="0.3"
        strokeWidth="8"
      />
      <G clipPath="url(#clip0_65_19371)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M27.6853 23.0025C28.0711 22.6168 28.5943 22.4 29.1399 22.4H34.8609C35.4065 22.4 35.9297 22.6168 36.3155 23.0025L40.9979 27.6849C41.3837 28.0707 41.6004 28.5939 41.6004 29.1395V34.8605C41.6004 35.4061 41.3837 35.9294 40.9979 36.3151L36.3155 40.9976C35.9297 41.3833 35.4065 41.6 34.8609 41.6H29.1399C28.5943 41.6 28.0711 41.3833 27.6853 40.9976L23.0029 36.3151C22.6171 35.9294 22.4004 35.4061 22.4004 34.8605V29.1395C22.4004 28.5939 22.6171 28.0707 23.0029 27.6849L27.6853 23.0025ZM32.0004 26.6857C32.5684 26.6857 33.029 27.1463 33.029 27.7143V32.1715C33.029 32.7395 32.5684 33.2 32.0004 33.2C31.4323 33.2 30.9718 32.7395 30.9718 32.1715V27.7143C30.9718 27.1463 31.4323 26.6857 32.0004 26.6857ZM33.3718 35.9429C33.3718 36.7003 32.7578 37.3143 32.0004 37.3143C31.243 37.3143 30.629 36.7003 30.629 35.9429C30.629 35.1855 31.243 34.5715 32.0004 34.5715C32.7578 34.5715 33.3718 35.1855 33.3718 35.9429Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_65_19371">
          <Rect
            width="24"
            height="24"
            fill="white"
            transform="translate(20 20)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default AlertFailed;
