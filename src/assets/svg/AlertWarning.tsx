import React from 'react';
import Svg, {
  SvgProps,
  Path,
  Circle,
  G,
  Defs,
  Rect,
  ClipPath,
} from 'react-native-svg';

function AlertWarning(props: SvgProps) {
  return (
    <Svg width="64" height="64" viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx="32" cy="32" r="24" fill="#FAAE4C" />
      <Circle
        cx="32"
        cy="32"
        r="28"
        stroke="#FAAE4C"
        strokeOpacity="0.3"
        strokeWidth="8"
      />
      <G clipPath="url(#clip0_5765_14763)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M30.9265 22.702C31.2496 22.5044 31.6211 22.3999 31.9998 22.3999C32.3786 22.3999 32.75 22.5044 33.0732 22.702C33.3964 22.8995 33.6588 23.1824 33.8315 23.5195L33.8345 23.5255L41.3796 38.6158C41.5371 38.9287 41.6127 39.2779 41.598 39.628C41.5833 39.9786 41.4793 40.3195 41.2957 40.6185C41.1122 40.9175 40.8553 41.1646 40.5494 41.3363C40.2435 41.5081 39.8988 41.5988 39.5479 41.5999H39.5459H24.4538H24.4517C24.1009 41.5988 23.7562 41.5081 23.4503 41.3363C23.1444 41.1646 22.8875 40.9175 22.7039 40.6185C22.5204 40.3195 22.4164 39.9786 22.4017 39.628C22.387 39.2779 22.462 38.9298 22.6195 38.6169L30.1652 23.5255L30.1682 23.5195C30.3409 23.1824 30.6033 22.8995 30.9265 22.702ZM31.9998 28.0514C32.5681 28.0514 33.0288 28.5121 33.0288 29.0804V33.5395C33.0288 34.1078 32.5681 34.5685 31.9998 34.5685C31.4315 34.5685 30.9708 34.1078 30.9708 33.5395V29.0804C30.9708 28.5121 31.4315 28.0514 31.9998 28.0514ZM33.3718 37.3125C33.3718 38.0703 32.7575 38.6845 31.9998 38.6845C31.2421 38.6845 30.6278 38.0703 30.6278 37.3125C30.6278 36.5547 31.2421 35.9405 31.9998 35.9405C32.7575 35.9405 33.3718 36.5547 33.3718 37.3125Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5765_14763">
          <Rect
            width="24"
            height="24"
            fill="white"
            transform="translate(20 20)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default AlertWarning;
