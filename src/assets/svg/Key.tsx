import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function KeySVG({ color, ...props }: SvgProps) {
  const fillColor = color || '#B7D5C2';
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        fill={fillColor}
        fillRule="evenodd"
        d="M14.704 1.846a1.429 1.429 0 0 1 2.02 0l2.858 2.857a1.429 1.429 0 0 1-2.02 2.02l-1.848-1.846-1.55 1.55 1.846 1.848a1.429 1.429 0 1 1-2.02 2.02l-1.847-1.847-1.506 1.506a5.714 5.714 0 1 1-2.02-2.02l2.51-2.511.006-.005.005-.006 3.566-3.566ZM2.857 12.856a2.857 2.857 0 1 1 5.714 0 2.857 2.857 0 0 1-5.714 0Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}
export default KeySVG;
