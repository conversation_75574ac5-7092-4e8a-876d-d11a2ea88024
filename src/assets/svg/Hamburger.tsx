import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

interface HamburgerMenuSVGProps {
  svgProps?: SvgProps;
  color?: string;
}

function HamburgerMenuSVG({ svgProps, color }: HamburgerMenuSVGProps) {
  return (
    <Svg width={22} height={22} fill="none" {...svgProps}>
      <Path
        fill={color ?? '#fff'}
        d="M2.75 7.333h16.5a.917.917 0 0 0 0-1.833H2.75a.917.917 0 0 0 0 1.833Zm16.5 7.334H2.75a.916.916 0 1 0 0 1.833h16.5a.917.917 0 0 0 0-1.833Zm0-4.584H2.75a.917.917 0 0 0 0 1.834h16.5a.917.917 0 0 0 0-1.834Z"
      />
    </Svg>
  );
}

export default HamburgerMenuSVG;
