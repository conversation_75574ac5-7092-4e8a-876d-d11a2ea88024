import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function UpArrowSVG(props: SvgProps) {
  const { color } = props;
  return (
    <Svg width={16} height={10} viewBox="0 0 16 10" fill="none" {...props}>
      <Path
        d="M15.551 9.559a1.517 1.517 0 01-2.14 0L7.962 4.186 2.589 9.559A1.518 1.518 0 11.45 7.403L6.884.968a1.518 1.518 0 012.156 0l6.511 6.435a1.518 1.518 0 010 2.156z"
        fill={color || '#fff'}
      />
    </Svg>
  );
}

export default UpArrowSVG;
