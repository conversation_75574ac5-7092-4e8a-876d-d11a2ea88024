import React from 'react';
import Svg, {
  SvgProps,
  Path,
  G,
  Defs,
  Rect,
  ClipPath,
  Circle,
} from 'react-native-svg';

function EditIconSVG(props: SvgProps) {
  return (
    <Svg width={32} height={32} fill="none" {...props}>
      <Circle cx="16" cy="16" r="16" fill="#0077B8" />
      <G clipPath="url(#clip0_5765_11319)">
        <Path
          d="M19.3996 9.6001C19.2169 9.6001 19.036 9.6366 18.8675 9.70745C18.7001 9.77786 18.5484 9.8808 18.4211 10.0103L10.8872 17.5049C10.8312 17.5606 10.7907 17.6297 10.7694 17.7057L9.61704 21.8197C9.5725 21.9787 9.61723 22.1494 9.73404 22.2662C9.85086 22.383 10.0216 22.4277 10.1807 22.3832L14.2962 21.2312C14.3722 21.2099 14.4414 21.1693 14.497 21.1135L21.9943 13.5824L21.9956 13.5811C22.1235 13.4539 22.2251 13.3027 22.2946 13.1361C22.3642 12.969 22.4001 12.7897 22.4001 12.6087C22.4001 12.4277 22.3642 12.2484 22.2946 12.0813C22.2251 11.9147 22.1235 11.7635 21.9956 11.6363L21.9943 11.6351L20.379 10.0112C20.2516 9.8813 20.0995 9.77804 19.9317 9.70745C19.7632 9.6366 19.5823 9.6001 19.3996 9.6001Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5765_11319">
          <Rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default EditIconSVG;
