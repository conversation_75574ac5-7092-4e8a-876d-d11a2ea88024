import * as React from 'react';
import Svg, {
  Circle,
  G,
  Path,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function ClockIconSvg(props: SvgProps) {
  return (
    <Svg width={64} height={64} viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx={32} cy={32} r={24} fill="#0077B8" />
      <Circle
        cx={32}
        cy={32}
        r={28}
        stroke="#006CAF"
        strokeOpacity={0.3}
        strokeWidth={8}
      />
      <G clipPath="url(#clip0_4454_5276)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M31.8 41.6a9.6 9.6 0 100-19.2 9.6 9.6 0 000 19.2zm.857-13.029a.857.857 0 00-1.714 0V32c0 .2.07.395.198.549l3.429 4.114a.857.857 0 001.317-1.097l-3.23-3.876V28.57z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_4454_5276">
          <Path fill="#fff" transform="translate(20 20)" d="M0 0H24V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default ClockIconSvg;
