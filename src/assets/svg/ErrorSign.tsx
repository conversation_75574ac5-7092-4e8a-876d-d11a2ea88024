import React from 'react';
import Svg, { SvgProps, Path, G, Defs, Rect, ClipPath } from 'react-native-svg';

function ErrorSignSVG(props: SvgProps) {
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#clip0_5367_9279)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.40406 2.5021C6.72555 2.18061 7.16159 2 7.61624 2H12.3838C12.8384 2 13.2744 2.18061 13.5959 2.5021L17.4979 6.40406C17.8194 6.72555 18 7.16159 18 7.61624V12.3838C18 12.8384 17.8194 13.2744 17.4979 13.5959L13.5959 17.4979C13.2744 17.8194 12.8384 18 12.3838 18H7.61624C7.16159 18 6.72555 17.8194 6.40406 17.4979L2.5021 13.5959C2.18061 13.2744 2 12.8384 2 12.3838V7.61624C2 7.16159 2.18061 6.72555 2.5021 6.40406L6.40406 2.5021ZM10 5.57143C10.4734 5.57143 10.8571 5.95519 10.8571 6.42857V10.1429C10.8571 10.6162 10.4734 11 10 11C9.52662 11 9.14286 10.6162 9.14286 10.1429V6.42857C9.14286 5.95519 9.52662 5.57143 10 5.57143ZM11.1429 13.2857C11.1429 13.9169 10.6312 14.4286 10 14.4286C9.36882 14.4286 8.85714 13.9169 8.85714 13.2857C8.85714 12.6545 9.36882 12.1429 10 12.1429C10.6312 12.1429 11.1429 12.6545 11.1429 13.2857Z"
          fill="#9C0A0A"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5367_9279">
          <Rect width={20} height={20} fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default ErrorSignSVG;
