import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function CreditCardSVG({ color, ...props }: SvgProps) {
  const fillColor = color || '#B7D5C2';
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        fill={fillColor}
        fillRule="evenodd"
        d="M2.143 2.5C.959 2.5 0 3.46 0 4.643v1.343h20V4.643c0-1.184-.96-2.143-2.143-2.143H2.143ZM0 15.357V7.771h20v7.586c0 1.184-.96 2.143-2.143 2.143H2.143A2.143 2.143 0 0 1 0 15.357Zm13.571-3.036a.893.893 0 0 0 0 1.786h2.143a.893.893 0 1 0 0-1.786h-2.143Z"
        clipRule="evenodd"
      />
    </Svg>
  );
}

export default CreditCardSVG;
