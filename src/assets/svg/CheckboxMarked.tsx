import React from 'react';
import Svg, { SvgProps, Path, G, Defs, Rect, ClipPath } from 'react-native-svg';

function CheckboxMarked(props: SvgProps) {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none" {...props}>
      <Rect width="24" height="24" rx="12" fill="#497C5C" />
      <G clipPath="url(#clip0_5765_14776)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0682 6.60943C18.4574 6.93109 18.5123 7.50738 18.1905 7.89662L10.8397 16.7919L10.8375 16.7945C10.6703 16.9953 10.4605 17.1563 10.2233 17.2658C9.98602 17.3753 9.72737 17.4305 9.46612 17.4275C9.20058 17.4242 8.93842 17.3607 8.7009 17.242C8.46417 17.1237 8.25724 16.9533 8.09551 16.7439C8.09496 16.7432 8.09442 16.7425 8.09387 16.7418L5.79273 13.7832C5.48273 13.3846 5.55453 12.8102 5.95311 12.5002C6.35168 12.1902 6.92611 12.262 7.23611 12.6606L9.48851 15.5565L16.781 6.7318C17.1026 6.34256 17.679 6.28778 18.0682 6.60943Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5765_14776">
          <Rect
            width="16"
            height="16"
            fill="white"
            transform="translate(4 4)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default CheckboxMarked;
