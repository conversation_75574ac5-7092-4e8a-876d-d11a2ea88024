import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function CrossPrimarySmallSVG(props: SvgProps) {
  return (
    <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.951.335A1.143 1.143 0 00.335 1.95L6.384 8l-6.05 6.05a1.143 1.143 0 101.617 1.615L8 9.616l6.05 6.05a1.143 1.143 0 001.615-1.617L9.616 8l6.05-6.049A1.143 1.143 0 0014.048.335L8 6.384 1.951.334z"
        fill="#0077B8"
      />
    </Svg>
  );
}

export default CrossPrimarySmallSVG;
