import * as React from 'react';
import Svg, {
  Circle,
  G,
  Path,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function RoundedKeySVG(props: SvgProps) {
  return (
    <Svg width={33} height={32} viewBox="0 0 33 32" fill="none" {...props}>
      <Circle cx={16.6665} cy={16} r={16} fill="#0077B8" />
      <G clipPath="url(#clip0_7145_20628)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.277 10.668a.914.914 0 011.293 0l1.829 1.828a.914.914 0 11-1.293 1.293l-1.182-1.182-.993.993 1.182 1.182a.914.914 0 01-1.293 1.293l-1.182-1.182-.963.963a3.657 3.657 0 11-1.293-1.293l1.606-1.606.004-.004.003-.003 2.282-2.282zm-7.582 7.046a1.829 1.829 0 113.657 0 1.829 1.829 0 01-3.657 0z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_7145_20628">
          <Path fill="#fff" transform="translate(8.666 8)" d="M0 0H16V16H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RoundedKeySVG;
