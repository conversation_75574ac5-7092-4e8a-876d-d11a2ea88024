import React from 'react';
import Svg, {
  SvgProps,
  Path,
  G,
  Defs,
  Rect,
  ClipPath,
  Circle,
} from 'react-native-svg';

function DeleteIconSVG(props: SvgProps) {
  return (
    <Svg width={32} height={32} fill="none" {...props}>
      <Circle cx="16" cy="16" r="16" fill="#0077B8" />
      <G clipPath="url(#clip0_5765_11324)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.5983 11.5083C14.9095 11.1971 15.3315 11.0223 15.7716 11.0223C16.2116 11.0223 16.6337 11.1971 16.9449 11.5083C17.1415 11.7049 17.2836 11.9458 17.3617 12.2075H14.1815C14.2595 11.9458 14.4017 11.7049 14.5983 11.5083ZM12.7268 12.2075C12.8267 11.5658 13.1277 10.9676 13.5926 10.5026C14.1705 9.92475 14.9543 9.6001 15.7716 9.6001C16.5888 9.6001 17.3726 9.92475 17.9505 10.5026C18.4155 10.9676 18.7165 11.5658 18.8164 12.2075H21.4605C21.8532 12.2075 22.1716 12.5259 22.1716 12.9186C22.1716 13.3114 21.8532 13.6297 21.4605 13.6297H20.5123V21.4519C20.5123 21.8291 20.3625 22.1909 20.0958 22.4577C19.8291 22.7244 19.4673 22.8742 19.0901 22.8742H12.4531C12.0759 22.8742 11.7141 22.7244 11.4474 22.4577C11.1807 22.1909 11.0308 21.8291 11.0308 21.4519V13.6297H10.0827C9.68996 13.6297 9.37158 13.3114 9.37158 12.9186C9.37158 12.5259 9.68996 12.2075 10.0827 12.2075H12.7268ZM13.8753 15.1719C14.2026 15.1719 14.4679 15.4372 14.4679 15.7644V19.5585C14.4679 19.8857 14.2026 20.1511 13.8753 20.1511C13.548 20.1511 13.2827 19.8857 13.2827 19.5585V15.7644C13.2827 15.4372 13.548 15.1719 13.8753 15.1719ZM18.2605 15.7644C18.2605 15.4372 17.9952 15.1719 17.6679 15.1719C17.3406 15.1719 17.0753 15.4372 17.0753 15.7644V19.5585C17.0753 19.8857 17.3406 20.1511 17.6679 20.1511C17.9952 20.1511 18.2605 19.8857 18.2605 19.5585V15.7644Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5765_11324">
          <Rect
            width="16"
            height="16"
            fill="white"
            transform="translate(8 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default DeleteIconSVG;
