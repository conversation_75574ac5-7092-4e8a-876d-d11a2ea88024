import React from 'react';
import Svg, {
  SvgProps,
  Path,
  Circle,
  G,
  Defs,
  Rect,
  ClipPath,
} from 'react-native-svg';

function AlertSuccess(props: SvgProps) {
  return (
    <Svg width="64" height="64" viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx="32" cy="32" r="24" fill="#497C5C" />
      <Circle
        cx="32"
        cy="32"
        r="28"
        stroke="#497C5C"
        strokeOpacity="0.3"
        strokeWidth="8"
      />
      <G clipPath="url(#clip0_5765_16623)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M41.1021 23.9144C41.6859 24.3969 41.7682 25.2613 41.2855 25.8452L30.2594 39.1881L30.2561 39.192C30.0053 39.4932 29.6905 39.7347 29.3346 39.8989C28.9788 40.0632 28.5908 40.146 28.1989 40.1415C27.8006 40.1365 27.4074 40.0414 27.0511 39.8632C26.696 39.6858 26.3856 39.4303 26.143 39.1161C26.1422 39.115 26.1414 39.114 26.1406 39.1129L22.6889 34.675C22.2238 34.0772 22.3315 33.2155 22.9294 32.7505C23.5273 32.2855 24.3889 32.3932 24.8539 32.9911L28.2325 37.335L39.1712 24.0979C39.6537 23.5141 40.5182 23.4319 41.1021 23.9144Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5765_16623">
          <Rect
            width="24"
            height="24"
            fill="white"
            transform="translate(20 20)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default AlertSuccess;
