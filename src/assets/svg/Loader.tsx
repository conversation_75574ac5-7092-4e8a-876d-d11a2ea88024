import React from 'react';
import Svg, { SvgProps, Circle } from 'react-native-svg';

function LoaderSVG(props: SvgProps) {
  return (
    <Svg width={68} height={22} fill="none" {...props}>
      <Circle
        cx="5.27713"
        cy="17.1111"
        r="4.88889"
        transform="rotate(180 5.27713 17.1111)"
        fill="white"
      />
      <Circle
        cx="5.27713"
        cy="17.1111"
        r="4.88889"
        transform="rotate(180 5.27713 17.1111)"
        fill="white"
      />
      <Circle
        cx="22.3885"
        cy="4.88894"
        r="4.88889"
        transform="rotate(180 22.3885 4.88894)"
        fill="#99CBE7"
      />
      <Circle
        cx="39.4998"
        cy="17.1111"
        r="4.88889"
        transform="rotate(180 39.4998 17.1111)"
        fill="white"
      />
      <Circle
        cx="62.7224"
        cy="17.1111"
        r="4.88889"
        transform="rotate(180 62.7224 17.1111)"
        fill="white"
      />
    </Svg>
  );
}

export default LoaderSVG;
