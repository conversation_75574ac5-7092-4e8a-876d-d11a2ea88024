import React from 'react';
import Svg, { Path, Rect, Defs, G, ClipPath } from 'react-native-svg';

function CardSVG() {
  return (
    <Svg width="36" height="36" viewBox="0 0 36 36" fill="none">
      <Rect width="36" height="36" rx="18" fill="#EDF4EF" />
      <G clipPath="url(#clip0_5716_8830)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.2143 12C10.2675 12 9.5 12.7675 9.5 13.7143V14.7885H25.5V13.7143C25.5 12.7675 24.7325 12 23.7857 12H11.2143ZM9.5 22.2857V16.2171H25.5V22.2857C25.5 23.2325 24.7325 24 23.7857 24H11.2143C10.2675 24 9.5 23.2325 9.5 22.2857ZM20.3571 19.8571C19.9627 19.8571 19.6429 20.1769 19.6429 20.5714C19.6429 20.9659 19.9627 21.2857 20.3571 21.2857H22.0714C22.4659 21.2857 22.7857 20.9659 22.7857 20.5714C22.7857 20.1769 22.4659 19.8571 22.0714 19.8571H20.3571Z"
          fill="#0077B8"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5716_8830">
          <Rect
            width="20"
            height="20"
            fill="white"
            transform="translate(8 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default CardSVG;
