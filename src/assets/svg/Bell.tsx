import React from 'react';
import Svg, { SvgProps, Path, Circle } from 'react-native-svg';

type BellProps = {
  svgProps?: SvgProps;
  badge?: boolean;
  color?: string;
};

function BellSVG(props: BellProps) {
  const { badge = false, svgProps, color } = props;
  return (
    <Svg width={22} height={22} fill="none" {...svgProps}>
      <Path
        fill={color ?? '#fff'}
        fillRule="evenodd"
        d="M6.536 4.654a5.647 5.647 0 0 1 9.64 3.993v4.392a1.255 1.255 0 0 0 1.255 1.255.627.627 0 0 1 0 1.255H3.627a.627.627 0 0 1 0-1.255 1.255 1.255 0 0 0 1.255-1.255V8.647c0-1.498.595-2.934 1.654-3.993Zm1.797 13.405c0-.52.422-.941.942-.941h2.51a.941.941 0 0 1 0 1.882h-2.51a.941.941 0 0 1-.942-.941Z"
        clipRule="evenodd"
      />
      {badge && <Circle cx={16.5} cy={4.583} r={4.583} fill="#FAAE4C" />}
    </Svg>
  );
}

export default BellSVG;
