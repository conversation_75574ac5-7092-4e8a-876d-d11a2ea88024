import React from 'react';
import Svg, { SvgProps, G, Path, Defs, ClipPath } from 'react-native-svg';

function UserCircleSingleSVG({ color, ...props }: SvgProps) {
  const fillColor = color || '#B7D5C2';
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#a)">
        <Path
          fill={fillColor}
          fillRule="evenodd"
          d="M20 10a9.967 9.967 0 0 1-2.773 6.912A9.972 9.972 0 0 1 10.03 20h-.06a9.971 9.971 0 0 1-7.197-3.088A9.966 9.966 0 0 1 0 10C0 4.477 4.477 0 10 0s10 4.477 10 10Zm-3.939 5A7.84 7.84 0 0 0 10 12.143 7.841 7.841 0 0 0 3.939 15 7.841 7.841 0 0 0 10 17.857 7.84 7.84 0 0 0 16.061 15Zm-6.06-4.286a3.571 3.571 0 1 0 0-7.143 3.571 3.571 0 0 0 0 7.143Z"
          clipRule="evenodd"
        />
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill="#fff" d="M0 0h20v20H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default UserCircleSingleSVG;
