import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SvgProps } from 'react-native-svg';

function RegisterCompleteSVG(props: SvgProps) {
  return (
    <Svg width={180} height={218} viewBox="0 0 180 218" fill="none" {...props}>
      <Path
        d="M89.087 30.319L85.317 0H96.58l-3.77 30.319h-3.723zM74.986 39.2l-20.29-14.927 7.958-7.952L77.592 36.55l-2.606 2.65zM106.957 39.2l20.291-14.927-7.958-7.952-14.939 20.228 2.606 2.65z"
        fill="#A4D300"
      />
      <G clipPath="url(#clip0_5272_10459)">
        <Path
          d="M173.4 133.763c0 5.406-6.13 10.133-7.139 15.245-1.04 5.262 2.794 11.974.787 16.821-2.044 4.925-9.514 6.952-12.446 11.33-2.951 4.415-1.977 12.094-5.722 15.835-3.738 3.741-11.412 2.767-15.823 5.719-4.375 2.935-6.4 10.411-11.322 12.455-4.838 2.009-11.545-1.828-16.803-.788-5.103 1.011-9.832 7.145-15.235 7.145-5.403 0-10.126-6.134-15.235-7.145-5.258-1.04-11.965 2.797-16.803.788-4.922-2.044-6.947-9.52-11.322-12.455-4.411-2.952-12.085-1.978-15.824-5.725-3.738-3.741-2.764-11.42-5.715-15.835-2.932-4.378-10.402-6.405-12.446-11.33-2.007-4.841 1.827-11.553.787-16.815C12.13 143.902 6 139.169 6 133.763c0-5.407 6.13-10.134 7.14-15.246 1.04-5.262-2.795-11.974-.788-16.821 2.043-4.925 9.514-6.952 12.446-11.33 2.951-4.415 1.978-12.094 5.722-15.835 3.738-3.74 11.412-2.767 15.823-5.72 4.375-2.934 6.4-10.41 11.322-12.454 4.838-2.009 11.545 1.828 16.803.788C79.571 56.135 84.3 50 89.703 50c5.403 0 10.126 6.134 15.235 7.145 5.258 1.04 11.965-2.797 16.809-.788 4.922 2.044 6.947 9.52 11.322 12.455 4.411 2.953 12.085 1.978 15.823 5.725 3.739 3.74 2.765 11.42 5.716 15.835 2.932 4.378 10.402 6.405 12.446 11.33 2.007 4.841-1.827 11.553-.787 16.815 1.009 5.106 7.139 9.839 7.139 15.246h-.006z"
          fill="#3398CF"
        />
        <G clipPath="url(#clip1_5272_10459)">
          <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M116.548 112.933c1.703 1.267 1.943 3.536.535 5.069l-32.16 35.025-.01.01a7.647 7.647 0 01-2.687 1.856 8.423 8.423 0 01-3.312.637 8.405 8.405 0 01-3.348-.731 7.574 7.574 0 01-2.649-1.961l-.007-.008-10.067-11.65c-1.357-1.569-1.043-3.831.701-5.052 1.744-1.22 4.257-.938 5.613.632l9.855 11.403 31.904-34.748c1.408-1.532 3.929-1.748 5.632-.482z"
            fill="#fff"
          />
        </G>
        <Path
          d="M136.283 180.375c25.724-25.743 25.724-67.481 0-93.224-25.725-25.743-67.433-25.743-93.158 0-25.724 25.743-25.724 67.481 0 93.224 25.725 25.743 67.433 25.743 93.158 0z"
          stroke="#fff"
          strokeWidth={5}
          strokeMiterlimit={10}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5272_10459">
          <Path
            fill="#fff"
            transform="translate(6 50)"
            d="M0 0H167.4V167.519H0z"
          />
        </ClipPath>
        <ClipPath id="clip1_5272_10459">
          <Path
            fill="#fff"
            transform="translate(62 108.619)"
            d="M0 0H56V50.4H0z"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RegisterCompleteSVG;
