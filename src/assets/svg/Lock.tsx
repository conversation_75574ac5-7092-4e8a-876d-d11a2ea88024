import React from 'react';
import Svg, { SvgProps, Path, G, Defs, ClipPath, Rect } from 'react-native-svg';

function LockSVG(props: SvgProps) {
  return (
    <Svg width="56" height="56" viewBox="0 0 56 56" fill="none" {...props}>
      <G clipPath="url(#clip0_7487_19962)">
        <Path
          d="M40.3202 20.9997V16.5197C40.3202 10.2477 35.3922 5.31967 29.1202 5.31967C22.8482 5.31967 17.9202 10.2477 17.9202 16.5197V20.9997C14.1122 20.9997 11.2002 23.9117 11.2002 27.7197V43.3997C11.2002 47.2077 14.1122 50.1197 17.9202 50.1197H40.3202C44.1282 50.1197 47.0402 47.2077 47.0402 43.3997V27.7197C47.0402 23.9117 44.1282 20.9997 40.3202 20.9997ZM22.4002 16.5197C22.4002 12.7117 25.3122 9.79967 29.1202 9.79967C32.9282 9.79967 35.8402 12.7117 35.8402 16.5197V20.9997H22.4002V16.5197ZM31.5842 35.5597L31.3602 35.7837V38.9197C31.3602 40.2637 30.4642 41.1597 29.1202 41.1597C27.7762 41.1597 26.8802 40.2637 26.8802 38.9197V35.7837C25.5362 34.4397 25.3122 32.4237 26.6562 31.0797C28.0002 29.7357 30.0162 29.5117 31.3602 30.8557C32.7042 31.9757 32.9282 34.2157 31.5842 35.5597Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_7487_19962">
          <Rect width="56" height="56" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
export default LockSVG;
