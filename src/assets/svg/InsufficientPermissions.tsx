import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function InsufficientPermissions(props: SvgProps) {
  return (
    <Svg width={132} height={130} fill="none" {...props}>
      <Path
        d="M65 9.19311C65 9.19311 48.4003 21.7798 15 27.3548V58.3734C15 92.1597 35.7496 116.298 65 127.791C94.2504 116.298 115 92.1531 115 58.3734V27.3548C81.5997 21.7732 65 9.18652 65 9.18652V9.19311Z"
        fill="#A8D3EB"
      />
      <Path
        d="M65 65C73.2843 65 80 58.0604 80 49.5C80 40.9396 73.2843 34 65 34C56.7157 34 50 40.9396 50 49.5C50 58.0604 56.7157 65 65 65Z"
        fill="#EFF3F6"
      />
      <Path
        d="M65 70C54.8079 70 45.7562 74.9028 40 82.5C45.7508 90.0972 54.8079 95 65 95C75.1921 95 84.2438 90.0972 90 82.5C84.2492 74.9028 75.1921 70 65 70Z"
        fill="#EFF3F6"
      />
    </Svg>
  );
}

export default InsufficientPermissions;
