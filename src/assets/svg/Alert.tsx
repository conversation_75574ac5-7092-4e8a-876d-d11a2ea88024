import * as React from 'react';
import Svg, {
  Circle,
  G,
  <PERSON>,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function AlertSVG(props: SvgProps) {
  return (
    <Svg width={64} height={64} viewBox="0 0 64 64" fill="none" {...props}>
      <Circle cx={32} cy={32} r={24} fill="#FAAE4C" />
      <Circle
        cx={32}
        cy={32}
        r={28}
        stroke="#FAAE4C"
        strokeOpacity={0.3}
        strokeWidth={8}
      />
      <G clipPath="url(#clip0_5903_18306)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M30.927 22.703a2.057 2.057 0 012.905.817l.003.006 7.545 15.09a2.062 2.062 0 01-.084 2.003 2.057 2.057 0 01-1.748.981H24.452a2.058 2.058 0 01-1.832-2.983l7.546-15.091.003-.006c.172-.337.435-.62.758-.817zM32 28.052c.569 0 1.03.46 1.03 1.029v4.459a1.029 1.029 0 01-2.059 0v-4.46c0-.567.461-1.028 1.03-1.028zm1.372 9.261a1.372 1.372 0 11-2.744 0 1.372 1.372 0 012.744 0z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5903_18306">
          <Path fill="#fff" transform="translate(20 20)" d="M0 0H24V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default AlertSVG;
