import React from 'react';
import Svg, { SvgProps, Path, Rect, G, Defs, ClipPath } from 'react-native-svg';

function TimerOuterGraySVG(props: SvgProps) {
  return (
    <Svg width={36} height={36} fill="none" {...props}>
      <Rect width="36" height="36" rx="18" fill="#EDF4EF" />
      <G clipPath="url(#clip0_7455_19942)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.833 26C22.2513 26 25.833 22.4183 25.833 18C25.833 13.5817 22.2513 10 17.833 10C13.4147 10 9.83301 13.5817 9.83301 18C9.83301 22.4183 13.4147 26 17.833 26ZM18.5473 15.1429C18.5473 14.7484 18.2275 14.4286 17.833 14.4286C17.4385 14.4286 17.1187 14.7484 17.1187 15.1429V18C17.1187 18.1671 17.1773 18.3289 17.2843 18.4573L20.1414 21.8858C20.394 22.1889 20.8444 22.2298 21.1474 21.9773C21.4505 21.7248 21.4914 21.2744 21.2389 20.9713L18.5473 17.7414V15.1429Z"
          fill="#0077B8"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_7455_19942">
          <Rect
            width="20"
            height="20"
            fill="white"
            transform="translate(8 8)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
export default TimerOuterGraySVG;
