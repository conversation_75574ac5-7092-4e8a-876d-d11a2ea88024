import { useTheme } from '@/theme';
import React from 'react';
import Svg, { SvgProps, G, Path, Defs, ClipPath } from 'react-native-svg';

function LogoutSVG(props: SvgProps) {
  const { backgrounds } = useTheme();
  const { fill = backgrounds.springGrass40.backgroundColor } = props;
  return (
    <Svg width={16} height={16} fill="none" {...props}>
      <G clipPath="url(#a)">
        <Path
          fill={fill}
          fillRule="evenodd"
          d="M0 1.714C0 .768.768 0 1.714 0h8c.947 0 1.715.768 1.715 1.714V3.93c-.46.368-.765.905-.84 1.499H6.571a2.571 2.571 0 1 0 0 5.142h4.018c.075.594.38 1.13.84 1.5v2.215c0 .947-.768 1.714-1.715 1.714h-8A1.714 1.714 0 0 1 0 14.286V1.714Zm12.53 3.208a.857.857 0 0 0-.53.792v1.143H6.571a1.143 1.143 0 0 0 0 2.286H12v1.143a.857.857 0 0 0 1.463.606l2.286-2.286a.857.857 0 0 0 0-1.212l-2.286-2.286a.857.857 0 0 0-.934-.186Z"
          clipRule="evenodd"
        />
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill="#fff" d="M0 0h16v16H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
export default LogoutSVG;
