import React from 'react';
import Svg, { SvgProps, G, Path, Defs, ClipPath } from 'react-native-svg';

function HomeSVG({ color, ...props }: SvgProps) {
  const fillColor = color || '#B7D5C2';
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#a)">
        <Path
          fill={fillColor}
          fillRule="evenodd"
          d="M.455 8.636C.165 8.906 0 9.284 0 9.68v8.176C0 19.041.96 20 2.143 20H8.57v-4.286a1.429 1.429 0 1 1 2.858 0V20h6.428C19.041 20 20 19.04 20 17.857V9.681c0-.397-.165-.775-.455-1.045L10.465.172a.714.714 0 0 0-.93 0L.455 8.636Z"
          clipRule="evenodd"
        />
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill="#fff" d="M0 0h20v20H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default HomeSVG;
