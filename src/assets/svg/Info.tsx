import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function InfoSvg(props: SvgProps) {
  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.6 10A9.6 9.6 0 11.4 10a9.6 9.6 0 0119.2 0zM8.286 7.6A1.714 1.714 0 1110 9.314c-.568 0-1.029.46-1.029 1.029v.886a1.029 1.029 0 002.058 0A3.773 3.773 0 0010 3.83a3.771 3.771 0 00-3.771 3.77 1.029 1.029 0 002.057 0zm3.085 7.2a1.371 1.371 0 11-2.742 0 1.371 1.371 0 012.742 0z"
        fill="#006CAF"
      />
    </Svg>
  );
}

export default InfoSvg;
