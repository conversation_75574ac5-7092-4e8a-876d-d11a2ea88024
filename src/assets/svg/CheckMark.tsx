import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

function CheckSVG(props: SvgProps) {
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5851 3.26191C18.0716 3.66399 18.1402 4.38435 17.738 4.8709L8.54956 15.99L8.5468 15.9933C8.33779 16.2443 8.07549 16.4455 7.77895 16.5823C7.4824 16.7192 7.15909 16.7883 6.83252 16.7845C6.5006 16.7804 6.1729 16.7011 5.876 16.5526C5.5801 16.4047 5.32142 16.1918 5.11926 15.93C5.11858 15.9291 5.1179 15.9283 5.11722 15.9273L2.24079 12.2291C1.85329 11.7309 1.94304 11.0128 2.44126 10.6253C2.93948 10.2378 3.65751 10.3276 4.04502 10.8258L6.86051 14.4458L15.9761 3.41487C16.3782 2.92832 17.0986 2.85984 17.5851 3.26191Z"
        fill="white"
      />
    </Svg>
  );
}

export default CheckSVG;
