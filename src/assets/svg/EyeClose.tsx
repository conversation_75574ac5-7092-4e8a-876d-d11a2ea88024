import React from 'react';
import Svg, { SvgProps, Path, Defs, G, ClipPath, Rect } from 'react-native-svg';

function EyeCloseSVG(props: SvgProps) {
  return (
    <Svg width={20} height={20} fill="none" {...props}>
      <G clipPath="url(#clip0_5270_1442)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.25264 3.47248C1.91579 3.13563 1.91579 2.58949 2.25264 2.25264C2.58949 1.91579 3.13563 1.91579 3.47248 2.25264L6.49559 5.27574C7.53967 4.70203 8.72671 4.29584 9.99996 4.29584C11.7619 4.29584 13.3587 5.07362 14.6401 5.9968C15.9261 6.92332 16.9441 8.03201 17.5605 8.77689L17.5656 8.7832C17.8409 9.12568 17.9858 9.55967 17.9858 10C17.9858 10.4404 17.8409 10.8744 17.5656 11.2169L17.5605 11.2232C16.9985 11.9024 16.1025 12.884 14.9741 13.7542L17.7474 16.5274C18.0842 16.8643 18.0842 17.4105 17.7474 17.7473C17.4105 18.0842 16.8643 18.0842 16.5275 17.7473L2.25264 3.47248ZM12.3198 11.1C12.4782 10.7666 12.5668 10.3936 12.5668 9.99995C12.5668 8.58232 11.4176 7.4331 9.99996 7.4331C9.6063 7.4331 9.23333 7.52171 8.89993 7.68009L12.3198 11.1ZM2.43945 8.77689C2.80684 8.33286 3.317 7.75956 3.94156 7.17264L12.1117 15.3428C11.444 15.5684 10.7364 15.7043 9.99996 15.7043C8.23807 15.7043 6.64125 14.9265 5.35987 14.0034C4.07386 13.0768 3.05577 11.9681 2.43945 11.2232L2.43431 11.2169C2.15907 10.8744 2.01416 10.4404 2.01416 10C2.01416 9.55967 2.15907 9.12568 2.43431 8.7832L2.43945 8.77689Z"
          fill="#006CAF"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5270_1442">
          <Rect width="20" height="20" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default EyeCloseSVG;
