import Svg, { <PERSON>, <PERSON>, Defs, <PERSON>lip<PERSON><PERSON>, SvgProps } from 'react-native-svg';

function EmailSentSvg({ ...props }: SvgProps) {
  return (
    <Svg width={180} height={218} viewBox="0 0 180 218" fill="none" {...props}>
      <G clipPath="url(#clip0_5272_19351)">
        <Path d="M180 112.655H0v101.313h180V112.655z" fill="#161718" />
        <Path d="M90.006 55.744L0 112.655h180l-89.994-56.91z" fill="#D4BC98" />
        <Path
          d="M166.066 80.966H13.934V210.53h152.132V80.966z"
          fill="#EFF3F6"
        />
        <Path
          d="M0 112.655l69.426 52.672L0 218V112.655zM180 112.655l-69.414 52.672L180 218V112.655z"
          fill="#C4AC88"
        />
        <Path d="M0 218l69.426-52.673h41.16L180 218H0z" fill="#D4BC98" />
        <Path
          d="M176.022 86.57c0 12.634-10.234 22.864-22.873 22.864-12.638 0-22.873-10.23-22.873-22.863s10.235-22.863 22.873-22.863c12.639 0 22.873 10.23 22.873 22.863z"
          fill="#F05050"
        />
        <Path
          d="M156.245 77.651v19.004h-4.407V81.183h-3.788V77.65h8.195z"
          fill="#fff"
        />
        <Path
          d="M89.087 30.319L85.317 0H96.58l-3.77 30.319h-3.723zM74.986 39.2l-20.29-14.927 7.958-7.952L77.592 36.55l-2.606 2.65zM106.957 39.2l20.291-14.927-7.958-7.952-14.939 20.228 2.606 2.65z"
          fill="#A4D300"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5272_19351">
          <Path fill="#fff" d="M0 0H180V218H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default EmailSentSvg;
