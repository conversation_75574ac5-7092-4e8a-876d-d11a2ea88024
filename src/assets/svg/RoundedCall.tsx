import * as React from 'react';
import Svg, {
  Circle,
  G,
  Path,
  Defs,
  ClipPath,
  SvgProps,
} from 'react-native-svg';

function RoundedCallSVG(props: SvgProps) {
  return (
    <Svg width={33} height={32} viewBox="0 0 33 32" fill="none" {...props}>
      <Circle cx={16.333} cy={16} r={16} fill="#0077B8" />
      <G clipPath="url(#clip0_7145_20642)">
        <Path
          d="M14.4 22.636a3.53 3.53 0 01-4.428-.487l-.498-.487a1.196 1.196 0 010-1.672l2.114-2.092a1.185 1.185 0 011.66 0 1.196 1.196 0 001.672 0l3.322-3.322a1.173 1.173 0 000-1.671 1.185 1.185 0 010-1.661l2.103-2.104a1.196 1.196 0 011.672 0l.487.499a3.532 3.532 0 01.487 4.428 32.04 32.04 0 01-8.591 8.569z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_7145_20642">
          <Path fill="#fff" transform="translate(8.333 8)" d="M0 0H16V16H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RoundedCallSVG;
