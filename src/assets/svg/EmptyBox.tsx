import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function EmptyBoxSVG(props: SvgProps) {
  return (
    <Svg width={180} height={187} viewBox="0 0 180 187" fill="none" {...props}>
      <Path
        d="M22.903 148.279l.229-80.482 66.655-38.725-.228 80.482-66.656 38.725z"
        fill="#916F4B"
      />
      <Path
        d="M89.56 109.554l.228-80.482 67.113 38.725-.229 80.482-67.112-38.725z"
        fill="#59442E"
      />
      <Path
        d="M156.884 67.797L180 38.725 112.904 0 89.788 29.072l67.096 38.725z"
        fill="#CC9C6A"
      />
      <Path
        d="M156.9 67.797l-.228 80.482L90 187.004l.228-80.482L156.9 67.797z"
        fill="#B78C5F"
      />
      <Path
        d="M90.228 106.522L90 187.004l-67.097-38.725.229-80.482 67.096 38.725z"
        fill="#7F6142"
      />
      <Path
        d="M67.113 108.038l23.115-1.533-67.112-38.708L0 69.313l67.113 38.725z"
        fill="#CC9C6A"
      />
      <Path
        d="M112.904 108.038l-23.116-1.533 67.096-38.708L180 69.313l-67.096 38.725zM23.116 67.797L0 38.725 67.113 0l23.115 29.072-67.112 38.725z"
        fill="#CC9C6A"
      />
    </Svg>
  );
}

export default EmptyBoxSVG;
