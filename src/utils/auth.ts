import { TokenData } from '@/types/schemas/tokenData';
import { MMKV } from 'react-native-mmkv';
import { clearAnalyticsStorageKey,  clearUserInfoData } from './storage';
import { logEventWithStatus, Events, EventStatus } from './analytics';

export const authStorage = new MMKV({ id: 'auth-storage' });
export const TOKEN_STORAGE_KEY = 'tokens';

export const setTokenData = (tokenData: TokenData) => {
  authStorage.set(TOKEN_STORAGE_KEY, JSON.stringify(tokenData));
};

export const getTokenData = (): TokenData | null => {
  const tokenDataString = authStorage.getString(TOKEN_STORAGE_KEY);
  if (!tokenDataString) {
    return null;
  }
  const tokenData: TokenData = JSON.parse(tokenDataString) as TokenData;
  return tokenData;
};

export const clearTokenData = async () => {
  clearUserInfoData();
  clearAnalyticsStorageKey();
  authStorage.delete(TOKEN_STORAGE_KEY);
  await logEventWithStatus(Events.logout, EventStatus.completed);
};

export const tokenDataListener = (
  callbackFn: (data: TokenData | null) => void,
) => {
  return authStorage.addOnValueChangedListener(key => {
    if (key === TOKEN_STORAGE_KEY) {
      const tokenData = getTokenData();
      callbackFn(tokenData);
    }
  });
};
