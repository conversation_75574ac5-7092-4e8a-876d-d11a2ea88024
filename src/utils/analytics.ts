import analytics from '@react-native-firebase/analytics';

export enum Events {
  register = 'register',
  login = 'login',
  logout = 'logout',
  addStorageUnit = 'add_storage_unit',
  findStorageUnit = 'find_storage_account_number',
  oneTimePayment = 'one_time_payment',
  autopayAdd = 'autopay_add',
  autopayRemove = 'autopay_remove',
  autopayPaid = 'autopay_paid',
  addBillingAddress = 'add_billing_address',
  gateAccessRequest = 'gate_access_request',
  account = 'account',
  requestReceipt = 'request_receipt',
  secondaryContent = 'secondary_content',
  getNotificationsList = 'get_notifications_list',
  supportForm = 'support_form',
  permissionBiometrics = 'permission_biometrics',
  permissionLocation = 'permission_location',
  permissionBluetooth = 'permission_bluetooth',
  gateAccessCode = 'gate_access_code',
  screenView = 'screen_view',
}

export enum EventStatus {
  initiated = 'initiated',
  completed = 'completed',
  failed = 'failed',
  return = 'returned',
  enabled = 'enabled',
  disabled = 'disabled',
}

export type AnalyticsParams = {
  facilityId?: string;
  tenantId?: string;
  error?: string;
  gateType?: string;
  deviceId?: string;
  [key: string]: string | undefined;
};

export async function logCustomEvent(name: string, params: object) {
  try {
    await analytics().logEvent(name, params);
  } catch (error) {
    console.warn('error logging analytic event', error);
  }
}

export const logEventWithStatus = async (
  event: Events,
  status: EventStatus,
  params?: AnalyticsParams,
) => {
  console.log('[Analytics] Logging event:', {
    event,
    status,
    facilityId: params?.facilityId,
    tenantId: params?.tenantId,
  });

  const enhancedParams = {
    ...params,
  };

  await logCustomEvent(event, { ...enhancedParams, status });
};
