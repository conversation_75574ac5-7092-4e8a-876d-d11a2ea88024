import { Notification } from '@/types/schemas/notification';
import {
  getLastFetchedNotificationDate,
  getNotificationDeleteId,
} from '@/utils/storage';
import { DateTime } from 'luxon';
import { useWindowDimensions } from 'react-native';
import { getUserInfoData } from './storage';

// Format phone number as XXX-XXX-XXXX
export const formatPhoneNumber = (input: string) => {
  // Remove all non-numeric characters from the input
  let cleaned = input.replace(/\D/g, '');
  cleaned = cleaned.replace('-', '');
  let formatted: string = cleaned;

  // Format the cleaned input
  if (cleaned.length > 9) {
    formatted = `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(
      6,
    )}`;
  } else if (cleaned.length > 6) {
    formatted = `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(
      6,
    )}`;
  } else if (cleaned.length > 3) {
    formatted = `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
  }
  return formatted;
};

// Remove all non-numeric characters from the input
export const digitsOnly = (input: string) => {
  return input.replace(/\D/g, '');
};

// Check if there are new notifications
export const isNewNotification = (data: Notification[]) => {
  if (data) {
    const deletedIds: string[] = getNotificationDeleteId(getUserInfoData()?.contactInfo?.email?? '') ?? [];
    const lastFetchDate: string = getLastFetchedNotificationDate(getUserInfoData()?.contactInfo?.email?? '') ?? '';
    const lastFetchDateTime = DateTime.fromISO(lastFetchDate);
    if (deletedIds && lastFetchDateTime.isValid) {
      const result = data.filter(item => {
        const date = DateTime.fromISO(item.date);
        return (
          !deletedIds.includes(item.id) &&
          date.isValid &&
          lastFetchDateTime.isValid &&
          date > lastFetchDateTime
        );
      });
      return result.length > 0;
    }
  }
  return true;
};

// Convert verbose relative time to short form
export const shortenRelativeTime = (relativeTime: string | null) => {
  if (!relativeTime) {
    return null;
  }

  const mappings: { [key: string]: string } = {
    second: 's ago',
    seconds: 's ago',
    minute: 'm ago',
    minutes: 'm ago',
    hour: 'h',
    hours: 'h',
    day: 'd',
    days: 'd',
    week: 'w',
    weeks: 'w',
    month: 'mo',
    months: 'mo',
    year: 'y',
    years: 'y',
  };

  const parts = relativeTime.split(' ');
  if (parts.length === 3 && parts[2] === 'ago') {
    return `${parts[0]}${mappings[parts[1]]}`;
  }
  return relativeTime; // Default fallback in case of unexpected format
};

export enum DoorState {
  OPEN = 'open',
  OPENING = 'openning',
  OPENED = 'opened',
  FAILED = 'failed',
}

export enum GateSystemType {
  OPENTECH = 'OpenTech',
  PTI = 'PTI',
  NOKE = 'Noke',
  NONE = 'None',
}

function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

export function getDistanceFromLatLonInKm(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1); // deg2rad below
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
}

  /**
  * Scales the font size based on the screen width to maintain consistency across android devices.
  * @param {number} size - The original font size.
  * @returns {number} - The scaled font size adjusted according to the screen width.
  */
export function fixedFontSize(size: number) {
  const { width } = useWindowDimensions(); // Handles dynamic changes
  const baseWidth = width || 460; // Prevents divide-by-zero issues
  return (baseWidth / 460) * size;
  };
