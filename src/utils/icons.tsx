import React from 'react';
import {
  HomeSVG,
  CreditCardSVG,
  KeySVG,
  UserCircleSingleSVG,
  LogoutSVG,
} from '@/assets/svg';

const ICON_MAP: { [key: string]: ({ ...props }) => JSX.Element } = {
  Home: HomeSVG,
  CreditCard: CreditCardSVG,
  Key: KeySVG,
  Account: UserCircleSingleSVG,
  Logout: LogoutSVG,
};

export const renderIcon = (iconName: string, color?: string) => {
  const Icon = ICON_MAP[iconName];
  if (color) {
    return <Icon color={color} />;
  }
  return <Icon />;
};

interface IconProps {
  color?: string;
  width?: number;
}

export const renderSVGIcon = (
  Icon: React.ComponentType<IconProps>, // Component type
  color?: string,
  width?: number,
) => {
  return <Icon color={color} width={width} />;
};
