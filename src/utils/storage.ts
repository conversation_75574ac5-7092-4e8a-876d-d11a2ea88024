import { AccountData } from '@/types/schemas/account';
import { MMKV } from 'react-native-mmkv';
import { NokeLock } from '@/types/schemas/Noke';
import { ANALYTICS_STORAGE_KEYS } from '@/context/AnalyticsContext';

export const userStorage = new MMKV({ id: 'user-storage' });
export const PROFILE_IMAGE_DATA = 'profileImageData';
export const USER_INFO_DATA = 'userInfoData';
export const FETCH_LAST_NOTIFICATION_DATE = 'fetch_last_notification_date';
export const DELETED_NOTIFICATION_ID = 'deleted_notification_id';
export const PERMISSIONS_DIALOG_SHOWN = 'permissions_dialog_shown';
export const NOKE_LOCKS_STORAGE_KEY_PREFIX = 'noke_locks_storage_';

export const getNoteStorageKeyForUnitId = (unitId: string) => {
  return `unitNote-${unitId}`;
};

export const setLastFetchedNotificationDate = (date: string, email: string) => {
  userStorage.set(`${FETCH_LAST_NOTIFICATION_DATE}_${email}`, date);
};

export const getNotificationDeleteId = (email: string): string[] | null => {
  const notificationID: string | undefined = userStorage.getString(
    `${DELETED_NOTIFICATION_ID}_${email}`,
  );
  if (!notificationID) {
    return null;
  }
  const stringArray: string[] = notificationID
    ? (JSON.parse(notificationID) as string[])
    : [];

  return stringArray;
};

export const setNotificationDeleteId = (id: string[], email: string) => {
  userStorage.set(`${DELETED_NOTIFICATION_ID}_${email}`, JSON.stringify(id));
};

export const getLastFetchedNotificationDate = (email: string): string | null => {
  const lastNotificationDate = userStorage.getString(`${FETCH_LAST_NOTIFICATION_DATE}_${email}`,);
  if (!lastNotificationDate) {
    return null;
  }

  return lastNotificationDate;
};

export const setProfileImageData = (imageData: object) => {
  userStorage.set(PROFILE_IMAGE_DATA, JSON.stringify(imageData));
};

export const getProfileImageData = (): object | null => {
  const imageDataString = userStorage.getString(PROFILE_IMAGE_DATA);
  if (!imageDataString) {
    return null;
  }
  const imageData: object = JSON.parse(imageDataString) as object;
  return imageData;
};

export const clearProfileImageData = () => {
  userStorage.delete(PROFILE_IMAGE_DATA);
};

export const setUserInfoData = (accountData: AccountData) => {
  userStorage.set(USER_INFO_DATA, JSON.stringify(accountData));
};

export const getUserInfoData = (): AccountData | null => {
  const userDataString = userStorage.getString(USER_INFO_DATA);
  if (!userDataString) {
    return null;
  }
  const imageData: AccountData = JSON.parse(userDataString) as AccountData;
  return imageData;
};

export const clearAnalyticsStorageKey = () => {
  userStorage.delete(ANALYTICS_STORAGE_KEYS.FACILITY_ID);
  userStorage.delete(ANALYTICS_STORAGE_KEYS.TENANT_ID);
}

export const clearUserInfoData = () => {
  userStorage.delete(USER_INFO_DATA);
};

export const getCachedNokeLocks = (facilityId: number): NokeLock[] => {
  const locksString = userStorage.getString(
    `${NOKE_LOCKS_STORAGE_KEY_PREFIX}${facilityId}`,
  );
  return locksString ? (JSON.parse(locksString) as NokeLock[]) : [];
};

export const setCachedNokeLocks = (
  facilityId: number,
  locks: NokeLock[],
): void => {
  userStorage.set(
    `${NOKE_LOCKS_STORAGE_KEY_PREFIX}${facilityId}`,
    JSON.stringify(locks),
  );
};
