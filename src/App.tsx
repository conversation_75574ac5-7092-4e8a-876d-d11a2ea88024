import React from 'react';
import 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { ThemeProvider } from '@/theme';
import * as Sentry from '@sentry/react-native';
import Config from 'react-native-config';
import { AnalyticsProvider } from '@/context/AnalyticsContext';

import ApplicationNavigator from './navigators/Application';
import './translations';
import useStorage from './hooks/useStorage';

Sentry.init({
  dsn: 'https://<EMAIL>/4508445233643520',
  environment: Config.ENVIRONMENT,

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // enableSpotlight: __DEV__,
});

const queryClient = new QueryClient();

function App() {
  const themeStorage = useStorage('theme-storage');

  return (
    <SafeAreaProvider>
      <QueryClientProvider client={queryClient}>
        <AnalyticsProvider>
          <ThemeProvider storage={themeStorage}>
            <ApplicationNavigator />
          </ThemeProvider>
        </AnalyticsProvider>
      </QueryClientProvider>
    </SafeAreaProvider>
  );
}

export default App;
