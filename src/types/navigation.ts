import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import type { DrawerScreenProps } from '@react-navigation/drawer';
import { Location } from './schemas/location';
import { Unit } from './schemas/unit';

export interface RootParamList extends ApplicationStackParamList {}

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace ReactNavigation {
    interface RootParamList extends ApplicationStackParamList {}
  }
}

export type ApplicationStackParamList = {
  Startup: undefined;
  Example: undefined;
  Game: undefined;
  Onboarding: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
};

export type ApplicationScreenProps =
  NativeStackScreenProps<ApplicationStackParamList>;

export type RegisterStackParamList = {
  RegisterUser: undefined;
  FindStorageNumber: undefined;
  StorageNumberEmailSent: { description: string; isFromResetPassword: boolean };
  Login: undefined;
  LoginScreen: undefined;
  Onboarding: undefined;
  Register: undefined;
  ResetPassword: undefined;
};

export type RegisterStepScreenProps =
  NativeStackScreenProps<RegisterStackParamList>;

export type MainTabNavParamList = {
  Home: undefined;
  Payments: undefined;
  Access: undefined;
  Account: undefined;
  Main: undefined;
};

export type HomeScreenProps = BottomTabScreenProps<MainTabNavParamList>;
export type PaymentsScreenProps = BottomTabScreenProps<MainTabNavParamList>;
export type AccessScreenProps = BottomTabScreenProps<MainTabNavParamList>;
export type AccountScreenProps = BottomTabScreenProps<MainTabNavParamList>;

export type AccountStackParamList = {
  AccountHome: undefined;
  AccountSettings: undefined;
  MainNotifications: undefined;
};
export type AccountStackScreenProps =
  NativeStackScreenProps<AccountStackParamList>;

export type LocationDetailsParams = {
  location: Location;
};
export type LocationUnitNotesParams = {
  unit: Unit[];
  selectedUnit: Unit;
};
export type HomeStackParamList = {
  MainHome: undefined;
  MainLocationDetails: LocationDetailsParams;
  MainLocationUnitNotes: LocationUnitNotesParams;
  MainNotifications: undefined;
};
export type HomeStackScreenProps = NativeStackScreenProps<HomeStackParamList>;

export type PaymentStackParamList = {
  Payments: undefined;
  PaymentHistory: undefined;
  PaymentWebView: undefined;
  SavedAddress: undefined;
  AutoPayTurnOffAlert: undefined;
  AutoPayTurnOffSuccess: undefined;
  AddBillingAddress: undefined;
};

export type PaymentStackScreenProps =
  NativeStackScreenProps<PaymentStackParamList>;

export type AccessStackParamList = {
  Access: undefined;
  AccessSuccessFailedAlert: undefined;
  Support: undefined;
  AccountSettings: undefined;
  NokeTest: undefined;
};

export type AccessStackScreenProps =
  NativeStackScreenProps<AccessStackParamList>;

export type DrawerParamList = {
  DrawerMain: undefined;
  DrawerHome: undefined;
  DrawerPayments: undefined;
  DrawerAccess: undefined;
  DrawerAccount: undefined;
};

type RouteProp = Pick<DrawerScreenProps<DrawerParamList>, 'route'>;
export type MainDrawerScreenProps = Omit<
  DrawerScreenProps<DrawerParamList>,
  'route'
> & {
  route?: RouteProp;
};
