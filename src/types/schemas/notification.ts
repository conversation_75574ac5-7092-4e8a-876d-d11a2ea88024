import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString, nullableBoolean } from './zodHelpers';

export const ApiNotificationSchema = z.object({
  id: nullableString(),
  title: nullableString(),
  body: nullableString(),
  date: nullableString(),
  isBillingRelated: nullableBoolean(),
});

export const NotificationSchema = z.object({
  id: nullableString(),
  title: nullableString(),
  subtitle: nullableString(),
  date: nullableString(),
});

export const ApiNotificationsResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    notifications: ApiNotificationSchema.array(),
  }),
);

/**
 * API Types
 */
export type ApiNotification = z.infer<typeof ApiNotificationSchema>;
export type ApiNotificationsResponse = z.infer<
  typeof ApiNotificationsResponseSchema
>;

/**
 *  Types
 */
export type Notification = z.infer<typeof NotificationSchema>;
