import { z } from 'zod';
import { ApiUnitSchema, UnitSchema } from './unit';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString, nullableBoolean, nullableNumber } from './zodHelpers';

export const ApiLocationImageSchema = z.object({
  Url: nullableString(),
  AltText: nullableString(),
});

export const LocationImageSchema = z.object({
  url: nullableString(),
  altText: nullableString(),
});

export const ApiTokenPaymentsSchema = z.object({
  IsSsm: nullableBoolean(),
  UseTokenPayments: nullableBoolean(),
  ApiKey: nullableString(),
  Environment: nullableString(),
});

export const TokenPaymentsSchema = z.object({
  isSsm: nullableBoolean(),
  useTokenPayments: nullableBoolean(),
  apiKey: nullableString(),
  environment: nullableString(),
});

export const ApiHoursSchema = z.object({
  Day: nullableString(),
  Hours: nullableString(),
});

export const HoursSchema = z.object({
  day: nullableString(),
  hours: nullableString(),
});

export const ApiLocationSchema = z.object({
  Image: ApiLocationImageSchema,
  LocationId: nullableNumber(),
  OfficeHours: ApiHoursSchema.array(),
  GateHours: ApiHoursSchema.array(),
  todaysOfficeHours: ApiHoursSchema,
  todaysGateHours: ApiHoursSchema,
  GateSystemType: nullableString(),
  Address: nullableString(),
  City: nullableString(),
  State: nullableString(),
  Zip: nullableString(),
  Phone: nullableString(),
  Rating: nullableNumber(),
  NewReviewUrl: nullableString(),
  TokenPayments: ApiTokenPaymentsSchema,
  Units: ApiUnitSchema.array(),
  latitude: nullableNumber(),
  longitude: nullableNumber(),
});

export const LocationSchema = z.object({
  image: LocationImageSchema, // Change to a single object
  locationId: nullableNumber(),
  officeHours: z.array(HoursSchema),
  gateHours: z.array(HoursSchema),
  todaysOfficeHours: HoursSchema,
  todaysGateHours: HoursSchema,
  gateSystemType: nullableString(),
  address: nullableString(),
  city: nullableString(),
  state: nullableString(),
  zip: nullableString(),
  phone: nullableString(),
  rating: nullableNumber().nullable(), // Nullable to handle null ratings
  newReviewUrl: nullableString(),
  tokenPayments: TokenPaymentsSchema, // Change to a single object
  units: z.array(UnitSchema),
  latitude: nullableNumber(),
  longitude: nullableNumber(),
});

export const LocationDropItemSchema = z.object({
  name: nullableString(),
  locationId: nullableNumber(),
});

export const ApiLocationResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    accountDisabled: nullableBoolean(),
    locations: z.array(LocationSchema),
  }),
);

export const ApiLocationsInStateResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    facilitiesInState: LocationDropItemSchema.array(),
  }),
);

/**
 * API Types
 */
export type ApiLocation = z.infer<typeof ApiLocationSchema>;
export type ApiLocationImage = z.infer<typeof ApiLocationImageSchema>;
export type ApiHours = z.infer<typeof ApiHoursSchema>;
export type ApiLocationResponse = z.infer<typeof ApiLocationResponseSchema>;
export type ApiLocationsInStateResponse = z.infer<
  typeof ApiLocationsInStateResponseSchema
>;

/**
 * Frontend Adapter Types
 */
export type Location = z.infer<typeof LocationSchema>;
export type LocationImage = z.infer<typeof LocationImageSchema>;
export type Hours = z.infer<typeof HoursSchema>;
export type FacilitiesInState = z.infer<typeof LocationDropItemSchema>;
export type Units = z.infer<typeof LocationDropItemSchema>;
