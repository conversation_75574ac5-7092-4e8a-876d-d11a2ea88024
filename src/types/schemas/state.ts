import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString } from './zodHelpers';

export const StateSchema = z.object({
  name: nullableString(),
  abbreviation: nullableString(),
});

export const ApiStateResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    states: StateSchema.array(),
  }),
);

/**
 * API Types
 */
export type ApiStateResponse = z.infer<typeof ApiStateResponseSchema>;

/**
 *  Types
 */
export type State = z.infer<typeof StateSchema>;
