import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableNumber, nullableString, nullableBoolean } from './zodHelpers';

export const PaymentHistoryItemSchema = z.object({
  paymentReceiptNumber: nullableNumber(),
  date: nullableString(),
  card: nullableString(),
  amount: nullableString(),
  accountId: nullableNumber(),
  rentalId: nullableNumber(),
  facilityId: nullableNumber(),
});

export const PaymentHistorySchema = z.object({
  items: PaymentHistoryItemSchema.array(),
  isSsmHistory: nullableBoolean(),
});

export const ApiPaymentHistoryResponseSchema =
  BaseApiResponseSchema.merge(PaymentHistorySchema);

/**
 * API Types
 */
export type ApiPaymentHistory = z.infer<typeof PaymentHistorySchema>;
export type ApiPaymentHistoryResponse = z.infer<
  typeof ApiPaymentHistoryResponseSchema
>;

/**
 * Frontend Adapter Types
 */
export type PaymentHistoryItem = z.infer<typeof PaymentHistoryItemSchema>;
export type PaymentsHistory = z.infer<typeof PaymentHistorySchema>;
