import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableNumber, nullableString } from './zodHelpers';

export const ApiAccessCodeSchema = z.object({
  tenantId: nullableNumber(),
  accessCode: nullableString(),
});

export const AccessCodeSchema = z.object({
  tenantId: nullableNumber(),
  accessCode: nullableString(),
});

export const ApiAccessCodeResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    accessCodes: ApiAccessCodeSchema.array(),
  }),
);

/**
 * API Types
 */
export type ApiAccessCode = z.infer<typeof ApiAccessCodeSchema>;
export type ApiAccessCodeResponse = z.infer<typeof ApiAccessCodeResponseSchema>;

/**
 *  Types
 */
export type AccessCode = z.infer<typeof AccessCodeSchema>;
