import { z } from 'zod';
import { nullableNumber, nullableString, nullableBoolean } from './zodHelpers';

/**
 * Request Body Schemas
 */
export const AddStorageAccountRequestSchema = z.object({
  accountId: nullableNumber(),
  lastName: nullableString(),
});

export const UpdateCustomerContactRequestSchema = z.object({
  firstName: nullableNumber(),
  lastName: nullableString(),
  phone: nullableString(),
  email: nullableString(),
});

export const ChangePasswordRequestSchema = z.object({
  newPassword: nullableString(),
  oldPassword: nullableString(),
});

export const SubmitSupportFormRequestSchema = z.object({
  facilityId: nullableNumber().optional(),
  name: nullableString(),
  email: nullableString(),
  phone: nullableString(),
  message: nullableString(),
});

export const AddBillingAddressRequestSchema = z.object({
  address: nullableString(),
  apartment: nullableString(),
  city: nullableString(),
  state: nullableString(),
  zipCode: nullableString(),
  setPrimary: nullableBoolean(),
});

export const DeleteBillingAddressRequestSchema = z.object({
  addressId: nullableNumber(),
});

export const SetPrimaryAddressRequestSchema = z.object({
  addressId: nullableNumber(),
});

export const GetPaymentHistoryRequestSchema = z.object({
  culture: nullableString(),
  rentalAccounts: z.array(
    z.object({
      accountId: nullableString(),
      rentalId: nullableString(),
    }),
  ),
});

export const PaymentHistoryRequestSchema = z.array(
  z.object({
    accountId: nullableString(),
    rentalId: nullableString(),
  }),
);

export const WebViewUrlRequestSchema = z.object({
  facilityId: nullableNumber(),
  accountId: nullableNumber(),
  rentalId: nullableNumber(),
  deepLinkCallback: nullableString(),
});

export const PasswordResetRequestSchema = z.object({
  email: nullableString(),
});

export const RegisterAccountRequestSchema = z.object({
  firstName: nullableString(),
  lastName: nullableString(),
  storageAccountNumber: nullableString(),
  email: nullableString(),
  phoneNumber: nullableString(),
  password: nullableString(),
});

export const FindAccountRequestSchema = z.object({
  phoneNumber: nullableString(),
  firstName: nullableString().optional(),
  lastName: nullableString(),
  locationId: nullableNumber(),
});

export const DisableAutoPayRequestSchema = z.object({
  rentalId: nullableNumber(),
  accountId: nullableNumber(),
  siteId: nullableNumber(),
});

export const SendPaymentReceiptRequestSchema = z.object({
  culture: nullableString(),
  accountId: nullableNumber(),
  rentalId: nullableNumber(),
  paymentReceiptNumber: nullableNumber(),
});

export const GetGateOptionsRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
});

export const OpenGateRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
  gateDeviceId: nullableString(),
  accessCode: nullableString(),
});

export const GetNokeLocksRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
});

export const GetNokeUnlockCommandRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
  lockSession: nullableString(),
  lockMacAddress: nullableString(),
});

export const NokeUnlockOverNetworkRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
  lockMacAddress: nullableString(),
});

export const NokeUpdateOnsiteStatusRequestSchema = z.object({
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
  isOnSite: nullableBoolean(),
});

/**
 * Request Body Types
 */
export type AddStorageAccountRequest = z.infer<
  typeof AddStorageAccountRequestSchema
>;
export type UpdateCustomerContactRequest = z.infer<
  typeof UpdateCustomerContactRequestSchema
>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordRequestSchema>;
export type SubmitSupportFormRequest = z.infer<
  typeof SubmitSupportFormRequestSchema
>;
export type AddBillingAddressRequest = z.infer<
  typeof AddBillingAddressRequestSchema
>;
export type DeleteBillingAddressRequest = z.infer<
  typeof DeleteBillingAddressRequestSchema
>;
export type SetPrimaryAddressRequest = z.infer<
  typeof SetPrimaryAddressRequestSchema
>;
export type GetPaymentHistoryRequest = z.infer<
  typeof GetPaymentHistoryRequestSchema
>;
export type PaymentHistoryRequest = z.infer<typeof PaymentHistoryRequestSchema>;
export type WebViewUrlRequest = z.infer<typeof WebViewUrlRequestSchema>;
export type PasswordResetRequest = z.infer<typeof PasswordResetRequestSchema>;
export type RegisterAccountRequest = z.infer<
  typeof RegisterAccountRequestSchema
>;
export type FindAccountRequest = z.infer<typeof FindAccountRequestSchema>;
export type DisableAutoPayRequest = z.infer<typeof DisableAutoPayRequestSchema>;
export type SendPaymentReceiptRequest = z.infer<
  typeof SendPaymentReceiptRequestSchema
>;
export type GetGateOptionsRequest = z.infer<typeof GetGateOptionsRequestSchema>;
export type OpenGateRequest = z.infer<typeof OpenGateRequestSchema>;
export type GetNokeLocksRequest = z.infer<typeof GetNokeLocksRequestSchema>;
export type GetNokeUnlockCommandRequest = z.infer<
  typeof GetNokeUnlockCommandRequestSchema
>;
export type NokeUnlockOverNetworkRequest = z.infer<
  typeof NokeUnlockOverNetworkRequestSchema
>;
export type NokeUpdateOnsiteStatusRequest = z.infer<
  typeof NokeUpdateOnsiteStatusRequestSchema
>;
