import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString } from './zodHelpers';

export const WebViewUrlSchema = z.object({
  webviewUrl: nullableString(),
});

export const ApiWebViewResponseSchema =
  BaseApiResponseSchema.merge(WebViewUrlSchema);

/**
 * API Types
 */
export type ApiWebViewResponse = z.infer<typeof ApiWebViewResponseSchema>;

/**
 * Frontend Adapter Types
 */
export type WebViewUrl = z.infer<typeof WebViewUrlSchema>;

