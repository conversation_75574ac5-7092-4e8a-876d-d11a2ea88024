import { z } from 'zod';
import { nullableString, nullableNumber } from './zodHelpers';

export const ApiTokenResponseSchema = z.object({
  access_token: nullableString(),
  token_type: nullableString(),
  expires_in: nullableNumber(),
  scope: nullableString(),
  id_token: nullableString(),
  refresh_token: nullableString(),
});

export const TokenDataSchema = z.object({
  accessToken: nullableString(),
  tokenType: nullableString(),
  expiresIn: nullableNumber(),
  scope: nullableString(),
  idToken: nullableString(),
  refreshToken: nullableString(),
  expiresAt: nullableString(),
});

export const tokenSchema = z.object({
  access_token: nullableString(),
  token_type: nullableString(),
  expires_in: nullableNumber(),
  scope: nullableString(),
  id_token: nullableString(),
  refresh_token: nullableString(),
  expires_at: nullableString(),
});

export type ApiTokenResponse = z.infer<typeof ApiTokenResponseSchema>;

export type TokenData = z.infer<typeof TokenDataSchema>;
