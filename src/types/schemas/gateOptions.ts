import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString, nullableBoolean, nullableNumber } from './zodHelpers';

export const ApiGateOptionsSchema = z.object({
  friendlyName: nullableString(),
  deviceId: nullableString(),
  isElevator: nullableBoolean(),
});

export const GateOptionsSchema = z.object({
  friendlyName: nullableString(),
  deviceId: nullableString(),
  isElevator: nullableBoolean(),
  tenantId: nullableNumber(),
  facilityId: nullableNumber(),
});

export const ApiGateOptionsResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    gateDeviceOptions: ApiGateOptionsSchema.array(),
  }),
);

/**
 * API Types
 */
export type ApiGateOptions = z.infer<typeof ApiGateOptionsSchema>;
export type ApiGateOptionsResponse = z.infer<
  typeof ApiGateOptionsResponseSchema
>;

/**
 *  Types
 */
export type GateOptions = z.infer<typeof GateOptionsSchema>;
