import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableBoolean, nullableNumber, nullableString } from './zodHelpers';

export const userSchema = z.object({
  name: nullableString(),
});

export const ContactInfoSchema = z.object({
  firstName: nullableString(),
  lastName: nullableString(),
  phone: nullableString(),
  email: nullableString(),
});

export const BillingAddressSchema = z.object({
  addressId: nullableNumber(),
  isPrimary: nullableBoolean(),
  address: nullableString(),
  appartment: nullableString(),
  city: nullableString(),
  state: nullableString(),
  zipCode: nullableString(),
});

export const AccountSchema = z.object({
  contactInfo: ContactInfoSchema,
  billingAddresses: BillingAddressSchema.array(),
  personalizedPrefillUrl: nullableString(),
});

export const ApiAccountSchema = z.object({
  contactInfo: ContactInfoSchema,
  billingAddresses: BillingAddressSchema.array(),
  personalizedPrefillUrl: nullableString(),
});

export const ApiAccountResponseSchema =
  BaseApiResponseSchema.merge(AccountSchema);

/**
 * API Types
 */
export type ApiAccountResponse = z.infer<typeof ApiAccountResponseSchema>;

/**
 * Frontend Adapter Types
 */
export type ContactInfo = z.infer<typeof ContactInfoSchema>;
export type BillingAddress = z.infer<typeof BillingAddressSchema>;
export type Account = z.infer<typeof AccountSchema>;
export type AccountData = z.infer<typeof AccountSchema>;
