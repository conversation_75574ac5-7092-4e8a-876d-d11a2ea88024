import { z } from 'zod';
import { nullableNumber, nullableString, nullableBoolean } from './zodHelpers';

export const ApiUnitSchema = z.object({
  FacilityId: nullableNumber(),
  AccountId: nullableNumber(),
  RentalId: nullableNumber(),
  UnitNumber: nullableString(),
  UnitSize: nullableString(),
  NextPaymentDue: nullableString(),
  Autopay: nullableBoolean(),
  NoPaymentReason: nullableString(),
  NextPayment: nullableString(),
  UnitTotal: nullableNumber(),
  Vacated: nullableBoolean(),
  CanMakePayment: nullableBoolean(),
  IsLate: nullableBoolean(),
  NextPaymentDueValue: nullableString(),
  PaidThruDate: nullableString(),
  LocationPhoneText: nullableString(),
  LocationPhoneDigits: nullableString(),
  CurrentInsurance: nullableString(),
});

export const UnitSchema = z.object({
  facilityId: nullableNumber(),
  accountId: nullableNumber(),
  rentalId: nullableNumber(),
  unitNumber: nullableString(),
  unitSize: nullableString(),
  nextPaymentDue: nullableString(),
  autopay: nullableBoolean(),
  noPaymentReason: nullableString(),
  nextPayment: nullableString(),
  unitTotal: nullableNumber(),
  vacated: nullableBoolean(),
  canMakePayment: nullableBoolean(),
  isLate: nullableBoolean(),
  nextPaymentDueValue: nullableString(),
  paidThruDate: nullableString(),
  locationPhoneText: nullableString(),
  locationPhoneDigits: nullableString(),
  currentInsurance: nullableString(),
});

/**
 * API Types
 */
export type ApiUnit = z.infer<typeof ApiUnitSchema>;

/**
 * Frontend Adapter Types
 */
export type Unit = z.infer<typeof UnitSchema>;
