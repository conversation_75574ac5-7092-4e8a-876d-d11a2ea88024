import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableString, nullableBoolean } from './zodHelpers';

export const FAQItemSchema = z.object({
  header: nullableString(),
  answer: nullableString(),
  featureOnLocations: nullableBoolean(),
});

export const SecondaryContentSchema = z.object({
  faqItems: FAQItemSchema.array(),
  privacyPolicyUrl: nullableString(),
  termsPoliciesUrl: nullableString(),
});

export const ApiSecondaryContentResponseSchema = BaseApiResponseSchema.merge(
  SecondaryContentSchema,
);

/**
 * API Types
 */
export type ApiSecondaryContentResponse = z.infer<
  typeof ApiSecondaryContentResponseSchema
>;

/**
 *  Types
 */
export type FAQItem = z.infer<typeof FAQItemSchema>;
export type SecondaryContent = z.infer<typeof SecondaryContentSchema>;
