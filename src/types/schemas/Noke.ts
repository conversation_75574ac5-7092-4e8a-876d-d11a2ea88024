import { z } from 'zod';
import { BaseApiResponseSchema } from './apiResponse';
import { nullableNumber, nullableString } from './zodHelpers';

export const ApiLockSchema = z.object({
  id: nullableNumber(),
  mac: nullableString(),
  name: nullableString(),
  offlineKey: nullableString(),
  offlineKeyObj: z.object({
    offlineExpiration: nullableString(),
    offlineKey: nullableString(),
  }),
  unlockCmd: nullableString(),
  scheduledUnlockCmd: nullableString(),
});

export const LockSchema = z.object({
  id: nullableNumber(),
  mac: nullableString(),
  name: nullableString(),
  offlineKey: nullableString(),
  offlineKeyObj: z.object({
    offlineExpiration: nullableString(),
    offlineKey: nullableString(),
  }),
  unlockCmd: nullableString(),
  scheduledUnlockCmd: nullableString(),
  facilityId: nullableNumber(),
  tenantId: nullableNumber(),
});

export const unitSchema = z.object({
  locks: ApiLockSchema.array().optional(),
});

export const LocksSchema = z.object({
  units: unitSchema.array().optional(),
});

export const CommandsSchema = z.object({
  commands: nullableString().array().optional(),
});

function NokeResponseSchema<T extends z.ZodTypeAny>(dataSchema?: T) {
  return z.object({
    result: nullableString(),
    message: nullableString(),
    errorCode: nullableNumber(),
    token: nullableString(),
    request: nullableString(),
    currentTime: nullableNumber(),
    data: dataSchema || z.null().optional(),
  });
}

export const ApiNokeLocksResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    nokeResponse: NokeResponseSchema(LocksSchema).optional(),
  }),
);

export const ApiNokeUnlockCommandResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    nokeResponse: NokeResponseSchema(CommandsSchema).optional(),
  }),
);

export const ApiNokeGenericResponseSchema = BaseApiResponseSchema.merge(
  z.object({
    nokeResponse: NokeResponseSchema().optional(),
  }),
);

export type ApiNokeLockResponse = z.infer<typeof ApiNokeLocksResponseSchema>;
export type ApiNokeUnlockCommandResponse = z.infer<
  typeof ApiNokeUnlockCommandResponseSchema
>;
export type ApiNokeGenericResponse = z.infer<
  typeof ApiNokeGenericResponseSchema
>;

export type ApiNokeLock = z.infer<typeof ApiLockSchema>;
export type NokeLock = z.infer<typeof LockSchema>;
