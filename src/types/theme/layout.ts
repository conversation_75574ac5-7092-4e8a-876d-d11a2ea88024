import { config } from '@/theme/_config';
import { staticLayoutStyles } from '@/theme/layout';

import type {
  ArrayValue,
  RemoveBeforeSeparator,
  RemoveAfterSeparator,
  ToNumber,
} from './common';

type Layouts = 'w' | 'h';

type LayoutHSizesKeys = `h_${ArrayValue<typeof config.layout.sizes>}`;
type LayoutWSizesKeys = `w_${ArrayValue<typeof config.layout.sizes>}`;

type LayoutPercent = 'percentW' | 'percentH';

type LayoutPercentKeys = `${LayoutPercent}_${ArrayValue<
  typeof config.layout.percents
>}`;

export type LayoutWidthSizes = {
  [key in LayoutWSizesKeys]: {
    width: ToNumber<RemoveBeforeSeparator<key>>;
  };
};

export type LayoutHeightSizes = {
  [key in LayoutHSizesKeys]: {
    height: ToNumber<RemoveBeforeSeparator<key>>;
  };
};

export type LayoutSizes = LayoutWidthSizes & LayoutHeightSizes;

export type LayoutPercents = {
  [key in LayoutPercentKeys]: {
    [K in Extract<RemoveAfterSeparator<key>, Layouts>]: ToNumber<
      RemoveBeforeSeparator<key>
    >;
  };
};

export type Layout = LayoutWidthSizes &
  LayoutHeightSizes &
  LayoutPercents &
  typeof staticLayoutStyles;
