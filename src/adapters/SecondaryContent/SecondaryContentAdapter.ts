import {
  ApiSecondaryContentResponse,
  SecondaryContent,
} from '@/types/schemas/secondaryContent';

export class SecondaryContentAdapter {
  private value: ApiSecondaryContentResponse;

  constructor(obj: ApiSecondaryContentResponse) {
    this.value = obj;
  }

  adapt(): SecondaryContent {
    return {
      faqItems: this.value.faqItems,
      privacyPolicyUrl: this.value.privacyPolicyUrl,
      termsPoliciesUrl: this.value.termsPoliciesUrl,
    };
  }
}
