import { ApiUnit, Unit } from '@/types/schemas/unit';

export class UnitAdapter {
  private value: ApiUnit;

  constructor(obj: ApiUnit) {
    this.value = obj;
  }

  adapt(): Unit {
    return {
      facilityId: this.value.FacilityId,
      accountId: this.value.AccountId,
      rentalId: this.value.RentalId,
      unitNumber: this.value.UnitNumber,
      unitSize: this.value.UnitSize,
      nextPaymentDue: this.value.NextPaymentDue,
      autopay: this.value.Autopay,
      noPaymentReason: this.value.NoPaymentReason,
      nextPayment: this.value.NextPayment,
      unitTotal: this.value.UnitTotal,
      vacated: this.value.Vacated,
      canMakePayment: this.value.CanMakePayment,
      isLate: this.value.IsLate,
      nextPaymentDueValue: this.value.NextPaymentDueValue,
      paidThruDate: this.value.PaidThruDate,
      locationPhoneText: this.value.LocationPhoneText,
      locationPhoneDigits: this.value.LocationPhoneDigits,
    };
  }
}
