import { DateTime } from 'luxon';

import { ApiTokenResponse, TokenData } from '@/types/schemas/tokenData';

export class TokensAdapter {
  private value: ApiTokenResponse;

  constructor(obj: ApiTokenResponse) {
    this.value = obj;
  }

  get expiresAt() {
    return DateTime.now().plus({ seconds: this.value.expires_in }).toISO();
  }

  adapt(): TokenData {
    return {
      accessToken: this.value.access_token,
      tokenType: this.value.token_type,
      expiresIn: this.value.expires_in,
      scope: this.value.scope,
      idToken: this.value.id_token,
      refreshToken: this.value.refresh_token,
      expiresAt: this.expiresAt,
    };
  }
}
