import { ApiLocation, Location } from '@/types/schemas/location';
import { UnitAdapter } from '../Units/UnitAdapter';

export class LocationAdapter {
  private value: ApiLocation;

  constructor(obj: ApiLocation) {
    this.value = obj;
  }

  get image() {
    return {
      url: this.value.Image.Url,
      altText: this.value.Image.AltText,
    };
  }

  get officeHours() {
    return this.value.OfficeHours.map(officeHours => {
      return {
        day: officeHours.Day,
        hours: officeHours.Hours,
      };
    });
  }

  get gateHours() {
    return this.value.GateHours.map(gateHours => {
      return {
        day: gateHours.Day,
        hours: gateHours.Hours,
      };
    });
  }

  get units() {
    return this.value.Units.map(unit => {
      return new UnitAdapter(unit).adapt();
    });
  }

  adapt(): Location {
    return {
      image: this.image,
      officeHours: this.officeHours,
      gateHours: this.gateHours,
      locationId: this.value.LocationId,
      address: this.value.Address,
      city: this.value.City,
      state: this.value.State,
      zip: this.value.Zip,
      phone: this.value.Phone,
      rating: this.value.Rating,
      newReviewUrl: this.value.NewReviewUrl,
      units: this.units,
      latitude: this.value.latitude,
      longitude: this.value.longitude,
    };
  }
}
