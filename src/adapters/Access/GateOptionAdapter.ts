import {
  ApiGateOptions,
  GateOptions,
  GateOptionsSchema,
} from '@/types/schemas/gateOptions';

export class GateOptionAdapter {
  private value: ApiGateOptions;

  constructor(obj: ApiGateOptions) {
    this.value = obj;
  }

  adapt(facilityId: number, tenantId: number): GateOptions {
    return GateOptionsSchema.parse({
      friendlyName: this.value.friendlyName,
      deviceId: this.value.deviceId,
      isElevator: this.value.isElevator,
      tenantId,
      facilityId,
    });
  }
}
