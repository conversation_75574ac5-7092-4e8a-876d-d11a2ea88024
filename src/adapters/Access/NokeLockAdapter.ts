import { Api<PERSON><PERSON>Lock, LockSchema, NokeLock } from '@/types/schemas/Noke';

export class NokeLockAdapter {
  private value: ApiNokeLock;

  constructor(obj: ApiNokeLock) {
    this.value = obj;
  }

  adapt(facilityId: number, tenantId: number): NokeLock {
    return LockSchema.parse({
      id: this.value.id,
      mac: this.value.mac,
      name: this.value.name,
      offlineKey: this.value.offlineKey,
      offlineKeyObj: this.value.offlineKeyObj,
      unlockCmd: this.value.unlockCmd,
      scheduledUnlockCmd: this.value.scheduledUnlockCmd,
      facilityId,
      tenantId,
    });
  }
}
