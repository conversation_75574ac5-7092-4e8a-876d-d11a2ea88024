import { AccountData, ApiAccountResponse } from '@/types/schemas/account';

export class AccountAdapter {
  private value: ApiAccountResponse;

  constructor(obj: ApiAccountResponse) {
    this.value = obj;
  }

  adapt(): AccountData {
    return {
      contactInfo: {
        firstName: this.value.contactInfo.firstName,
        lastName: this.value.contactInfo.lastName,
        phone: this.value.contactInfo.phone,
        email: this.value.contactInfo.email,
      },
      billingAddresses: this.value.billingAddresses,
      personalizedPrefillUrl: this.value.personalizedPrefillUrl,
    };
  }
}
