import { useTheme } from '@/theme';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Example, Game, Onboarding, Startup } from '@/screens';
import { Platform, UIManager } from 'react-native';
import { ApplicationStackParamList } from '@/types/navigation';
import { useLinkTo } from '@react-navigation/native';
import { To } from '@react-navigation/native/lib/typescript/src/useLinkTo';
import React, { useEffect } from 'react';
import LopoSVG from '@/assets/svg/Logo';
import MainNavigator from './Main';
import RegisterNavigator from './Register';
import LoginNavigator from './Login';

interface AppStackProps {
  onLinkToCreated: (
    linkTo: (
      to: To<
        ReactNavigation.RootParamList,
        keyof ReactNavigation.RootParamList
      >,
    ) => void,
  ) => void;
}

if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

function AppStack({ onLinkToCreated }: AppStackProps) {
  const { variant } = useTheme();
  const Stack = createNativeStackNavigator<ApplicationStackParamList>();
  const linkTo = useLinkTo();

  useEffect(() => {
    onLinkToCreated(linkTo);
  }, []);

  const renderheader = () => <LopoSVG />;

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{
        headerShown: false,
        headerStyle: {
          backgroundColor: '#006CAF',
        },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen name="Startup" component={Startup} />
      <Stack.Screen name="Onboarding" component={Onboarding} />
      <Stack.Screen name="Main" component={MainNavigator} />
      <Stack.Screen name="Example" component={Example} />
      <Stack.Screen name="Game" component={Game} />

      {/* Define Modals Here per react-navigation best practice */}
      <Stack.Group
        screenOptions={{
          headerShown: true,
          headerBackVisible: false,
          headerTitle: renderheader,
          headerStyle: {
            backgroundColor: '#006CAF',
          },
          headerShadowVisible: false,
          headerTitleAlign: 'center',
        }}
      >
        <Stack.Screen name="Login" component={LoginNavigator} />
      </Stack.Group>
      <Stack.Group
        screenOptions={{
          headerShown: true,
          headerBackVisible: false,
          headerTitle: renderheader,
          headerStyle: {
            backgroundColor: '#006CAF',
          },
          headerTitleAlign: 'center',
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="Register" component={RegisterNavigator} />
      </Stack.Group>
    </Stack.Navigator>
  );
}

export default AppStack;
