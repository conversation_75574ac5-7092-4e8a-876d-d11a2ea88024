import React, { useEffect, useState } from 'react';
import {
  NavigationContainer,
  useNavigationContainerRef,
  NavigationState,
  PartialState,
} from '@react-navigation/native';
import { useTheme } from '@/theme';
import useAuthStateManager from '@/hooks/useAuthStateManager';
import branch from 'react-native-branch';
import * as Sentry from '@sentry/react-native';
import AppStack from './AppStack';

function ApplicationNavigator() {
  const { navigationTheme } = useTheme();
  const navRef = useNavigationContainerRef();
  const { onNavReady, onLinkToCreated, onDeepLinkOpened } =
    useAuthStateManager(navRef);
  const [focusedRouteName, setFocusedRouteName] = useState('');

  useEffect(() => {
    const unsubscribe = branch.subscribe(({ error, params }) => {
      if (error) {
        console.error(error);
        return;
      }

      console.log('DEEPLINK: branch callback', params);

      const route = params?.route as string;
      if (route) {
        onDeepLinkOpened(route);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const getFocusedRouteName = (navState: NavigationState): string => {
    const route = navState.routes[navState.index];

    if (route.state) {
      // routes containing their own state have deeper nested routes
      return getFocusedRouteName(route.state as NavigationState);
    }

    // no state means we are looking at our focused route
    return route.name;
  };

  const linking = {
    prefixes: ['SmartStop://'],
    config: {
      screens: {
        Main: {
          screens: {
            Home: 'home',
            Payments: 'payments',
            Access: 'access',
            Account: 'account',
          },
        },
        UnitNotes: 'notes',
        Login: 'login',
        Register: 'register',
      },
    },
  };

  return (
    <NavigationContainer
      ref={navRef}
      theme={navigationTheme}
      linking={linking}
      onReady={() => onNavReady()}
      onStateChange={newNavState => {
        const newFocusedRouteName = getFocusedRouteName(
          newNavState as NavigationState,
        );
        if (newFocusedRouteName !== focusedRouteName) {
          Sentry.addBreadcrumb({
            category: 'navigation',
            data: {
              to: newFocusedRouteName,
              from: focusedRouteName,
            },
            level: 'info',
          });
          setFocusedRouteName(newFocusedRouteName);
        }
      }}
    >
      <AppStack onLinkToCreated={onLinkToCreated} />
    </NavigationContainer>
  );
}

export default ApplicationNavigator;
