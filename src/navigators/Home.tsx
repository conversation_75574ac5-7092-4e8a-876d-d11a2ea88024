import React, { useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';
import {
  FAQs,
  FindStorageNumber,
  Home,
  LocationDetails,
  PaymentWebView,
  StorageNumberEmailSent,
  Support,
  TermsPrivacyWebView,
  UnitNotes,
} from '@/screens';
import Notifications from '@/screens/Main/Notifications';

const Stack = createNativeStackNavigator();

function HomeNavigator() {
  const { variant, fonts } = useTheme();
  const [badge, setBadge] = useState<boolean>(false);

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{
        headerShown: true,
        headerBackTitleVisible: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerStyle: {
          backgroundColor: '#006CAF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          color: 'white',
        },
      }}
    >
      <Stack.Screen
        name="MainHome"
        component={Home}
        initialParams={{ badge, setBadge }}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="MainLocationDetails"
        component={LocationDetails}
        options={{
          title: 'Location',
        }}
      />
      <Stack.Screen
        name="PaymentWebView"
        component={PaymentWebView}
        options={{
          title: 'Payments',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="MainLocationUnitNotes"
        component={UnitNotes}
        options={{
          title: 'Unit Notes',
        }}
      />
      <Stack.Screen
        name="MainNotifications"
        component={Notifications}
        options={{
          title: 'Notifications',
        }}
      />
      <Stack.Screen
        name="Support"
        component={Support}
        options={{
          title: 'Support',
        }}
      />
      <Stack.Screen name="FindStorageNumber" component={FindStorageNumber} />
      <Stack.Screen
        name="StorageNumberEmailSent"
        component={StorageNumberEmailSent}
      />
      <Stack.Screen
        name="TermsPrivacyWebView"
        component={TermsPrivacyWebView}
        options={{
          title: '',
        }}
      />
      <Stack.Screen
        name="FAQs"
        component={FAQs}
        options={{
          title: 'FAQs',
        }}
      />
    </Stack.Navigator>
  );
}

export default HomeNavigator;
