import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';
import { Access, NokeTest, Settings, Support } from '@/screens';
import { HamburgerMenuButton } from '@/components/atoms';
import {
  AccessScreenProps,
  DrawerParamList,
  MainDrawerScreenProps,
} from '@/types/navigation';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { StatusBar } from 'react-native';

const Stack = createNativeStackNavigator();

function AccessNavigator({ navigation }: AccessScreenProps) {
  const { variant, fonts } = useTheme();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  const renderHeaderLeft = ({ navigation: nav }: MainDrawerScreenProps) => {
    return <HamburgerMenuButton navigation={nav} />;
  };

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{
        headerShown: true,
        headerShadowVisible: false,
        headerBackTitleVisible: false,
        headerTitleAlign: 'center',
        headerStyle: {
          backgroundColor: '#006CAF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          color: 'white',
        },
      }}
    >
      <Stack.Screen
        name="AccessHome"
        component={Access}
        options={({
          navigation: nav,
        }: {
          navigation: DrawerNavigationProp<DrawerParamList>;
        }) => ({
          title: 'Gate Access',
          headerLeft: () => renderHeaderLeft({ navigation: nav }),
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        })}
      />
      <Stack.Screen
        name="AccountSettings"
        component={Settings}
        options={{
          title: 'Settings',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="Support"
        component={Support}
        options={{
          title: 'Support',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="NokeTest"
        component={NokeTest}
        options={{ title: 'Noke Test' }}
      />
    </Stack.Navigator>
  );
}

export default AccessNavigator;
