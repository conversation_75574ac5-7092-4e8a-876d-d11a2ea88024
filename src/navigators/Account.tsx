import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';
import {
  Account,
  ChangePassword,
  EditProfile,
  PaymentHistory,
  Settings,
  Support,
} from '@/screens';
import { ActionSheet, HamburgerMenuButton } from '@/components/atoms';
import {
  AccountScreenProps,
  DrawerParamList,
  MainDrawerScreenProps,
} from '@/types/navigation';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import Notifications from '@/screens/Main/Notifications';
import { StatusBar } from 'react-native';

const Stack = createNativeStackNavigator();

function AccountNavigator({ navigation }: AccountScreenProps) {
  const { variant, fonts } = useTheme();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  const renderHeaderLeft = ({ navigation: nav }: MainDrawerScreenProps) => {
    return <HamburgerMenuButton navigation={nav} />;
  };

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{
        headerShown: true,
        headerBackTitleVisible: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerStyle: {
          backgroundColor: '#006CAF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          color: 'white',
        },
      }}
    >
      <Stack.Screen
        name="AccountHome"
        component={Account}
        options={({
          navigation: nav,
        }: {
          navigation: DrawerNavigationProp<DrawerParamList>;
        }) => ({
          title: 'My Account',
          headerLeft: () => renderHeaderLeft({ navigation: nav }),
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        })}
      />
      <Stack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{
          title: 'Edit Profile',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="AccountSettings"
        component={Settings}
        options={{
          title: 'Settings',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="MainNotifications"
        component={Notifications}
        options={{
          title: 'Notifications',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="Support"
        component={Support}
        options={{
          title: 'Support',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{
          title: 'Change Password',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="ActionSheet"
        component={ActionSheet}
        options={{
          headerShown: false,
          presentation: 'transparentModal',
        }}
      />
      <Stack.Screen
        name="PaymentHistory"
        component={PaymentHistory}
        options={{
          title: 'Payment History',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
    </Stack.Navigator>
  );
}

export default AccountNavigator;
