import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';

import {
  RegisterUser,
  FindStorageNumber,
  StorageNumberEmailSent,
} from '@/screens';

import type { RegisterStackParamList } from '@/types/navigation';

const Stack = createNativeStackNavigator<RegisterStackParamList>();

function RegisterNavigator() {
  const { variant } = useTheme();

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{ headerShown: false, gestureEnabled: false }}
    >
      <Stack.Screen name="RegisterUser" component={RegisterUser} />
      <Stack.Screen name="FindStorageNumber" component={FindStorageNumber} />
      <Stack.Screen
        name="StorageNumberEmailSent"
        component={StorageNumberEmailSent}
      />
    </Stack.Navigator>
  );
}

export default RegisterNavigator;
