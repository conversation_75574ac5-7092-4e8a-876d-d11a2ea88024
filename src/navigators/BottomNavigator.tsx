import React, { useContext } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTheme } from '@/theme';
import { renderIcon } from '@/utils/icons';
import { Platform } from 'react-native';
import AccessNavigator from './Access';
import AccountNavigator from './Account';
import HomeNavigator from './Home';
import { NavigationContext } from '../screens/Main/Home';
import PaymentsNavigator from './Payments';

const Tab = createBottomTabNavigator();

function BottomTabNavigator() {
  const { hasStorage } = useContext(NavigationContext);
  const { variant, fonts } = useTheme();

  return (
    <Tab.Navigator
      key={variant}
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        tabBarActiveBackgroundColor: '#E6F2F9',
        tabBarActiveTintColor: '#0077B8',
        tabBarInactiveTintColor: '#0077B8',
        tabBarHideOnKeyboard: true,
        tabBarStyle: {
          paddingLeft: 12,
          paddingRight: 12,
          paddingBottom: Platform.OS === 'android' ? 12 : 30,
          backgroundColor: '#FFFFFF',
          height: Platform.OS === 'ios' ? 98 : 80,
          // borderTopLeftRadius: 30,
          // borderTopRightRadius: 30,
          overflow: 'hidden',
        },
        tabBarItemStyle: {
          paddingBottom: 12,
          paddingTop: 12,
          borderBottomRightRadius: 12,
          borderBottomLeftRadius: 12,
        },
        tabBarLabelStyle: {
          fontFamily: fonts.appFontMedium.fontFamily,
          fontSize: fonts.size_12.fontSize,
          color: fonts.blue.color,
          fontWeight: '700',
          letterSpacing: 1.2,
        },
        tabBarAllowFontScaling: true,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeNavigator}
        options={{
          title: 'HOME',
          tabBarIcon: ({ color }) => renderIcon('Home', color),
        }}
      />
      {!hasStorage && (
        <>
          <Tab.Screen
            name="Access"
            component={AccessNavigator}
            options={{
              title: 'ACCESS',
              tabBarIcon: ({ color }) => renderIcon('Key', color),
            }}
          />
          <Tab.Screen
            name="Payments"
            component={PaymentsNavigator}
            options={{
              title: 'PAYMENTS',
              tabBarIcon: ({ color }) => renderIcon('CreditCard', color),
            }}
          />
        </>
      )}
      <Tab.Screen
        name="Account"
        component={AccountNavigator}
        options={{
          title: 'ACCOUNT',
          tabBarIcon: ({ color }) => renderIcon('Account', color),
        }}
      />
    </Tab.Navigator>
  );
}

export default BottomTabNavigator;
