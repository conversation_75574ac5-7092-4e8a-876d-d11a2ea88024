import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';
import {
  AutoPayTurnOffAlert,
  AutoPayTurnOffSuccess,
  FindStorageNumber,
  PaymentHistory,
  PaymentWebView,
  Payments,
  SavedAddress,
  StorageNumberEmailSent,
} from '@/screens';
import { HamburgerMenuButton } from '@/components/atoms';
import {
  DrawerParamList,
  MainDrawerScreenProps,
  PaymentsScreenProps,
} from '@/types/navigation';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import AddBillingAddress from '@/screens/Main/AddBillingAddress';
import { StatusBar } from 'react-native';

const Stack = createNativeStackNavigator();

function PaymentsNavigator({ navigation }: PaymentsScreenProps) {
  const { variant, fonts } = useTheme();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      StatusBar.setBarStyle('light-content');
    });

    return unsubscribe;
  }, [navigation]);

  const renderHeaderLeft = ({ navigation: nav }: MainDrawerScreenProps) => {
    return <HamburgerMenuButton navigation={nav} />;
  };

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{
        headerShown: true,
        headerBackTitleVisible: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerStyle: {
          backgroundColor: '#006CAF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          color: 'white',
        },
      }}
    >
      <Stack.Screen
        name="PaymentsHome"
        component={Payments}
        options={({
          navigation: nav,
        }: {
          navigation: DrawerNavigationProp<DrawerParamList>;
        }) => ({
          title: 'Payments',
          headerLeft: () => renderHeaderLeft({ navigation: nav }),
        })}
      />
      <Stack.Screen
        name="PaymentHistory"
        component={PaymentHistory}
        options={{
          title: 'Payment History',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="PaymentWebView"
        component={PaymentWebView}
        options={{
          title: 'Payments',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="AutoPayTurnOffAlert"
        component={AutoPayTurnOffAlert}
        options={{
          presentation: 'transparentModal',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AutoPayTurnOffSuccess"
        component={AutoPayTurnOffSuccess}
        options={{
          presentation: 'transparentModal',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="SavedAddress"
        component={SavedAddress}
        options={{
          title: 'Manage Billing Address',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen
        name="AddBillingAddress"
        component={AddBillingAddress}
        options={{
          title: 'Manage Billing Address',
          headerTitleStyle: [fonts.white, fonts.size_18, fonts.appFontMedium],
        }}
      />
      <Stack.Screen name="FindStorageNumber" component={FindStorageNumber} />
      <Stack.Screen
        name="StorageNumberEmailSent"
        component={StorageNumberEmailSent}
      />
    </Stack.Navigator>
  );
}

export default PaymentsNavigator;
