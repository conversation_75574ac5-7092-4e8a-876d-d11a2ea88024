/* eslint-disable react-native/no-inline-styles */
import React, {
  useState,
  useMemo,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import {
  createDrawerNavigator,
  DrawerContentComponentProps,
  DrawerItem,
  DrawerItemList,
  useDrawerStatus,
} from '@react-navigation/drawer';
import { Dimensions, Linking, Platform, ScrollView, StatusBar, TouchableOpacity, View } from 'react-native';

import { useTheme } from '@/theme';
import { CloseSVG, LogoSVG } from '@/assets/svg';
import { SafeScreen } from '@/components/template';
import { MainTabNavParamList } from '@/types/navigation';
import { renderIcon } from '@/utils/icons';

import signout from '@/services/accounts/signout';
import { AccountData } from '@/types/schemas/account';
import { getUserInfoData } from '@/utils/storage';
import { HeaderBackButton } from '@react-navigation/elements';
import { SS_LINKS } from '@/utils/linking';
import BottomTabNavigator from './BottomNavigator';
import { NavigationContext } from '../screens/Main/Home';

const Drawer = createDrawerNavigator();

const DRAWER_SCREEN_NAMES = [
  'DrawerHome',
  'DrawerPayments',
  'DrawerAccess',
  'DrawerAccount',
];

const DRAWER_SCREEN_TO_TAB: { [key: string]: keyof MainTabNavParamList } = {
  DrawerHome: 'Home',
  DrawerPayments: 'Payments',
  DrawerAccess: 'Access',
  DrawerAccount: 'Account',
};

function CustomDrawerContent(props: DrawerContentComponentProps) {
  const { gutters, layout, backgrounds, fonts } = useTheme();
  const [accountData] = useState<AccountData | null>(getUserInfoData());
  const { navigation } = props;
  const isOpen = useDrawerStatus() === 'open';

  useEffect(() => {
    if (isOpen) {
      StatusBar.setBarStyle('light-content');
    }
  }, [isOpen]);

  return (
    <SafeScreen style={[backgrounds.bluePrimary]}>
      <View
        style={[
          layout.row,
          layout.itemsCenter,
          layout.justifyCenter,
          gutters.paddingHorizontal_16,
          {
            height: 40,
          },
        ]}
      >

        <LogoSVG />
        <View
          style={[layout.absolute, { right: 16 }]}
        >
          <TouchableOpacity style={[
            layout.itemsCenter,
            layout.justifyCenter,
          ]} onPress={() => props.navigation.closeDrawer()}>
            <CloseSVG width={20} height={20} fill="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView contentContainerStyle={[layout.flexGrow_1]}>
        <View style={[layout.flex_1]}>
          <DrawerItemList {...props} />

          <View style={[gutters.marginTop_40]} />
          <DrawerItem
            label="FAQs"
            onPress={() => navigation.navigate('FAQs')}
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
          <DrawerItem
            label="Support"
            onPress={() => navigation.navigate('Support')}
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
          <DrawerItem
            label="Rent A Unit"
            onPress={() => {
              void Linking.openURL(
                accountData?.personalizedPrefillUrl ?? SS_LINKS.HOME,
              );
            }}
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
          <DrawerItem
            label="Privacy Policy"
            onPress={() =>
              navigation.navigate({
                name: 'TermsPrivacyWebView',
                params: {
                  isTermsConditions: false,
                  title: 'Privacy Policy',
                },
              })
            }
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
          <DrawerItem
            label="Terms & Conditions"
            onPress={() =>
              navigation.navigate({
                name: 'TermsPrivacyWebView',
                params: {
                  isTermsConditions: true,
                  title: 'Terms & Conditions',
                },
              })
            }
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
        </View>

        <View
          style={[layout.flex_1, layout.justifyEnd, gutters.paddingBottom_20]}
        >
          <View
            style={[
              { backgroundColor: '#3398CF' },
              gutters.marginLeft_16,
              gutters.marginRight_16,
              layout.h_1,
            ]}
          />
          <DrawerItem
            label="Log Out"
            icon={() => renderIcon('Logout', '#ffffff')}
            onPress={() => void signout()}
            labelStyle={[fonts.white, fonts.size_20, fonts.medium]}
          />
        </View>
      </ScrollView>
    </SafeScreen>
  );
}

// Define the context type
type ErrorContextType = {
  hasStorage: boolean;
  setHasStorageState: Dispatch<SetStateAction<boolean>>;
};

function MainNavigator() {
  const { variant } = useTheme();
  const [hasStorage, setHasStorage] = useState(false);
  const iconColor = '#FFFFFF';

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue: ErrorContextType = useMemo(
    () => ({ hasStorage, setHasStorageState: setHasStorage }),
    [hasStorage],
  );

  return (
    <NavigationContext.Provider value={contextValue}>
      <Drawer.Navigator
        key={variant}
        screenListeners={({ navigation, route }) => ({
          focus: () => {
            const nav =
              navigation as BottomTabNavigationProp<MainTabNavParamList>;
            if (DRAWER_SCREEN_NAMES.includes(route.name)) {
              const mappedRoute = DRAWER_SCREEN_TO_TAB[route.name];
              nav.navigate(mappedRoute);
            }
          },
        })}
        initialRouteName="DrawerMain"
        screenOptions={{
          headerShown: false,
          headerShadowVisible: false,
          drawerType: 'front',
          drawerStyle: {
            width: Dimensions.get('window').width * 0.75,
            backgroundColor: 'transparent',
          },
          drawerItemStyle: {
            borderRadius: 0,
            borderBottomWidth: 1,
            borderBottomColor: '#3398CF',
          },
          drawerLabelStyle: {
            color: '#FFFFFF',
            fontSize: 24,
            fontWeight: '500',
            marginLeft: -16,
          },
        }}
        drawerContent={CustomDrawerContent}
      >
        {/* Set as the initial screen for the drawer but hidden from the drawer items.
        The Drawer.Navigator has screenListeners set for when the drawer items that
        match the bottom nav items are "focused", we'll immediately use the navigate
        function to navigate to the bottom nav screen instead of rendering the 
        drawer screen component.
      */}
        <Drawer.Screen
          name="DrawerMain"
          options={{ drawerItemStyle: { display: 'none' } }}
          component={BottomTabNavigator}
        />

        <Drawer.Screen
          name="DrawerHome"
          options={{
            drawerLabel: 'Home',
            drawerIcon: () => renderIcon('Home', iconColor),
          }}
          component={View}
        />
        {!hasStorage && (
          <>
            <Drawer.Screen
              name="DrawerAccess"
              options={{
                drawerLabel: 'Access',
                drawerIcon: () => renderIcon('Key', iconColor),
              }}
              component={View}
            />
            <Drawer.Screen
              name="DrawerPayments"
              options={{
                drawerLabel: 'Payments',
                drawerIcon: () => renderIcon('CreditCard', iconColor),
              }}
              component={View}
            />
          </>
        )}
        <Drawer.Screen
          name="DrawerAccount"
          options={{
            drawerLabel: 'Account',
            drawerIcon: () => renderIcon('Account', iconColor),
          }}
          component={View}
        />
      </Drawer.Navigator>
    </NavigationContext.Provider>
  );
}

export default MainNavigator;
