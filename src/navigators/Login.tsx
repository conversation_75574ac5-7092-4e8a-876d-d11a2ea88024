import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@/theme';

import { LoginScreen, ResetPassword, StorageNumberEmailSent } from '@/screens';

import type { RegisterStackParamList } from '@/types/navigation';

const Stack = createNativeStackNavigator<RegisterStackParamList>();

function LoginNavigator() {
  const { variant } = useTheme();

  return (
    <Stack.Navigator
      key={variant}
      screenOptions={{ headerShown: false, gestureEnabled: false }}
    >
      <Stack.Screen name="LoginScreen" component={LoginScreen} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
      <Stack.Screen
        name="StorageNumberEmailSent"
        component={StorageNumberEmailSent}
      />
    </Stack.Navigator>
  );
}

export default LoginNavigator;
