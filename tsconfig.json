{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"types": ["jest", "node"], "target": "esnext", "module": "esnext", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "react-native", "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["./src/**/*", "./src/@types/**/*", "index.d.ts", ".eslintrc.js", "jest.setup.js", "jest.config.js", "babel.config.js", "index.js"]}