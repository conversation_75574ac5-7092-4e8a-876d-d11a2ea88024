# SmartStop

## Environment files

This project utilizes `react-native-config` to expose configuration variables to
the codebase contained within a file. This project currently utilizes the
following environment files:

- [.env.production](https://medlenterprises.atlassian.net/wiki/spaces/SmartStop/pages/2534244370/Production)
- [.env.staging](https://medlenterprises.atlassian.net/wiki/spaces/SmartStop/pages/2534244380/Staging)

> NOTE: When testing via iOS, any change to the environment files won't take
> effect until you **Clean the build folder**!

### Environments: iOS

The iOS project uses "schemes" to designate the app environment. Current schemes:

- **SmartStop Staging**: Runs and Builds against the staging API and services.
- **SmartStop**: Runs and Builds against the Production API and services.

### Environments: Android

The Android project utilizes product flavors to designate the app environment. Current flavors:

- **stagingDebug**: Runs a debug version of staging.
- **stagingRelease**: Builds a release version against the staging envrionment.
- **productionDebug**: Runs a debug version of production.
- **productionRelease**: Builds a release version against the production environment.

## Github Actions

Github actions are being used to distribute app builds to Firebase App Distribution
If for any reason the iOS provisioning profiles or certs need to be updated, you'll
need to base64 encode the respective file and update the environment variable in
GitHub.

```bash
base64 -i SmartStop_Staging_In_House.mobileprovision | pbcopy
```

> The above command used in the terminal will base64 encode the provisioning profile
> and copy it to your paste bin (clipboard) so you can paste it where ever you need to.

There are two environments in Github Actions setup currently: Stage, and Production

## Stage

This repreesnts the staging environment and apps built in this environment will
be using the staging API and services. It is currently setup to distrubute using
MEDLs Enterprise Distribution for iOS.

## Production

Represents the production environment and apps built in this Github environment will
be using the production API and services. It is currently setup to distrubute using
MEDLs Enterprise Distribution for iOS. Eventually this should be updated to distribute
to the app store testing tracks (Testflight for iOS and a beta test track in Google Play).

## Run Configurations

### Prerequisites

- Node.js (v16 or higher)
- React Native development environment setup
- Xcode (for iOS development)
- Android Studio (for Android development)
- CocoaPods (for iOS dependencies)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-org/SmartStop.git
cd SmartStop
```

2. Install dependencies:

```bash
npm install
```

3. Install iOS dependencies:

```bash
cd ios
pod install
cd ..
```

### Running the App

#### iOS

1. Open the iOS workspace in Xcode:

```bash
cd ios
open SmartStop.xcworkspace
```

2. Select your desired scheme (SmartStop Staging or SmartStop)
3. Select your target device/simulator
4. Click Run or use Command+R

Alternatively, from the command line:

```bash
# Run on iOS simulator with staging environment
npm run ios:staging

# Run on iOS simulator with production environment
npm run ios
```

#### Android

1. Start an Android emulator or connect a physical device

2. Run the app with desired environment:

```bash
# Run staging debug build
npm run android:staging

# Run production debug build
npm run android
```

### Debugging

- For React Native debugging: Shake the device or press Command+D (iOS simulator) or Command+M (Android emulator)
- For native code debugging: Use Xcode's debugger (iOS) or Android Studio's debugger
- For network inspection: Use the Network tab in Chrome DevTools when debugging

### Common Issues

1. iOS build fails:
   - Clean the build folder in Xcode (Product > Clean Build Folder)
   - Delete derived data: `rm -rf ~/Library/Developer/Xcode/DerivedData`
   - Re-run `pod install`

2. Android build fails:
   - Clean the project: `cd android && ./gradlew clean`
   - Clear Android build cache: `cd android && ./gradlew cleanBuildCache`

3. Metro bundler issues:
   - Clear Metro cache: `npm start -- --reset-cache`
   - Ensure Metro is running on the correct port

### Environment Variables

For local development, create the appropriate .env file:

- `.env.staging` for staging environment
- `.env.production` for production environment

Contact the development team for the correct environment variable values.

### Screen Tracking Analytics

The app uses screen tracking analytics to monitor user interactions and app state. To add screen tracking to a new screen:

1. Import required hooks:
```typescript
import { useAnalytics } from '@/context/AnalyticsContext';
import { useIsFocused } from '@react-navigation/native';
```

2. Initialize hooks in your component:
```typescript
const { setCurrentScreen } = useAnalytics();
const isFocused = useIsFocused();
```

3. Implement screen tracking:
```typescript
useEffect(() => {
  if (isFocused) {
    setCurrentScreen('ScreenName', {
      // Add relevant screen state and metrics
      someMetric: value,
      someState: Boolean(stateValue),
      // ... other tracking parameters
    });
  }
}, [isFocused, /* other dependencies */]);
```

Current screens with analytics implementation:
- Support: Tracks form completion, validation status, and error states
- AddBillingAddress: Monitors address form fields, primary status, and submission flow
- Notifications: Tracks notification counts, deletion status, and refresh states

## Useful Tools

### SVG converter

Use the following link to help convert svg files from design into SVG components
that can be used with react-native-svg

<https://react-svgr.com/playground/?native=true&typescript=true>
