module.exports = {
  env: {
    'jest/globals': true,
  },
  root: true,
  extends: [
    '@react-native',
    'airbnb',
    'eslint:recommended',
    'airbnb/hooks',
    'airbnb-typescript',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/recommended-requiring-type-checking',
    'plugin:prettier/recommended',
    'plugin:import/recommended',
    'plugin:jest/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:@cspell/recommended',
  ],
  parser: '@typescript-eslint/parser',
  ignorePatterns: [
    '.eslintrc.js', // Ignore the ESLint config file itself
    'plugins/**/*',
    'metro.config.js',
    'react-native.config.js',
    'tests/**/*',
  ],
  plugins: ['react'],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 'latest',
    sourceType: 'module',
    tsconfigRootDir: '.',
    project: ['./tsconfig.json'],
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.ts', '.tsx'],
      },
      typescript: {},
    },
    react: {
      version: '18.x',
    },
  },
  rules: {
    'react/jsx-uses-react': 'error',
    'react/jsx-uses-vars': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'global-require': 0,
    'react-hooks/exhaustive-deps': 'off',
    quotes: ['error', 'single'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'react/require-default-props': ['off'],
    'react/default-props-match-prop-types': ['error'],
    'react/sort-prop-types': ['error'],
    'react/no-array-index-key': 'off',
    'no-tabs': 'off',
    'no-void': 'off',
    'react/jsx-props-no-spreading': 'off',
    'import/prefer-default-export': 'off',
    'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
    'react/display-name': 'off',
    'prettier/prettier': [
      'error',
      {
        printWidth: 80,
        endOfLine: 'lf',
        tabWidth: 2,
        indentStyle: 'space',
        useTabs: false,
        arrowParens: 'avoid',
        bracketSameLine: false,
        singleQuote: true,
        trailingComma: 'all',
      },
    ],
  },
};
